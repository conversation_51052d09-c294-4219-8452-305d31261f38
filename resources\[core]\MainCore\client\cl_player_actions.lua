-- Variable pour suivre si la mort est due à la commande /suicide
_G.isSuicideCommand = false
_G.allowTeamChangeOnNextDeath = false -- Flag pour le changement d'équipe à la prochaine mort

RegisterCommand('suicide', function(source, args, rawCommand)
    _G.isSuicideCommand = true
    SetEntityHealth(PlayerPedId(), 0)
    -- Le flag sera réinitialisé dans le gestionnaire d'événements de mort (par exemple, cl_faction_select.lua)
    -- ou après un court délai pour s'assurer qu'il est lu correctement.
    -- Nous ajoutons un Citizen.Wait(0) pour permettre au script de mort de s'exécuter avant une éventuelle réinitialisation précoce.
    Citizen.Wait(0) 
end, false)

-- Il est préférable de réinitialiser le flag dans le script qui gère la mort (cl_faction_select.lua)
-- après avoir vérifié sa valeur.
-- La boucle de sécurité ci-dessous est une précaution supplémentaire au cas où la réinitialisation
-- dans cl_faction_select.lua ne se produirait pas (par exemple, si le joueur ne meurt pas réellement).
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Vérifier toutes les secondes
        if _G.isSuicideCommand and GetEntityHealth(PlayerPedId()) > 0 then
            -- Si le joueur n'est pas mort (ou a respawn sans que le flag soit réinitialisé)
            -- et que le flag est toujours actif, réinitialiser.
            -- Cela évite que le flag reste actif indéfiniment si la mort n'est pas traitée comme prévu.
            -- print("Réinitialisation de sécurité du flag isSuicideCommand car le joueur est vivant.")
            _G.isSuicideCommand = false
        end
    end
end)

-- Gestion de l'appui sur F4 pour activer le changement d'équipe à la prochaine mort
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if IsControlJustReleased(0, 115) then -- 115 est le keycode pour F4 dans FiveM (vérifier si c'est le bon pour RedM ou utiliser RegisterKeyMapping)
            _G.allowTeamChangeOnNextDeath = true
            -- Remplacer par votre fonction de notification si disponible
            -- Par exemple: exports.MainCore:ShowNotification("Changement d'équipe activé à la prochaine mort.")
            print("Changement d'équipe activé à la prochaine mort. (Notification à implémenter)")
        end
    end
end)

-- Alternative avec RegisterKeyMapping (plus propre si disponible et configuré)
-- RegisterKeyMapping('toggleTeamChangeOnDeath', 'Activer/Désactiver changement d'équipe à la mort', 'keyboard', 'F4')
-- RegisterCommand('toggleTeamChangeOnDeath', function()
--  _G.allowTeamChangeOnNextDeath = not _G.allowTeamChangeOnNextDeath
--  if _G.allowTeamChangeOnNextDeath then
--      -- exports.MainCore:ShowNotification("Changement d'équipe activé à la prochaine mort.")
--      print("Changement d'équipe activé à la prochaine mort. (Notification à implémenter)")
--  else
--      -- exports.MainCore:ShowNotification("Changement d'équipe désactivé.")
--      print("Changement d'équipe désactivé. (Notification à implémenter)")
--  end
-- end, false)