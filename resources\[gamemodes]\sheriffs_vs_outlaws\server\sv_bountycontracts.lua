-- sv_bountycontracts.lua
-- Manages bounty contracts for Sheriff<PERSON>

-- Table to store available bounty contracts
-- Format: { targetSource = { bounty = playerBounty, name = playerName, lastSeen = "Unknown" }, ... }
AvailableBountyContracts = {}

-- Table to store active bounty contracts taken by Sheriffs
-- Format: { sheriffSource = { targetSource = targetSource, targetName = playerName, startTime = os.time() }, ... }
ActiveBountyContracts = {}

-- Function to periodically scan for eligible outlaw players
function ScanForEligibleOutlaws()
    local players = GetPlayers()
    local newAvailableContracts = {}

    for _, playerId in ipairs(players) do
        local playerSource = playerId
        -- TODO: Implement a way to check if player is an Outlaw and get their bounty
        -- For now, let's assume a function GetPlayerBounty(playerSource) exists
        -- and IsPlayerOutlaw(playerSource) exists
        local playerBounty = GetPlayerBounty(playerSource) -- Placeholder
        local isOutlaw = IsPlayerOutlaw(playerSource) -- Placeholder

        if isOutlaw and playerBounty and playerBounty >= Config.BountyContractPlayerMinBounty then
            local isAlreadyTarget = false
            for _, contract in pairs(ActiveBountyContracts) do
                if contract.targetSource == playerSource then
                    isAlreadyTarget = true
                    break
                end
            end

            if not isAlreadyTarget then
                -- Check if already in available contracts to avoid duplicate entries if no one takes it
                local alreadyAvailable = false
                for existingTargetSource, _ in pairs(AvailableBountyContracts) do
                    if existingTargetSource == playerSource then
                        alreadyAvailable = true
                        break
                    end
                end
                if not alreadyAvailable then
                     newAvailableContracts[playerSource] = {
                        bounty = playerBounty,
                        name = GetPlayerName(playerSource),
                        -- lastSeen = GetEntityCoords(GetPlayerPed(playerSource)) -- Consider privacy/gameplay balance
                        lastSeen = "Last known area" -- Placeholder for now
                    }
                end
            end
        end
    end
    AvailableBountyContracts = newAvailableContracts
    -- TODO: Notify clients if the list of available bounties has changed, or they will request it.
    -- print("[BountyContracts] Scanned for outlaws. Found " .. tablelength(AvailableBountyContracts) .. " potential targets.")
end

-- Periodically scan for outlaws (e.g., every 5 minutes)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5 * 60 * 1000) -- 5 minutes
        ScanForEligibleOutlaws()
    end
end)

-- Event handler for when a Sheriff requests the list of available bounties
RegisterNetEvent('sheriffs_vs_outlaws:requestBountyList')
AddEventHandler('sheriffs_vs_outlaws:requestBountyList', function()
    local source = source
    -- TODO: Check if player is a Sheriff
    -- For now, assume IsPlayerSheriff(source) exists
    if IsPlayerSheriff(source) then -- Placeholder
        TriggerClientEvent('sheriffs_vs_outlaws:receiveBountyList', source, AvailableBountyContracts)
    end
end)

-- Event handler for when a Sheriff tries to take a bounty contract
RegisterNetEvent('sheriffs_vs_outlaws:takeBountyContract')
AddEventHandler('sheriffs_vs_outlaws:takeBountyContract', function(targetSource)
    local sheriffSource = source
    -- Validate input from client
    if type(targetSource) ~= 'number' then
        print(string.format("[BountyContracts] Invalid targetSource type received from %s. Expected number, got %s.", sheriffSource, type(targetSource)))
        return
    end
    local sheriffPlayerId = GetPlayerIdentifier(sheriffSource, 0) -- Get a unique ID for the sheriff

    -- TODO: Check if player is a Sheriff
    if not IsPlayerSheriff(sheriffSource) then -- Placeholder
        TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "Only Sheriffs can take bounty contracts." } })
        return
    end

    if ActiveBountyContracts[sheriffSource] then
        TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "You already have an active bounty contract." } })
        return
    end

    local targetContract = AvailableBountyContracts[targetSource]
    if not targetContract then
        TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "This bounty is no longer available or the target is invalid." } })
        return
    end

    -- Check if target is already being tracked by another sheriff via this system
    for _, activeContract in pairs(ActiveBountyContracts) do
        if activeContract.targetSource == targetSource then
            TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "This target is already being tracked by another Sheriff." } })
            return
        end
    end

    ActiveBountyContracts[sheriffSource] = {
        targetSource = targetSource,
        targetName = targetContract.name,
        targetBounty = targetContract.bounty,
        startTime = GetGameTimer() -- Using GetGameTimer for server time reference
    }

    -- Remove from available contracts as it's now taken
    AvailableBountyContracts[targetSource] = nil

    TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "Contract taken! Target: " .. targetContract.name .. " (Bounty: $" .. targetContract.bounty .. ")" } })
    -- TODO: Notify client with more details, maybe a blip
    TriggerClientEvent('sheriffs_vs_outlaws:bountyContractStarted', sheriffSource, targetContract.name, targetContract.bounty)

    print("[JOURNAL] Sheriff " .. GetPlayerName(sheriffSource) .. " has taken a bounty contract on " .. targetContract.name)
end)

-- Event handler for when a Sheriff cancels their bounty contract
RegisterNetEvent('sheriffs_vs_outlaws:cancelBountyContract')
AddEventHandler('sheriffs_vs_outlaws:cancelBountyContract', function()
    local sheriffSource = source

    if ActiveBountyContracts[sheriffSource] then
        local contract = ActiveBountyContracts[sheriffSource]
        local targetSource = contract.targetSource

        -- Make the bounty available again if the target is still eligible
        -- We need to re-fetch bounty and outlaw status as it might have changed
        local playerBounty = GetPlayerBounty(targetSource) -- Placeholder
        local isOutlaw = IsPlayerOutlaw(targetSource) -- Placeholder

        if targetSource and GetPlayerName(targetSource) and isOutlaw and playerBounty and playerBounty >= Config.BountyContractPlayerMinBounty then
            AvailableBountyContracts[targetSource] = {
                bounty = playerBounty,
                name = GetPlayerName(targetSource),
                lastSeen = "Last known area" -- Placeholder
            }
        end

        ActiveBountyContracts[sheriffSource] = nil
        TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "Your bounty contract has been cancelled." } })
        TriggerClientEvent('sheriffs_vs_outlaws:bountyContractCancelled', sheriffSource)
        print("[JOURNAL] Sheriff " .. GetPlayerName(sheriffSource) .. " cancelled their bounty contract on " .. contract.targetName)
    else
        TriggerClientEvent('chat:addMessage', sheriffSource, { args = { "Bounty System", "You do not have an active bounty contract to cancel." } })
    end
end)

-- Function to be called from sv_arrest.lua when a contract target is arrested
-- Needs to be exported or called via an event
function NotifyBountyContractSystemOfArrest(arrestingSheriffSource, arrestedPlayerSource)
    local contract = ActiveBountyContracts[arrestingSheriffSource]

    if contract and contract.targetSource == arrestedPlayerSource then
        local sheriffName = GetPlayerName(arrestingSheriffSource)
        local targetName = contract.targetName

        -- Grant rewards
        -- Grant rewards via MainCore
        exports.MainCore:UpdatePlayerMoney(arrestingSheriffSource, Config.BountyContractRewardMoney or 0)
        exports.MainCore:AddPlayerXP(arrestingSheriffSource, Config.BountyContractRewardXP or 0)

        TriggerClientEvent('chat:addMessage', arrestingSheriffSource, { args = { "Bounty System", "Contract complete! You arrested " .. targetName .. ". Reward: $" .. (Config.BountyContractRewardMoney or 0) .. " and " .. (Config.BountyContractRewardXP or 0) .. " XP." } })
        print("[JOURNAL] Sheriff " .. sheriffName .. " completed bounty contract on " .. targetName .. ", collected $" .. Config.BountyContractRewardMoney .. " and " .. Config.BountyContractRewardXP .. " XP.")

        -- Log to server journal
        exports['sheriffs_vs_outlaws']:AddJournalEntry("Le Shérif " .. sheriffName .. " a ramené le hors-la-loi " .. targetName .. " et collecté la prime !")

        ActiveBountyContracts[arrestingSheriffSource] = nil
        TriggerClientEvent('sheriffs_vs_outlaws:bountyContractCompleted', arrestingSheriffSource, targetName)

        -- The target's bounty itself is handled by the arrest system.
        return true -- Indicates contract was relevant
    end
    return false -- Arrest was not related to an active bounty for this sheriff
end

-- Use MainCore functions
function GetPlayerBounty(playerSource)
    return exports.MainCore:GetPlayerBounty(playerSource) or 0
end

function IsPlayerOutlaw(playerSource)
    return exports.MainCore:GetPlayerFaction(playerSource) == "outlaws"
end

function IsPlayerSheriff(playerSource)
    return exports.MainCore:GetPlayerFaction(playerSource) == "sheriffs"
end

-- Helper function to get table length (Lua 5.1 doesn't have # for non-sequence tables)
function tablelength(T)
    local count = 0
    for _ in pairs(T) do count = count + 1 end
    return count
end

print("[BountyContracts] sv_bountycontracts.lua loaded.")

-- Exports for sv_arrest.lua to call
exports('NotifyBountyContractSystemOfArrest', NotifyBountyContractSystemOfArrest)