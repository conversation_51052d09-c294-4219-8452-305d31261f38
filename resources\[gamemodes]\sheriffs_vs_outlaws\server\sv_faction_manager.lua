-- Événement pour récupérer la faction du joueur
RegisterNetEvent('sheriffs_vs_outlaws:server:getPlayerFaction', function()
    local src = source
    local playerFaction = nil
    
    -- Récupérer la faction du joueur depuis la base de données ou le cache
    -- TODO: Implémenter la récupération de la faction depuis la base de données
    -- Pour l'instant, on utilise une valeur de test
    playerFaction = "sheriffs" -- ou "outlaws" selon la faction du joueur
    
    -- Envoyer la faction au client
    TriggerClientEvent('sheriffs_vs_outlaws:client:receivePlayerFaction', src, playerFaction)
end) 