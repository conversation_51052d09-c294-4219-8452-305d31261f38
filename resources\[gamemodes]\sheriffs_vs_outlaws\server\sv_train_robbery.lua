local lastTrainRobberyTime = 0
local trainRobberies = {} -- To keep track of active train robberies { trainId = {robbers = {playerId, ...}, startTime = os.time()} }

-- Function to check if a player is an Outlaw
local function isPlayerOutlaw(playerId)
    local faction = exports.MainCore:GetPlayerFaction(playerId)
    return faction == "outlaws"
end

RegisterNetEvent('sheriffs_vs_outlaws:startTrainRobbery')
AddEventHandler('sheriffs_vs_outlaws:startTrainRobbery', function()
    local src = source
    local playerPed = GetPlayerPed(src)
    local vehicle = GetVehiclePedIsUsing(playerPed)

    if not isPlayerOutlaw(src) then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Seuls les Hors-la-loi peuvent braquer les trains.", 5000)
        return
    end

    if vehicle == 0 or not IsThisModelATrain(GetEntityModel(vehicle)) then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vous devez être à bord d'un train pour initier un braquage.", 5000)
        return
    end

    local currentTime = GetGameTimer() -- Using GetGameTimer for more precision with server ticks
    if (currentTime - lastTrainRobberyTime) < Config.TrainRobberyCooldown then
        local remainingTime = math.ceil((Config.TrainRobberyCooldown - (currentTime - lastTrainRobberyTime)) / 1000)
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Un autre braquage de train a eu lieu récemment. Attendez " .. remainingTime .. " secondes.", 5000)
        return
    end

    local trainNetId = VehToNet(vehicle)
    if trainRobberies[trainNetId] then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Ce train est déjà en cours de braquage.", 5000)
        return
    end

    print("Starting train robbery for train " .. trainNetId .. " by player " .. src)
    trainRobberies[trainNetId] = {
        robbers = { [src] = true },
        startTime = currentTime,
        trainEntity = vehicle -- Store the entity for later checks
    }
    lastTrainRobberyTime = currentTime -- Set global cooldown start

    -- Notify player robbery started
    TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Le braquage du train a commencé ! Restez à bord et en vie.", 7000)

    -- Start timer for the robbery process (e.g., 60 seconds)
    local robberyDuration = 60000 -- 60 seconds, make this configurable later if needed
    local alertDelay = Config.TrainRobberyAlertLawTime

    -- Alert law enforcement after a delay
    SetTimeout(alertDelay, function()
        if trainRobberies[trainNetId] then -- Check if robbery is still active
            local trainCoords = GetEntityCoords(trainRobberies[trainNetId].trainEntity)
            local locationName = "sur la ligne principale" -- Placeholder, improve with actual location later
            -- TODO: Get a better location name based on trainCoords if possible

            print("Alerting law enforcement about train robbery at: " .. locationName)
            -- Trigger event for all sheriffs
            local sheriffs = exports.MainCore:GetPlayersByFaction("sheriffs")
            for playerId_str, _ in pairs(sheriffs) do
                local playerId = tonumber(playerId_str)
                TriggerClientEvent('sheriffs_vs_outlaws:showNotification', playerId, "ALERTE : Un train est attaqué " .. locationName .. "!", 10000)
                    -- TriggerClientEvent('sheriffs_vs_outlaws:notifyTrainRobberyInProgress', playerId, trainCoords) -- For blip or map marker
                end
            end
    end)

    -- Robbery success timer
    SetTimeout(robberyDuration, function()
        if trainRobberies[trainNetId] then
            local stillOnTrainAndAlive = false
            local involvedOutlaws = {}

            for robberSrc, _ in pairs(trainRobberies[trainNetId].robbers) do
                local robberPed = GetPlayerPed(robberSrc)
                if robberPed and DoesEntityExist(robberPed) and not IsEntityDead(robberPed) then
                    local currentVehicle = GetVehiclePedIsUsing(robberPed)
                    if currentVehicle == trainRobberies[trainNetId].trainEntity then
                        stillOnTrainAndAlive = true
                        table.insert(involvedOutlaws, robberSrc)
                    else
                        -- Player left the train
                        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', robberSrc, "Vous avez quitté le train. Braquage annulé pour vous.", 5000)
                    end
                else
                    -- Player died or disconnected
                    TriggerClientEvent('sheriffs_vs_outlaws:showNotification', robberSrc, "Vous n'êtes plus en état de continuer le braquage.", 5000)
                end
            end

            if stillOnTrainAndAlive and #involvedOutlaws > 0 then
                print("Train robbery successful for train " .. trainNetId)
                local reward = math.random(Config.TrainRobberyRewardMin, Config.TrainRobberyRewardMax)
                local xp = Config.TrainRobberyXP
                local bountyIncrease = Config.TrainRobberyBountyIncrease

                for _, outLawSrc in ipairs(involvedOutlaws) do
                    exports.MainCore:UpdatePlayerDirtyMoney(outLawSrc, reward) -- Argent sale
                    exports.MainCore:AddPlayerXP(outLawSrc, xp)
                    exports.MainCore:UpdatePlayerBounty(outLawSrc, bountyIncrease)

                    print("Rewarding player " .. outLawSrc .. " with " .. reward .. " (dirty money), " .. xp .. " XP, and " .. bountyIncrease .. " bounty.")
                    TriggerClientEvent('sheriffs_vs_outlaws:showNotification', outLawSrc, "Braquage réussi ! Vous avez obtenu $" .. reward .. " (argent sale), " .. xp .. " XP et votre prime a augmenté.", 7000)

                    -- Log to server journal
                    local playerName = GetPlayerName(outLawSrc)
                    exports['sheriffs_vs_outlaws']:AddJournalEntry(playerName .. " a audacieusement dévalisé un train, empochant $" .. reward .. " d'argent sale !")
                    print(playerName .. " successfully robbed a train for $" .. reward .. " (dirty money)")
                end

            else
                print("Train robbery failed for train " .. trainNetId .. ". No outlaws on train or alive.")
                for robberSrc, _ in pairs(trainRobberies[trainNetId].robbers) do
                    TriggerClientEvent('sheriffs_vs_outlaws:showNotification', robberSrc, "Le braquage du train a échoué.", 5000)
                end
            end

            trainRobberies[trainNetId] = nil -- Clear the robbery state for this train
            -- Global cooldown was already set at the start of the robbery.
        end
    end)
end)

-- TODO: Add logic to handle player disconnects or deaths during the robbery.
-- If a player involved in trainRobberies[trainNetId].robbers disconnects or dies,
-- they should be removed from the table. If all robbers are gone, the robbery fails.
-- This could be handled via playerDropped event and an OnPlayerDeath event.

AddEventHandler('playerDropped', function(reason)
    local src = source
    for trainNetId, robberyData in pairs(trainRobberies) do
        if robberyData.robbers[src] then
            robberyData.robbers[src] = nil
            print("Player " .. src .. " disconnected during train robbery on train " .. trainNetId)
            
            local remainingRobbers = 0
            for _, _ in pairs(robberyData.robbers) do
                remainingRobbers = remainingRobbers + 1
            end

            if remainingRobbers == 0 then
                print("Train robbery on " .. trainNetId .. " failed due to all robbers disconnecting.")
                -- Potentially notify other players or log this specific failure type
                trainRobberies[trainNetId] = nil -- Clear the robbery
            end
            break -- Player can only be in one robbery
        end
    end
end)

-- Gestion de la mort des joueurs impliqués dans le braquage de train
AddEventHandler('MainCore:playerDied', function(deadPlayerId, killerId, killerType, deathCoords)
    local src = deadPlayerId
    for trainNetId, robberyData in pairs(trainRobberies) do
        if robberyData.robbers[src] then
            robberyData.robbers[src] = nil -- Marquer comme non participant ou mort
            print("Player " .. src .. " died during train robbery on train " .. trainNetId)
            TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vous êtes mort. Le braquage du train a échoué pour vous.", 5000)

            local remainingRobbersActive = 0
            for robber, isActive in pairs(robberyData.robbers) do
                if isActive then -- Vérifier si le braqueur est toujours considéré comme actif
                    local robberPed = GetPlayerPed(robber)
                    if robberPed and DoesEntityExist(robberPed) and not IsEntityDead(robberPed) then
                        local currentVehicle = GetVehiclePedIsUsing(robberPed)
                        if currentVehicle == robberyData.trainEntity then
                           remainingRobbersActive = remainingRobbersActive + 1
                        end
                    end
                end
            end

            if remainingRobbersActive == 0 then
                print("Train robbery on " .. trainNetId .. " failed due to all active robbers dying or leaving.")
                -- Le timer de succès à la ligne 74 gérera la notification d'échec si aucun braqueur valide n'est trouvé.
                -- On pourrait aussi explicitement annuler ici si nécessaire, mais la logique existante devrait couvrir.
                -- trainRobberies[trainNetId] = nil -- Attention, cela pourrait interférer avec le SetTimeout de succès/échec.
                -- Il est préférable de laisser le SetTimeout gérer la finalisation.
            end
            break -- Le joueur ne peut être que dans un braquage de train à la fois
        end
    end
end)

print("sv_train_robbery.lua loaded.")