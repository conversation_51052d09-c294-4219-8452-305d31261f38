-- cl_bountycontracts.lua
-- Client-side logic for bounty contracts

local currentBounties = {}
local hasActiveContract = false
local activeContractTargetName = ""

-- Command to list available bounties
RegisterCommand('bounties', function(source, args, rawCommand)
    -- TODO: Check if player is a Sheriff (client-side check for UI, server will validate)
    -- For now, assume a client-side function IsPlayerSheriffLocal() might exist or we rely on server denial.
    print("[Bounty Client] Requesting bounty list...")
    TriggerServerEvent('sheriffs_vs_outlaws:requestBountyList')
end, false)

-- Event to receive the bounty list from the server
RegisterNetEvent('sheriffs_vs_outlaws:receiveBountyList')
AddEventHandler('sheriffs_vs_outlaws:receiveBountyList', function(bountyList)
    currentBounties = bountyList
    if tablelength(currentBounties) == 0 then
        TriggerEvent('chat:addMessage', { args = { "Bounty Board", "No bounty contracts currently available." } })
        return
    end

    TriggerEvent('chat:addMessage', { args = { "Bounty Board", "Available Bounty Contracts:" } })
    for targetSource, contract in pairs(currentBounties) do
        -- The targetSource is the server ID of the player.
        -- We need a way to display this meaningfully or use it in /takebounty
        TriggerEvent('chat:addMessage', { args = { "  - Target ID: " .. targetSource .. " | Name: " .. contract.name .. " | Bounty: $" .. contract.bounty .. " | Last Seen: " .. contract.lastSeen } })
    end
    TriggerEvent('chat:addMessage', { args = { "Bounty Board", "Use /takebounty <Target ID> to accept a contract." } })
end)

-- Command to take a bounty contract
RegisterCommand('takebounty', function(source, args, rawCommand)
    if hasActiveContract then
        TriggerEvent('chat:addMessage', { args = { "Bounty System", "You already have an active contract on " .. activeContractTargetName .. "." } })
        return
    end

    local targetId = tonumber(args[1])
    if not targetId then
        TriggerEvent('chat:addMessage', { args = { "Bounty System", "Usage: /takebounty <Target ID>" } })
        return
    end

    -- Check if the targetId is in the currentBounties list (basic client check)
    if not currentBounties[targetId] then
        TriggerEvent('chat:addMessage', { args = { "Bounty System", "Invalid Target ID or bounty not listed. Use /bounties to refresh." } })
        return
    end

    print("[Bounty Client] Attempting to take bounty on target ID: " .. targetId)
    TriggerServerEvent('sheriffs_vs_outlaws:takeBountyContract', targetId)
end, false)

-- Command to cancel an active bounty contract
RegisterCommand('cancelbounty', function(source, args, rawCommand)
    if not hasActiveContract then
        TriggerEvent('chat:addMessage', { args = { "Bounty System", "You do not have an active bounty contract to cancel." } })
        return
    end
    TriggerServerEvent('sheriffs_vs_outlaws:cancelBountyContract')
end, false)

-- Event when a bounty contract is successfully started
RegisterNetEvent('sheriffs_vs_outlaws:bountyContractStarted')
AddEventHandler('sheriffs_vs_outlaws:bountyContractStarted', function(targetName, targetBounty)
    hasActiveContract = true
    activeContractTargetName = targetName
    TriggerEvent('chat:addMessage', { args = { "Bounty System", "Contract active! Your target is " .. targetName .. ". Bring them in!" } })
    -- TODO: Add blip or other tracking UI elements if desired
    -- For example: CreateBlipForTarget(targetServerId) -- Need target's server ID or coords
end)

-- Event when a bounty contract is completed
RegisterNetEvent('sheriffs_vs_outlaws:bountyContractCompleted')
AddEventHandler('sheriffs_vs_outlaws:bountyContractCompleted', function(targetName)
    hasActiveContract = false
    activeContractTargetName = ""
    TriggerEvent('chat:addMessage', { args = { "Bounty System", "Congratulations! You have successfully brought in " .. targetName .. "." } })
    -- TODO: Remove blip or other tracking UI elements
end)

-- Event when a bounty contract is cancelled (by player or system)
RegisterNetEvent('sheriffs_vs_outlaws:bountyContractCancelled')
AddEventHandler('sheriffs_vs_outlaws:bountyContractCancelled', function()
    hasActiveContract = false
    activeContractTargetName = ""
    TriggerEvent('chat:addMessage', { args = { "Bounty System", "Your bounty contract has been cancelled." } })
    -- TODO: Remove blip or other tracking UI elements
end)


-- Helper function to get table length (Lua 5.1 doesn't have # for non-sequence tables)
function tablelength(T)
    local count = 0
    for _ in pairs(T) do count = count + 1 end
    return count
end

-- TODO: Implement Bounty Board interaction points if physical boards are desired
-- This would involve:
-- 1. Defining locations in config.lua
-- 2. Creating markers/peds at these locations
-- 3. Checking for player interaction (e.g., key press when near)
-- 4. On interaction, trigger the 'sheriffs_vs_outlaws:requestBountyList' event or open a UI

Citizen.CreateThread(function()
    -- Example of how you might set up a bounty board interaction point
    -- This is conceptual and needs actual implementation with markers/prompts
    -- if Config.BountyBoards and #Config.BountyBoards > 0 then
    --     for _, board in ipairs(Config.BountyBoards) do
    --         -- Create a marker or ped at board.x, board.y, board.z
    --         -- When player interacts (e.g. presses E):
    --         -- TriggerServerEvent('sheriffs_vs_outlaws:requestBountyList')
    --         -- Or, open a NUI interface to display bounties
    --     end
    -- end
end)

print("[BountyContracts] cl_bountycontracts.lua loaded.")