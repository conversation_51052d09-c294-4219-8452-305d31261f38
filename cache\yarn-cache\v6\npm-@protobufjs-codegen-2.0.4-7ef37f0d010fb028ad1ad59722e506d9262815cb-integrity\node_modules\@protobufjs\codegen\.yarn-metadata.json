{"manifest": {"name": "@protobufjs/codegen", "description": "A minimalistic code generation utility.", "version": "2.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "_registry": "npm", "_loc": "D:\\Redm\\txData\\RedMBasicServerCFXDefault_1DC7C9.base\\cache\\yarn-cache\\v6\\npm-@protobufjs-codegen-2.0.4-7ef37f0d010fb028ad1ad59722e506d9262815cb-integrity\\node_modules\\@protobufjs\\codegen\\package.json", "readmeFilename": "README.md", "readme": "@protobufjs/codegen\n===================\n[![npm](https://img.shields.io/npm/v/@protobufjs/codegen.svg)](https://www.npmjs.com/package/@protobufjs/codegen)\n\nA minimalistic code generation utility.\n\nAPI\n---\n\n* **codegen([functionParams: `string[]`], [functionName: string]): `Codegen`**<br />\n  Begins generating a function.\n\n* **codegen.verbose = `false`**<br />\n  When set to true, codegen will log generated code to console. Useful for debugging.\n\nInvoking **codegen** returns an appender function that appends code to the function's body and returns itself:\n\n* **Codegen(formatString: `string`, [...formatParams: `any`]): Codegen**<br />\n  Appends code to the function's body. The format string can contain placeholders specifying the types of inserted format parameters:\n\n  * `%d`: Number (integer or floating point value)\n  * `%f`: Floating point value\n  * `%i`: Integer value\n  * `%j`: JSON.stringify'ed value\n  * `%s`: String value\n  * `%%`: Percent sign<br />\n\n* **Codegen([scope: `Object.<string,*>`]): `Function`**<br />\n  Finishes the function and returns it.\n\n* **Codegen.toString([functionNameOverride: `string`]): `string`**<br />\n  Returns the function as a string.\n\nExample\n-------\n\n```js\nvar codegen = require(\"@protobufjs/codegen\");\n\nvar add = codegen([\"a\", \"b\"], \"add\") // A function with parameters \"a\" and \"b\" named \"add\"\n  (\"// awesome comment\")             // adds the line to the function's body\n  (\"return a + b - c + %d\", 1)       // replaces %d with 1 and adds the line to the body\n  ({ c: 1 });                        // adds \"c\" with a value of 1 to the function's scope\n\nconsole.log(add.toString()); // function add(a, b) { return a + b - c + 1 }\nconsole.log(add(1, 2));      // calculates 1 + 2 - 1 + 1 = 3\n```\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "Copyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@protobufjs/codegen/-/codegen-2.0.4.tgz#7ef37f0d010fb028ad1ad59722e506d9262815cb", "type": "tarball", "reference": "https://registry.yarnpkg.com/@protobufjs/codegen/-/codegen-2.0.4.tgz", "hash": "7ef37f0d010fb028ad1ad59722e506d9262815cb", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==", "registry": "npm", "packageName": "@protobufjs/codegen", "cacheIntegrity": "sha512-Yy<PERSON>aikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg== sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs="}, "registry": "npm", "hash": "7ef37f0d010fb028ad1ad59722e506d9262815cb"}