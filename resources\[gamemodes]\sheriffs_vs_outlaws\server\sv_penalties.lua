-- sv_penalties.lua
-- G<PERSON> les pénalités à la mort du joueur

-- S'assure que le code s'exécute côté serveur
if IsDuplicityVersion() then

    -- Fonction pour notifier le joueur (côté client)
    function NotifyPlayer(playerId, message)
        TriggerClientEvent("chat:addMessage", playerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Système", message}
        })
        -- Alternativement, utiliser une notification personnalisée si disponible
        -- TriggerClientEvent('vRP:notify', playerId, message)
        -- TriggerClientEvent('esx:showNotification', playerId, message)
    end

    -- Fonction pour gérer la perte d'argent
    function HandleMoneyPenalty(playerId, percentage, reason)
        local currentMoney = exports.MainCore:GetPlayerMoney(playerId)
        if not currentMoney or currentMoney == 0 then
            NotifyPlayer(playerId, "Vous n'aviez pas d'argent à perdre.")
            return
        end

        local amountToLose = math.floor(currentMoney * (percentage / 100))
        if amountToLose > 0 then
            exports.MainCore:UpdatePlayerMoney(playerId, -amountToLose)
            NotifyPlayer(playerId, string.format("Vous avez perdu %d$ suite à %s.", amountToLose, reason))
        else
            NotifyPlayer(playerId, "Vous n'avez perdu aucun argent.")
        end
    end

    -- Fonction pour gérer la perte d'armes
    function HandleWeaponLoss(playerId, loseWeaponsConfig, protectedWeaponsConfig, reason)
        if not loseWeaponsConfig then
            return
        end

        local playerPed = GetPlayerPed(playerId)
        local lostWeapons = {}

        -- Récupérer les armes du grade du joueur pour les protéger via MainCore
        local playerFaction = exports.MainCore:GetPlayerFaction(playerId)
        local playerRankName = exports.MainCore:GetPlayerRank(playerId) -- MainCore retourne le nom du rang
        local rankProtectedWeapons = {}

        -- La structure de Config.Factions (qui devrait être Factions de MainCore maintenant)
        -- et comment les rangs sont stockés/comparés est cruciale ici.
        -- MainCore.Factions stocke les grades avec des noms. Il faut trouver le bon grade par son nom.
        -- On va supposer que Config.Factions ici fait référence à la table Factions définie dans MainCore.
        -- Pour plus de propreté, MainCore pourrait exporter une fonction GetRankRewards(playerId)
        if playerFaction and playerRankName and Factions and Factions[playerFaction] and Factions[playerFaction].grades then
            for _, gradeData in ipairs(Factions[playerFaction].grades) do
                if gradeData.name == playerRankName then
                    if gradeData.rewards and gradeData.rewards.weapons then
                        for _, weaponName in ipairs(gradeData.rewards.weapons) do
                            table.insert(rankProtectedWeapons, GetHashKey(weaponName))
                        end
                    end
                    break -- Grade trouvé
                end
            end
        end

        -- Créer une table de recherche pour les armes protégées (config + grade)
        local allProtectedWeaponsLookup = {}
        for _, weaponNameOrHash in ipairs(protectedWeaponsConfig) do
            allProtectedWeaponsLookup[GetHashKey(weaponNameOrHash)] = true -- Convertit les noms en hash si nécessaire
        end
        for _, weaponHash in ipairs(rankProtectedWeapons) do
            allProtectedWeaponsLookup[weaponHash] = true
        end
        allProtectedWeaponsLookup[GetHashKey("WEAPON_UNARMED")] = true -- Toujours protéger les poings

        -- Itérer sur les armes du joueur (nécessite une méthode pour obtenir les armes actuelles du joueur)
        -- Ceci est un exemple et pourrait nécessiter une adaptation basée sur comment les armes sont gérées.
        -- Une approche commune est de parcourir une liste d'armes connues ou d'utiliser des natives si disponibles et fiables.
        -- Pour RedM, les natives de gestion d'inventaire d'armes peuvent être limitées.
        -- On suppose ici qu'on a une fonction GetPlayerWeapons(playerId) qui retourne une table de hashes d'armes.
        -- Si ce n'est pas le cas, il faudra l'implémenter ou la trouver dans votre base de code.

        -- Exemple simplifié: on va supposer que l'on doit explicitement retirer toutes les armes sauf les protégées.
        -- La native `RemoveAllPedWeapons` existe mais elle est trop agressive.
        -- Il faut une logique pour parcourir les armes que le joueur possède.
        -- La gestion de PlayersData[playerId].weapons est problématique car MainCore ne gère pas cela de base.
        -- Cette partie nécessitera une refonte si un système d'inventaire centralisé n'existe pas
        -- ou si MainCore n'est pas étendu pour stocker les armes du joueur.
        -- Pour l'instant, cette logique est commentée car elle ne fonctionnera pas.
        -- Nouvelle logique utilisant MainCore (supposant l'existence des fonctions GetPlayerWeapons et RemovePlayerWeapon)
        local playerWeapons = exports.MainCore:GetPlayerWeapons(playerId) -- Supposé retourner { hash1, hash2, ... } ou { [hash1]=ammo, ... }

        if playerWeapons then
            -- Déterminer le format de la table retournée (liste ou map)
            local isListFormat = #playerWeapons > 0 and playerWeapons[1] ~= nil

            if isListFormat then
                 -- Format: { hash1, hash2, ... }
                 for _, weaponHash in ipairs(playerWeapons) do
                    if not allProtectedWeaponsLookup[weaponHash] then
                        -- Utiliser RemoveWeaponFromPed pour l'instant, car RemovePlayerWeapon n'est pas standard
                        RemoveWeaponFromPed(playerPed, weaponHash)
                        -- TODO: Confirmer si MainCore doit aussi être mis à jour (ex: exports.MainCore:RemovePlayerWeapon(playerId, weaponHash))
                        -- Essayer d'obtenir le nom de l'arme pour la notification
                        local weaponLabel = GetLabelText(Citizen.InvokeNative(0x705993B21881652A, weaponHash, Citizen.ResultAsLong()))
                        if not weaponLabel or weaponLabel == "NULL" or weaponLabel == "" then weaponLabel = string.format("Arme (Hash: %s)", weaponHash) end
                        table.insert(lostWeapons, weaponLabel)
                        print(string.format("^2[PENALTIES] Arme %s retirée (via native) pour le joueur %s.^7", weaponLabel, playerId))
                    end
                 end
            else
                -- Format: { [hash1]=ammo, [hash2]=ammo, ... } ou autre structure clé-valeur
                for weaponHash, _ in pairs(playerWeapons) do
                    -- Assurer que le hash est un nombre
                    local numericHash = tonumber(weaponHash)
                    if numericHash and not allProtectedWeaponsLookup[numericHash] then
                         -- Utiliser RemoveWeaponFromPed pour l'instant
                         RemoveWeaponFromPed(playerPed, numericHash)
                         -- TODO: Confirmer si MainCore doit aussi être mis à jour
                         -- Essayer d'obtenir le nom de l'arme pour la notification
                         local weaponLabel = GetLabelText(Citizen.InvokeNative(0x705993B21881652A, numericHash, Citizen.ResultAsLong()))
                         if not weaponLabel or weaponLabel == "NULL" or weaponLabel == "" then weaponLabel = string.format("Arme (Hash: %s)", numericHash) end
                         table.insert(lostWeapons, weaponLabel)
                         print(string.format("^2[PENALTIES] Arme %s retirée (via native) pour le joueur %s.^7", weaponLabel, playerId))
                    end
                end
            end
        else
            print(string.format("^1[PENALTIES] Impossible de récupérer les armes du joueur %s via MainCore:GetPlayerWeapons. Aucune arme retirée.^7", playerId))
            NotifyPlayer(playerId, "Erreur: Impossible de gérer la perte d'armes (données MainCore manquantes).")
        end

        -- La logique de notification (lignes 131-135) utilisera maintenant la table lostWeapons correctement remplie.
        if #lostWeapons > 0 then
            NotifyPlayer(playerId, string.format("Vous avez perdu les armes suivantes suite à %s: %s", reason, table.concat(lostWeapons, ", ")))
        elseif loseWeaponsConfig then
             NotifyPlayer(playerId, string.format("Aucune arme n'a été perdue suite à %s.", reason))
        end
    end

    -- Événement déclenché lorsque le joueur meurt (non-arrestation), maintenant via MainCore
    AddEventHandler('MainCore:playerDied', function(playerId, killerId, killerType, deathCoords)
        -- playerId est déjà le bon ID du joueur mort fourni par MainCore:playerDied
        local reason = "votre mort"

        -- Vérifier si la mort est due à une arrestation (si un tel système existe et le marque)
        -- Pour l'instant, on suppose que toutes les morts via cet événement ne sont pas des arrestations.
        -- if IsPlayerBeingArrested(playerId) then return end -- Exemple de condition

        print(string.format("[PENALTIES] Le joueur %s (%s) est mort. Application des pénalités.", GetPlayerName(playerId), playerId))

        -- Perte d'argent
        if Config.DeathMoneyPenaltyPercent and Config.DeathMoneyPenaltyPercent > 0 then
            HandleMoneyPenalty(playerId, Config.DeathMoneyPenaltyPercent, reason)
        end

        -- Perte d'armes
        if Config.DeathLoseWeapons then
            HandleWeaponLoss(playerId, Config.DeathLoseWeapons, Config.ProtectedWeaponsOnDeath, reason)
        end
    end)

    -- Fonction exportée pour être appelée depuis sv_arrest.lua
    function ApplyArrestPenalties(playerId)
        local reason = "votre arrestation"
        print(string.format("[PENALTIES] Le joueur %s (%s) est arrêté. Application des pénalités d'arrestation.", GetPlayerName(playerId), playerId))

        -- Perte d'argent
        if Config.ArrestMoneyPenaltyPercent and Config.ArrestMoneyPenaltyPercent > 0 then
            HandleMoneyPenalty(playerId, Config.ArrestMoneyPenaltyPercent, reason)
        end

        -- Perte d'armes
        if Config.ArrestLoseWeapons then
            HandleWeaponLoss(playerId, Config.ArrestLoseWeapons, Config.ProtectedWeaponsOnDeath, reason)
        end
    end

    -- Assurez-vous que PlayersData est accessible et initialisé.
    -- Si PlayersData n'est pas global, il faudra le passer en argument ou utiliser des exports.
    -- Exemple: PlayersData = exports['player-data']:GetPlayersData()

    print("^2[PENALTIES] sv_penalties.lua chargé.^7")
end