-- sv_arrest.lua

-- Références aux configurations et données partagées
-- Config est supposé être initialisé globalement par config/locations.lua
Config.SignificantBountyThreshold = Config.SignificantBountyThreshold or 500 -- Seuil de prime pour une annonce de journal (à ajuster)
-- PlayersData n'est plus directement accessible ici, utiliser les exports de MainCore

-- Fonction pour vérifier si un joueur est un Shérif via MainCore
local function IsPlayerSheriff(playerId)
    return exports.MainCore:GetPlayerFaction(playerId) == "sheriffs" -- Note: "sheriffs" (pluriel) dans MainCore
end

-- Fonction pour vérifier si un joueur est un Hors-la-loi via MainCore
local function IsPlayerOutlaw(playerId)
    return exports.MainCore:GetPlayerFaction(playerId) == "outlaws"
end

-- Config.PrisonLocation et Config.DefaultJailTime sont supposés être dans config/locations.lua ou un fichier similaire chargé par le gamemode.
-- Config.PointsForArrest également.
-- Config.PrisonReleasePoint également.
Config.PrisonLocation = Config.PrisonLocation or {x = 0.0, y = 0.0, z = 0.0} -- Emplacement par défaut
Config.PrisonReleasePoint = Config.PrisonReleasePoint or {x = 0.0, y = 0.0, z = 0.0} -- Point de libération par défaut

-- Table pour suivre les joueurs en prison et leur temps de libération
local jailedPlayers = {} -- { [playerId] = releaseTime }

-- Gestionnaire d'événement pour l'initiation d'une arrestation
RegisterNetEvent('sheriffs_vs_outlaws:requestArrest')
AddEventHandler('sheriffs_vs_outlaws:requestArrest', function(targetPlayerId)
    local sourcePlayerId = source
    local sourcePed = GetPlayerPed(sourcePlayerId)
    local targetPed = GetPlayerPed(targetPlayerId)

    -- Vérifications
    if not IsPlayerSheriff(sourcePlayerId) then
        TriggerClientEvent('chat:addMessage', sourcePlayerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Système", "Vous devez être un Shérif pour arrêter quelqu'un."}
        })
        return
    end

    if not IsPlayerOutlaw(targetPlayerId) then
        TriggerClientEvent('chat:addMessage', sourcePlayerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Système", "Vous ne pouvez arrêter que les Hors-la-loi."}
        })
        return
    end

    if not targetPed or not DoesEntityExist(targetPed) then
        TriggerClientEvent('chat:addMessage', sourcePlayerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Système", "Le joueur ciblé n'existe plus."}
        })
        return
    end

    local sourceCoords = GetEntityCoords(sourcePed)
    local targetCoords = GetEntityCoords(targetPed)
    local distance = #(sourceCoords - targetCoords) -- Opérateur de longueur pour les vecteurs

    -- TODO: Ajuster la distance maximale pour une arrestation
    if distance > 5.0 then
        TriggerClientEvent('chat:addMessage', sourcePlayerId, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Système", "Vous êtes trop loin pour arrêter ce joueur."}
        })
        return
    end

    -- Logique d'arrestation
    -- Mise en prison
    SetEntityCoords(targetPed, Config.PrisonLocation.x, Config.PrisonLocation.y, Config.PrisonLocation.z, false, false, false, true)

    local jailTime = Config.DefaultJailTime or 300 -- Default à 5 minutes si non défini
    exports.MainCore:SetPlayerJailedStatus(targetPlayerId, true, jailTime)
    -- Ajoute le joueur à la table de suivi des prisonniers
    local releaseTime = GetGameTimer() + (jailTime * 1000)
    jailedPlayers[targetPlayerId] = releaseTime

    -- Récompense pour le Shérif via MainCore
    exports.MainCore:AddPlayerXP(sourcePlayerId, Config.PointsForArrest or 20) -- Default à 20 XP si non défini

    -- Impact sur la Prime via MainCore
    local outlawBountyBeforeArrest = exports.MainCore:GetPlayerBounty(targetPlayerId) or 0
    exports.MainCore:ResetPlayerBounty(targetPlayerId)
    -- ResetPlayerBountyAlertLevel est appelé implicitement par ResetPlayerBounty dans MainCore si la prime est à 0

    -- Entrée de journal pour arrestation importante
    local outlawName = GetPlayerName(targetPlayerId)
    local sheriffName = GetPlayerName(sourcePlayerId)

    if outlawBountyBeforeArrest >= Config.SignificantBountyThreshold then
        local journalMessage = string.format("Le célèbre bandit %s (prime de $%s) a été appréhendé par le Shérif %s après une longue traque !", outlawName, outlawBountyBeforeArrest, sheriffName)
        exports['sheriffs_vs_outlaws']:AddJournalEntry(journalMessage)
    end

    -- Appliquer les pénalités d'arrestation (argent, armes)
    if ApplyArrestPenalties then -- Vérifie si la fonction de sv_penalties est disponible
        ApplyArrestPenalties(targetPlayerId)
    else
        print("^1[ARREST] ERREUR: La fonction ApplyArrestPenalties n'est pas disponible. Assurez-vous que sv_penalties.lua est chargé avant sv_arrest.lua ou utilisez des exports.^7")
    end

    -- Notifier le système de contrats de prime de l'arrestation
    if exports.sv_bountycontracts and exports.sv_bountycontracts.NotifyBountyContractSystemOfArrest then
        local contractCompleted = exports.sv_bountycontracts:NotifyBountyContractSystemOfArrest(sourcePlayerId, targetPlayerId)
        if contractCompleted then
            -- Le message de succès et les récompenses spécifiques au contrat sont gérés dans NotifyBountyContractSystemOfArrest
            -- On pourrait éviter la notification d'XP standard si le contrat donne déjà de l'XP, ou l'ajouter.
            -- Pour l'instant, laissons les deux, sv_bountycontracts gère sa propre notification de récompense.
        end
    else
        print("^1[ARREST] ERREUR: Impossible de notifier sv_bountycontracts. L'export n'est pas disponible.^7")
    end

    -- Notifications
    TriggerClientEvent('chat:addMessage', sourcePlayerId, {
        color = {0, 255, 0},
        multiline = true,
        args = {"Système", "Vous avez arrêté " .. GetPlayerName(targetPlayerId) .. " et gagné " .. (Config.PointsForArrest or 20) .. " XP."}
    })

    TriggerClientEvent('chat:addMessage', targetPlayerId, {
        color = {255, 165, 0},
        multiline = true,
        args = {"Système", "Vous avez été arrêté par " .. GetPlayerName(sourcePlayerId) .. ". Temps de prison : " .. jailTime .. " secondes."}
    })

    -- Signaler la neutralisation pour la Chasse à l'Homme
    TriggerEvent('sheriffs_vs_outlaws:manhunt_targetNeutralized', targetPlayerId, sourcePlayerId)

    -- print("Joueur " .. GetPlayerName(sourcePlayerId) .. " (ID: " .. sourcePlayerId .. ") a arrêté le joueur " .. GetPlayerName(targetPlayerId) .. " (ID: " .. targetPlayerId .. ")") -- Print de débogage retiré
end)

-- Gestion optimisée de la peine de prison (boucle de vérification sur table dédiée)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000) -- Vérifie toutes les 5 secondes, plus raisonnable
        local currentTime = GetGameTimer()
        local playersToRelease = {}

        -- Itère uniquement sur les joueurs actuellement en prison dans notre table locale
        for playerId, releaseTime in pairs(jailedPlayers) do
            if currentTime >= releaseTime then
                table.insert(playersToRelease, playerId)
            end
        end

        -- Traite les libérations pour les joueurs identifiés
        if #playersToRelease > 0 then
            for _, playerId in ipairs(playersToRelease) do
                local playerPed = GetPlayerPed(playerId)
                if playerPed and DoesEntityExist(playerPed) then
                    -- Libération du joueur
                    SetEntityCoords(playerPed, Config.PrisonReleasePoint.x, Config.PrisonReleasePoint.y, Config.PrisonReleasePoint.z, false, false, false, true)
                    exports.MainCore:SetPlayerJailedStatus(playerId, false) -- Met à jour le statut dans MainCore

                    TriggerClientEvent('chat:addMessage', playerId, {
                        color = {0, 255, 0},
                        multiline = true,
                        args = {"Système", "Vous avez été libéré de prison."}
                    })
                    -- print("Joueur " .. GetPlayerName(playerId) .. " (ID: " .. playerId .. ") a été libéré de prison.") -- Print de débogage retiré
                else
                    -- Le joueur s'est déconnecté avant la fin de sa peine ou n'est plus valide
                    -- Assurez-vous que le statut est bien false dans MainCore (même si playerDropped devrait le faire)
                     if exports.MainCore:IsPlayerJailed(playerId) then
                        exports.MainCore:SetPlayerJailedStatus(playerId, false)
                     end
                    -- print("Joueur ID: " .. playerId .. " n'était plus valide pour la libération (déconnecté?), retiré de la liste des prisonniers.") -- Print de débogage retiré
                end
                -- Retire le joueur de la table de suivi dans tous les cas (libéré ou déconnecté/invalide)
                jailedPlayers[playerId] = nil
            end
        end
    end
end)

-- Gestion de la déconnexion d'un joueur emprisonné
AddEventHandler('playerDropped', function(reason)
    local playerId = source
    if jailedPlayers[playerId] then
        jailedPlayers[playerId] = nil
        -- MainCore devrait idéalement gérer la mise à jour du statut Jailed à la déconnexion,
        -- mais on s'assure ici que notre table locale est propre pour éviter des vérifications inutiles.
        -- print("Joueur ID: " .. playerId .. " déconnecté pendant sa peine, retiré de la liste locale des prisonniers.") -- Print de débogage retiré
    end
end)

-- print("sv_arrest.lua chargé.") -- Print de débogage final retiré