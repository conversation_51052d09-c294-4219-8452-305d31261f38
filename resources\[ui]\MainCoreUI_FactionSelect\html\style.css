body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Fond semi-transparent pour le jeu derrière */
    font-family: 'Arial', sans-serif; /* Police de base, à adapter pour un style Western */
}

body.hidden {
    display: none;
}

#factionMenu {
    background-color: #f0e6d2; /* Couleur parchemin/vieux papier */
    padding: 40px; /* Augmenter le padding */
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.7); /* Ombre portée plus prononcée */
    text-align: center;
    border: 3px solid #5a3a22; /* Bordure marron foncé plus épaisse */
    display: flex; /* Utiliser flexbox pour la mise en page */
    flex-direction: column; /* Aligner les éléments verticalement */
    align-items: center; /* Centrer les éléments horizontalement */
    gap: 20px; /* Ajouter de l'espace entre les éléments */
}

#factionMenu h1 {
    color: #5a3a22; /* Marron foncé pour le titre */
    font-family: 'Georgia', serif; /* Police plus thématique */
    margin-bottom: 25px;
}

#factionMenu button {
    background-color: #8b4513; /* Marron selle */
    color: white;
    border: 2px solid #5a3a22;
    padding: 15px 30px;
    margin: 5px; /* Réduire la marge pour un meilleur espacement dans flexbox */
    border-radius: 5px;
    cursor: pointer;
    font-size: 18px; /* Augmenter la taille de la police */
    transition: background-color 0.3s ease, transform 0.1s ease; /* Ajouter une transition pour le transform */
    min-width: 200px; /* Largeur minimale pour les boutons */
    text-transform: uppercase; /* Texte en majuscules */
    font-weight: bold; /* Texte en gras */
}

#factionMenu button:hover {
    background-color: #a0522d; /* Marron plus clair au survol */
    transform: translateY(-2px); /* Léger déplacement vers le haut au survol */
}

#factionMenu button:active {
    background-color: #5a3a22; /* Marron foncé au clic */
    transform: translateY(0); /* Retour à la position normale au clic */
}

#characterPreview {
    width: 300px; /* Largeur de la zone de prévisualisation */
    height: 400px; /* Hauteur de la zone de prévisualisation */
    border: 2px solid #5a3a22; /* Bordure similaire au menu */
    margin-bottom: 20px; /* Espace en dessous */
    background-color: #c4b7a6; /* Couleur de fond pour la zone de prévisualisation */
    /* Styles pour centrer le contenu si nécessaire */
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden; /* Cacher tout ce qui dépasse */
}