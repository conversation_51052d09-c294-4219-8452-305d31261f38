{"manifest": {"name": "@protobufjs/float", "description": "Reads / writes floats / doubles from / to buffers in both modern and ancient browsers.", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "dependencies": {}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"benchmark": "^2.1.4", "chalk": "^1.1.3", "ieee754": "^1.1.8", "istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js", "bench": "node bench"}, "_registry": "npm", "_loc": "D:\\Redm\\txData\\RedMBasicServerCFXDefault_1DC7C9.base\\cache\\yarn-cache\\v6\\npm-@protobufjs-float-1.0.2-5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1-integrity\\node_modules\\@protobufjs\\float\\package.json", "readmeFilename": "README.md", "readme": "@protobufjs/float\n=================\n[![npm](https://img.shields.io/npm/v/@protobufjs/float.svg)](https://www.npmjs.com/package/@protobufjs/float)\n\nReads / writes floats / doubles from / to buffers in both modern and ancient browsers. Fast.\n\nAPI\n---\n\n* **writeFloatLE(val: `number`, buf: `Uint8Array`, pos: `number`)**<br />\n  Writes a 32 bit float to a buffer using little endian byte order.\n\n* **writeFloatBE(val: `number`, buf: `Uint8Array`, pos: `number`)**<br />\n  Writes a 32 bit float to a buffer using big endian byte order.\n\n* **readFloatLE(buf: `Uint8Array`, pos: `number`): `number`**<br />\n  Reads a 32 bit float from a buffer using little endian byte order.\n\n* **readFloatBE(buf: `Uint8Array`, pos: `number`): `number`**<br />\n  Reads a 32 bit float from a buffer using big endian byte order.\n\n* **writeDoubleLE(val: `number`, buf: `Uint8Array`, pos: `number`)**<br />\n  Writes a 64 bit double to a buffer using little endian byte order.\n\n* **writeDoubleBE(val: `number`, buf: `Uint8Array`, pos: `number`)**<br />\n  Writes a 64 bit double to a buffer using big endian byte order.\n\n* **readDoubleLE(buf: `Uint8Array`, pos: `number`): `number`**<br />\n  Reads a 64 bit double from a buffer using little endian byte order.\n\n* **readDoubleBE(buf: `Uint8Array`, pos: `number`): `number`**<br />\n  Reads a 64 bit double from a buffer using big endian byte order.\n\nPerformance\n-----------\nThere is a simple benchmark included comparing raw read/write performance of this library (float), float's fallback for old browsers, the [ieee754](https://www.npmjs.com/package/ieee754) module and node's [buffer](https://nodejs.org/api/buffer.html). On an i7-2600k running node 6.9.1 it yields:\n\n```\nbenchmarking writeFloat performance ...\n\nfloat x 42,741,625 ops/sec ±1.75% (81 runs sampled)\nfloat (fallback) x 11,272,532 ops/sec ±1.12% (85 runs sampled)\nieee754 x 8,653,337 ops/sec ±1.18% (84 runs sampled)\nbuffer x 12,412,414 ops/sec ±1.41% (83 runs sampled)\nbuffer (noAssert) x 13,471,149 ops/sec ±1.09% (84 runs sampled)\n\n                      float was fastest\n           float (fallback) was 73.5% slower\n                    ieee754 was 79.6% slower\n                     buffer was 70.9% slower\n          buffer (noAssert) was 68.3% slower\n\nbenchmarking readFloat performance ...\n\nfloat x 44,382,729 ops/sec ±1.70% (84 runs sampled)\nfloat (fallback) x 20,925,938 ops/sec ±0.86% (87 runs sampled)\nieee754 x 17,189,009 ops/sec ±1.01% (87 runs sampled)\nbuffer x 10,518,437 ops/sec ±1.04% (83 runs sampled)\nbuffer (noAssert) x 11,031,636 ops/sec ±1.15% (87 runs sampled)\n\n                      float was fastest\n           float (fallback) was 52.5% slower\n                    ieee754 was 61.0% slower\n                     buffer was 76.1% slower\n          buffer (noAssert) was 75.0% slower\n\nbenchmarking writeDouble performance ...\n\nfloat x 38,624,906 ops/sec ±0.93% (83 runs sampled)\nfloat (fallback) x 10,457,811 ops/sec ±1.54% (85 runs sampled)\nieee754 x 7,681,130 ops/sec ±1.11% (83 runs sampled)\nbuffer x 12,657,876 ops/sec ±1.03% (83 runs sampled)\nbuffer (noAssert) x 13,372,795 ops/sec ±0.84% (85 runs sampled)\n\n                      float was fastest\n           float (fallback) was 73.1% slower\n                    ieee754 was 80.1% slower\n                     buffer was 67.3% slower\n          buffer (noAssert) was 65.3% slower\n\nbenchmarking readDouble performance ...\n\nfloat x 40,527,888 ops/sec ±1.05% (84 runs sampled)\nfloat (fallback) x 18,696,480 ops/sec ±0.84% (86 runs sampled)\nieee754 x 14,074,028 ops/sec ±1.04% (87 runs sampled)\nbuffer x 10,092,367 ops/sec ±1.15% (84 runs sampled)\nbuffer (noAssert) x 10,623,793 ops/sec ±0.96% (84 runs sampled)\n\n                      float was fastest\n           float (fallback) was 53.8% slower\n                    ieee754 was 65.3% slower\n                     buffer was 75.1% slower\n          buffer (noAssert) was 73.8% slower\n```\n\nTo run it yourself:\n\n```\n$> npm run bench\n```\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "Copyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@protobufjs/float/-/float-1.0.2.tgz#5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1", "type": "tarball", "reference": "https://registry.yarnpkg.com/@protobufjs/float/-/float-1.0.2.tgz", "hash": "5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=", "registry": "npm", "packageName": "@protobufjs/float", "cacheIntegrity": "sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ== sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E="}, "registry": "npm", "hash": "5e9e1abdcb73fc0a7cb8b291df78c8cbd97b87d1"}