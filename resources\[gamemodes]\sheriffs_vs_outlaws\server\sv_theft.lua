local PlayerTheftCooldowns = {} -- Pour stocker les cooldowns de vol par joueur
local PlayerLaunderingCooldowns = {} -- Pour stocker les cooldowns de blanchiment par joueur

-- Fonction pour vérifier si un joueur est un hors-la-loi
local function isPlayerOutlaw(playerId)
    local faction = exports.MainCore:GetPlayerFaction(playerId)
    return faction == "outlaws"
end

-- [[ MÉCANIQUE DE VOL SUR PNJ ]]
RegisterNetEvent('sheriffs_vs_outlaws:attemptTheftOnNPC')
AddEventHandler('sheriffs_vs_outlaws:attemptTheftOnNPC', function(npcNetId)
    local src = source
    local playerName = GetPlayerName(src)

    if not isPlayerOutlaw(src) then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Seuls les Hors-la-loi peuvent voler.", 5000)
        return
    end

    -- Vérifier le cooldown de vol pour ce joueur
    if PlayerTheftCooldowns[src] and (os.time() - PlayerTheftCooldowns[src]) < Config.TheftCooldown then
        local remainingTime = Config.TheftCooldown - (os.time() - PlayerTheftCooldowns[src])
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vous devez attendre encore " .. remainingTime .. " secondes avant de pouvoir voler à nouveau.", 5000)
        return
    end

    local success = math.random() < Config.TheftSuccessRate -- Utilise la probabilité de Config

    if success then
        -- Sélectionner un objet aléatoire de type "valuable" (ou autre selon la logique future)
        local stealableValuables = {}
        for itemId, itemData in pairs(Config.StealableItems) do
            if itemData.type == "valuable" then -- Pour l'instant, on ne vole que des objets de valeur
                table.insert(stealableValuables, itemId)
            end
        end

        if #stealableValuables > 0 then
            local stolenItemId = stealableValuables[math.random(#stealableValuables)]
            local stolenItem = Config.StealableItems[stolenItemId]

            -- Utiliser MainCore pour ajouter l'objet à l'inventaire du joueur
            local added = exports.MainCore:AddItem(src, stolenItemId, 1) -- Ajoute 1 objet de l'ID volé

            if added then
                TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vol réussi ! Vous avez obtenu : " .. stolenItem.name, 5000)
                print(string.format("^2[SvsO - Theft] %s (ID: %s) a volé %s.^0", playerName, src, stolenItem.name))
                PlayerTheftCooldowns[src] = os.time() -- Mettre à jour le cooldown
            else
                -- MainCore:AddItem retourne false si l'ajout échoue (inventaire plein, etc.)
                TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vol réussi, mais impossible d'ajouter l'objet à votre inventaire (peut-être plein ?).", 5000)
                print(string.format("^1[SvsO - Theft] %s (ID: %s) a volé %s mais l'ajout à l'inventaire MainCore a échoué.^0", playerName, src, stolenItem.name))
                -- On met quand même le cooldown pour éviter le spam
                PlayerTheftCooldowns[src] = os.time()
            end
        else
            TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vol réussi... mais il n'y avait rien d'intéressant à prendre.", 5000)
        end
    else
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vol échoué... Vous n'avez rien obtenu.", 5000)
        print(string.format("^1[SvsO - Theft] %s (ID: %s) a échoué son vol.^0", playerName, src))
        PlayerTheftCooldowns[src] = os.time() -- Mettre à jour le cooldown même en cas d'échec

        -- TODO: Possibilité d'alerter les Shérifs proches
        -- local playerCoords = GetEntityCoords(GetPlayerPed(src))
        -- AlertSheriffsNearby(playerCoords, "Un vol a été tenté !")
    end
end)

-- [[ MARCHÉ NOIR (RECELEUR) ]]
RegisterNetEvent('sheriffs_vs_outlaws:sellStolenGoods')
AddEventHandler('sheriffs_vs_outlaws:sellStolenGoods', function(fenceLocationId)
    local src = source
    local playerName = GetPlayerName(src)

    if not isPlayerOutlaw(src) then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Seuls les Hors-la-loi peuvent accéder au marché noir.", 5000)
        return
    end
    -- La vérification de l'inventaire est maintenant gérée par MainCore:SellStolenGoods
    -- On retire la vérification locale basée sur PlayersData

    -- Vérifier si le joueur est bien à un lieu de receleur (simplifié pour l'instant, le client devrait vérifier la proximité)
    -- local fence = Config.FenceLocations[fenceLocationId] -- Si on passe l'ID du receleur
    -- Pour une vérification plus robuste, le client enverrait sa position et on vérifierait la distance côté serveur.

    -- Appeler MainCore pour gérer la vente, en passant l'ID du lieu
    local success, message = exports.MainCore:SellStolenGoods(src, fenceLocationId)

    if success then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, message or "Objets vendus avec succès.", 7000)
        print(string.format("^2[SvsO - Theft] %s (ID: %s) a vendu des objets via MainCore au lieu %s.^0", playerName, src, fenceLocationId))
    else
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, message or "La vente a échoué ou vous n'aviez rien à vendre.", 5000)
    end
end)

-- [[ BLANCHIMENT D'ARGENT ]]
RegisterNetEvent('sheriffs_vs_outlaws:launderMoney')
AddEventHandler('sheriffs_vs_outlaws:launderMoney', function(launderingLocationId)
    local src = source
    local playerName = GetPlayerName(src)

    if not isPlayerOutlaw(src) then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Seuls les Hors-la-loi peuvent blanchir de l'argent.", 5000)
        return
    end

    -- Vérifier le cooldown de blanchiment pour ce joueur
    if PlayerLaunderingCooldowns[src] and (os.time() - PlayerLaunderingCooldowns[src]) < Config.MoneyLaunderingCooldown then
        local remainingTime = Config.MoneyLaunderingCooldown - (os.time() - PlayerLaunderingCooldowns[src])
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vous devez attendre encore " .. remainingTime .. " secondes avant de pouvoir blanchir à nouveau.", 5000)
        return
    end

    if exports.MainCore:GetPlayerDirtyMoney(src) == 0 then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Vous n'avez pas d'argent sale à blanchir.", 5000)
        return
    end

    -- Vérifier si le joueur est bien à un lieu de blanchiment (simplifié)

    -- Appeler MainCore pour gérer le blanchiment, en passant l'ID du lieu
    local success, message, launderedAmount, cleanAmount = exports.MainCore:LaunderMoney(src, launderingLocationId)

    if success then
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, message or string.format("Vous avez blanchi $%s en $%s.", launderedAmount or "?", cleanAmount or "?"), 7000)
        print(string.format("^2[SvsO - Theft] %s (ID: %s) a blanchi de l'argent via MainCore au lieu %s.^0", playerName, src, launderingLocationId))
        PlayerLaunderingCooldowns[src] = os.time() -- Mettre à jour le cooldown en cas de succès
    else
        TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, message or "Le blanchiment a échoué ou vous n'aviez pas d'argent à blanchir.", 5000)
    end
end)
-- [[ COMMANDES CLIENT ]]
RegisterNetEvent('sheriffs_vs_outlaws:requestPlayerInventory')
AddEventHandler('sheriffs_vs_outlaws:requestPlayerInventory', function()
    local src = source
    -- L'inventaire est géré par MainCore. Idéalement, on appellerait une fonction MainCore ici.
    -- Pour l'instant, on notifie simplement que l'inventaire n'est pas géré directement ici.
    -- local inventory = exports.MainCore:GetPlayerInventory(src) -- Exemple d'appel si la fonction existe
    -- if inventory then
    --     TriggerClientEvent('sheriffs_vs_outlaws:displayPlayerInventory', src, inventory) -- Adapter le format si nécessaire
    -- else
    --     TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "Impossible de récupérer l'inventaire depuis MainCore.", 5000)
    --     TriggerClientEvent('sheriffs_vs_outlaws:displayPlayerInventory', src, {})
    -- end
    -- Notification temporaire:
    TriggerClientEvent('sheriffs_vs_outlaws:showNotification', src, "La gestion de l'inventaire passe par MainCore.", 5000)
    TriggerClientEvent('sheriffs_vs_outlaws:displayPlayerInventory', src, {}) -- Envoyer table vide pour l'instant
end)

RegisterNetEvent('sheriffs_vs_outlaws:requestPlayerBalance')
AddEventHandler('sheriffs_vs_outlaws:requestPlayerBalance', function()
    local src = source
    local money = exports.MainCore:GetPlayerMoney(src)
    local dirtyMoney = exports.MainCore:GetPlayerDirtyMoney(src)
    TriggerClientEvent('sheriffs_vs_outlaws:displayPlayerBalance', src, money, dirtyMoney)
    -- Pas besoin de 'else' ici, GetPlayerMoney/DirtyMoney devraient retourner 0 si le joueur n'est pas trouvé ou n'a pas d'argent.
    -- La ligne 161 est donc redondante et peut causer une erreur si elle est atteinte.
    -- Suppression du bloc else.
end)

print('^2[SvsO - Theft] Le script sv_theft.lua est chargé et fonctionnel.^0')