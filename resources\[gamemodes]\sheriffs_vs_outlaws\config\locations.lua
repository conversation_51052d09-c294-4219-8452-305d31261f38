Config = {}

-- Emplacements braquables (coordonnées pour RedM)
Config.RobbableLocations = {
    {
        name = "Magasin général de Valentine",
        type = "store",
        coords = vector3(-324.3, 803.4, 117.9), -- Coordonnées approximatives du magasin de <PERSON>
        heading = 0.0,
        blipSprite = **********, -- Blip pour le magasin général
        reward = {min = 15, max = 40}, -- Récompense en dollars
        cooldown = 15, -- Temps de recharge en minutes
        robberyDuration = 60, -- Durée du braquage en secondes
        alertRadius = 150.0, -- Rayon d'alerte des forces de l'ordre en mètres
        bountyIncrease = 10 -- Augmentation de la prime du joueur
    },
    {
        name = "Banque de Saint Denis",
        type = "bank",
        coords = vector3(2644.48, -1292.45, 52.25), -- Coordonnées approximatives de la banque de Saint Denis
        heading = 0.0,
        blipSprite = **********, -- Blip pour la banque
        reward = {min = 50, max = 120}, -- Récompense en dollars
        cooldown = 30, -- Temps de recharge en minutes
        robberyDuration = 120, -- Durée du braquage en secondes
        alertRadius = 250.0, -- <PERSON><PERSON> d'alerte des forces de l'ordre en mètres
        bountyIncrease = 25 -- Augmentation de la prime du joueur
    },
    {
        name = "Saloon de Blackwater",
        type = "saloon",
        coords = vector3(-817.5, -1318.7, 43.7), -- Coordonnées approximatives du saloon de Blackwater
        heading = 0.0,
        blipSprite = **********, -- Blip pour le saloon
        reward = {min = 20, max = 50}, -- Récompense en dollars
        cooldown = 20, -- Temps de recharge en minutes
        robberyDuration = 90, -- Durée du braquage en secondes
        alertRadius = 200.0, -- Rayon d'alerte des forces de l'ordre en mètres
        bountyIncrease = 15 -- Augmentation de la prime du joueur
    }
}

Config.FactionSpawns = {
    sheriffs = { coords = vector4(-800.0, -1300.0, 43.0, 90.0), name = "Blackwater Sheriff Office" },
    outlaws = { coords = vector4(-2700.0, -2500.0, 5.0, 180.0), name = "Thieves Landing" }
}

Config.PrisonLocation = vector3(1000.0, 1000.0, 100.0) -- TODO: Remplacer par les coordonnées réelles de la prison
Config.PrisonReleasePoint = vector3(1050.0, 1050.0, 100.0) -- TODO: Remplacer par les coordonnées réelles du point de libération

Config.PointsForArrest = 20
Config.DefaultJailTime = 300 -- en secondes (5 minutes)

-- Configuration de la Chasse à l'Homme
Config.ManhuntBountyThreshold = 2500
Config.ManhuntDuration = 600 -- 10 minutes
Config.ManhuntCooldown = 1800 -- 30 minutes
Config.ManhuntRequiredRank = "Commandant des Rangers"
Config.ManhuntRangerRewardXP = 150
Config.ManhuntRangerRewardMoney = 200
Config.ManhuntTargetSurvivedXP = 100
Config.ManhuntTargetSurvivedMoney = 100
-- Statut des lieux (sera mis à jour dynamiquement)
Config.LocationStatus = {}

-- Initialiser le statut de tous les lieux
for i, location in ipairs(Config.RobbableLocations) do
    Config.LocationStatus[i] = {
        beingRobbed = false,
        lastRobbed = 0,
        canBeRobbed = true
    }
end 
-- Configuration for Train Robberies
Config.TrainRobberyCooldown = 1800000 -- Cooldown in milliseconds (e.g., 30 minutes)
Config.TrainRobberyRewardMin = 500
Config.TrainRobberyRewardMax = 1500
Config.TrainRobberyXP = 100
Config.TrainRobberyAlertLawTime = 30000 -- Time in milliseconds (e.g., 30 seconds)
Config.TrainRobberyBountyIncrease = 50 -- Augmentation de la prime pour un braquage de train réussi
-- Configuration des pénalités de mort et d'arrestation
Config.DeathMoneyPenaltyPercent = 10 -- Pourcentage d'argent perdu à la mort (hors arrestation)
Config.ArrestMoneyPenaltyPercent = 5 -- Pourcentage d'argent perdu lors d'une arrestation
Config.DeathLoseWeapons = true -- Les joueurs perdent-ils leurs armes à la mort (hors arrestation) ?
Config.ArrestLoseWeapons = true -- Les joueurs perdent-ils leurs armes lors d'une arrestation ?
Config.ProtectedWeaponsOnDeath = { -- Armes jamais perdues (hashes ou noms, à convertir en hashes si besoin)
    `WEAPON_UNARMED`,
    `WEAPON_MELEE_KNIFE` -- Exemple, à ajuster
}
-- Configuration pour le vol et le marché noir
Config.StealableItems = {
    JEWELRY_BOX = { name = "Boîte à Bijoux", value = 50, type = "valuable" },
    GOLD_NUGGET = { name = "Pépite d'Or", value = 25, type = "valuable" },
    WHISKEY_BOTTLE = { name = "Bouteille de Whiskey", value = 5, type = "contraband" }
}
-- 'value' est la valeur en argent sale si revendu au marché noir.

Config.TheftSuccessRate = 0.6 -- Probabilité de réussite du vol sur PNJ (60%)
Config.TheftCooldown = 120 -- Temps de recharge en secondes pour le vol sur PNJ (2 minutes)

-- Lieux pour le marché noir (Receleur)
Config.FenceLocations = {
    { name = "Receleur de Saint Denis", coords = vector3(2500.0, -1200.0, 50.0), blipSprite = 0, blipColor = 0 }, -- TODO: Remplacer par des coordonnées et blip réels
    { name = "Receleur de Valentine", coords = vector3(-300.0, 750.0, 115.0), blipSprite = 0, blipColor = 0 }  -- TODO: Remplacer par des coordonnées et blip réels
}

-- Lieu pour le blanchiment d'argent
Config.MoneyLaunderingLocations = {
    { name = "Blanchisseur discret", coords = vector3(2700.0, -1350.0, 45.0), blipSprite = 0, blipColor = 0 } -- TODO: Remplacer par des coordonnées et blip réels
}
Config.MoneyLaunderingRate = 0.7 -- 70% de l'argent sale est converti en propre
Config.MoneyLaunderingCooldown = 600 -- Temps de recharge en secondes pour le blanchiment (10 minutes)
-- Configuration for Bounty Contracts
Config.BountyContractPlayerMinBounty = 300
Config.BountyContractRewardMoney = 150 -- Can be a fixed amount or a percentage of the target's bounty
Config.BountyContractRewardXP = 75
Config.BountyBoards = {
    -- Example: { x = 123.45, y = 678.90, z = 12.34, heading = 45.0, name = "Sheriff's Office Bounty Board" }
    -- Add coordinates for bounty boards here if using physical interaction points
}