{"manifest": {"name": "@protobufjs/utf8", "description": "A minimal UTF8 implementation for number arrays.", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "devDependencies": {"istanbul": "^0.4.5", "tape": "^4.6.3"}, "scripts": {"test": "tape tests/*.js", "coverage": "istanbul cover node_modules/tape/bin/tape tests/*.js"}, "_registry": "npm", "_loc": "D:\\Redm\\txData\\RedMBasicServerCFXDefault_1DC7C9.base\\cache\\yarn-cache\\v6\\npm-@protobufjs-utf8-1.1.0-a777360b5b39a1a2e5106f8e858f2fd2d060c570-integrity\\node_modules\\@protobufjs\\utf8\\package.json", "readmeFilename": "README.md", "readme": "@protobufjs/utf8\n================\n[![npm](https://img.shields.io/npm/v/@protobufjs/utf8.svg)](https://www.npmjs.com/package/@protobufjs/utf8)\n\nA minimal UTF8 implementation for number arrays.\n\nAPI\n---\n\n* **utf8.length(string: `string`): `number`**<br />\n  Calculates the UTF8 byte length of a string.\n\n* **utf8.read(buffer: `Uint8Array`, start: `number`, end: `number`): `string`**<br />\n  Reads UTF8 bytes as a string.\n\n* **utf8.write(string: `string`, buffer: `Uint8Array`, offset: `number`): `number`**<br />\n  Writes a string as UTF8 bytes.\n\n\n**License:** [BSD 3-Clause License](https://opensource.org/licenses/BSD-3-Clause)\n", "licenseText": "Copyright (c) 2016, <PERSON>  All rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are\nmet:\n\n* Redistributions of source code must retain the above copyright\n  notice, this list of conditions and the following disclaimer.\n* Redistributions in binary form must reproduce the above copyright\n  notice, this list of conditions and the following disclaimer in the\n  documentation and/or other materials provided with the distribution.\n* Neither the name of its author, nor the names of its contributors\n  may be used to endorse or promote products derived from this software\n  without specific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n\"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\nLIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\nA PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\nOWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\nSPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT\nLIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\nDATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\nTHEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@protobufjs/utf8/-/utf8-1.1.0.tgz#a777360b5b39a1a2e5106f8e858f2fd2d060c570", "type": "tarball", "reference": "https://registry.yarnpkg.com/@protobufjs/utf8/-/utf8-1.1.0.tgz", "hash": "a777360b5b39a1a2e5106f8e858f2fd2d060c570", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=", "registry": "npm", "packageName": "@protobufjs/utf8", "cacheIntegrity": "sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw== sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA="}, "registry": "npm", "hash": "a777360b5b39a1a2e5106f8e858f2fd2d060c570"}