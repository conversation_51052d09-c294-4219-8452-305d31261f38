{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/index-minimal", "../src/reader.js", "../src/reader_buffer.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "char<PERSON>t", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "factory", "Float32Array", "f32", "f8b", "Uint8Array", "le", "writeFloat_f32_cpy", "val", "buf", "pos", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "writeFloat_ieee754", "writeUint", "sign", "isNaN", "round", "exponent", "floor", "log", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "f64", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "inquire", "moduleName", "mod", "eval", "Object", "keys", "e", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "configure", "Reader", "_configure", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "util", "build", "Writer", "BufferWriter", "rpc", "roots", "LongBits", "indexOutOfRange", "reader", "write<PERSON><PERSON>th", "RangeError", "value", "create_array", "isArray", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "create", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "bytes", "constructor", "skip", "skipType", "wireType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "<PERSON>", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "Service", "rpcImpl", "requestDelimited", "responseDelimited", "TypeError", "rpcCall", "method", "requestCtor", "responseCtor", "request", "callback", "self", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "finish", "response", "endedByRPC", "zero", "toNumber", "zzEncode", "zeroHash", "fromNumber", "from", "isString", "parseInt", "fromString", "low", "high", "unsigned", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "ifNotSet", "newError", "name", "CustomError", "message", "properties", "defineProperty", "get", "captureStackTrace", "stack", "toString", "pool", "global", "window", "emptyArray", "freeze", "emptyObject", "isNode", "process", "versions", "node", "isInteger", "Number", "isFinite", "isObject", "isset", "isSet", "obj", "prop", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "new<PERSON>uffer", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "lcFirst", "str", "toLowerCase", "substring", "ProtocolError", "oneOfGetter", "fieldNames", "fieldMap", "oneOfSetter", "toJSONOptions", "longs", "enums", "json", "encoding", "allocUnsafe", "Op", "next", "noop", "State", "writer", "head", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "set", "fork", "reset", "l<PERSON>im", "BufferWriter_", "writeBytesBuffer", "copy", "writeStringBuffer", "byteLength", "$require", "$module", "define", "amd", "isLong"], "mappings": ";;;;;;CAAA,SAAAA,GAAA,aAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,GChCAA,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,gCCxCA,IAAAE,EAAAf,EAOAe,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAG,OAAAF,MACAC,EACA,OAAAE,KAAAC,KAAA,EAAAL,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAI,EAAAlB,MAAA,IAGAmB,EAAAnB,MAAA,KAGAoB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAT,EAAAU,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAxB,EAAAyB,OAAA,SAAAxB,EAAAU,EAAAnB,GAIA,IAHA,IAEAsB,EAFAF,EAAApB,EACAyB,EAAA,EAEAR,EAAA,EAAAA,EAAAR,EAAAV,QAAA,CACA,IAAAmC,EAAAzB,EAAA0B,WAAAlB,KACA,GAAA,KAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAjD,EACA,MAAAmD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,KAAAsB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,GAAAsB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,EAAAsB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAAhC,EAAAoB,GAQAZ,EAAA6B,KAAA,SAAA5B,GACA,MAAA,mEAAA4B,KAAA5B,0BChIA,SAAA6B,IAOAC,KAAAC,EAAA,IAfAhD,EAAAC,QAAA6C,GAyBAG,UAAAC,GAAA,SAAAC,EAAAjD,EAAAC,GAKA,OAJA4C,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAAhB,KAAA,CACAjC,GAAAA,EACAC,IAAAA,GAAA4C,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAAjD,GACA,GAAAiD,IAAA1D,EACAsD,KAAAC,EAAA,QAEA,GAAA9C,IAAAT,EACAsD,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACA1B,EAAA,EAAAA,EAAA4B,EAAA9C,QACA8C,EAAA5B,GAAAvB,KAAAA,EACAmD,EAAAC,OAAA7B,EAAA,KAEAA,EAGA,OAAAsB,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA/B,EAAA,EACAA,EAAAnB,UAAAC,QACAiD,EAAArB,KAAA7B,UAAAmB,MACA,IAAAA,EAAA,EAAAA,EAAA4B,EAAA9C,QACA8C,EAAA5B,GAAAvB,GAAAa,MAAAsC,EAAA5B,KAAAtB,IAAAqD,GAEA,OAAAT,4BCaA,SAAAU,EAAAxD,GAwNA,MArNA,oBAAAyD,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAE,EAAA,IAAAC,WAAAF,EAAAhC,QACAmC,EAAA,MAAAF,EAAA,GAEA,SAAAG,EAAAC,EAAAC,EAAAC,GACAP,EAAA,GAAAK,EACAC,EAAAC,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GAGA,SAAAO,EAAAH,EAAAC,EAAAC,GACAP,EAAA,GAAAK,EACAC,EAAAC,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GAQA,SAAAQ,EAAAH,EAAAC,GAKA,OAJAN,EAAA,GAAAK,EAAAC,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAP,EAAA,GAGA,SAAAU,EAAAJ,EAAAC,GAKA,OAJAN,EAAA,GAAAK,EAAAC,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAP,EAAA,GAjBA1D,EAAAqE,aAAAR,EAAAC,EAAAI,EAEAlE,EAAAsE,aAAAT,EAAAK,EAAAJ,EAmBA9D,EAAAuE,YAAAV,EAAAM,EAAAC,EAEApE,EAAAwE,YAAAX,EAAAO,EAAAD,EA9CA,GAiDA,WAEA,SAAAM,EAAAC,EAAAX,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,QACA,GAAAW,MAAAb,GACAW,EAAA,WAAAV,EAAAC,QACA,GAAA,qBAAAF,EACAW,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,QACA,GAAAF,EAAA,sBACAW,GAAAC,GAAA,GAAAvD,KAAAyD,MAAAd,EAAA,yBAAA,EAAAC,EAAAC,OACA,CACA,IAAAa,EAAA1D,KAAA2D,MAAA3D,KAAA4D,IAAAjB,GAAA3C,KAAA6D,KAEAP,GAAAC,GAAA,GAAAG,EAAA,KAAA,GADA,QAAA1D,KAAAyD,MAAAd,EAAA3C,KAAA8D,IAAA,GAAAJ,GAAA,YACA,EAAAd,EAAAC,IAOA,SAAAkB,EAAAC,EAAApB,EAAAC,GACA,IAAAoB,EAAAD,EAAApB,EAAAC,GACAU,EAAA,GAAAU,GAAA,IAAA,EACAP,EAAAO,IAAA,GAAA,IACAC,EAAA,QAAAD,EACA,OAAA,MAAAP,EACAQ,EACAC,IACAZ,GAAAa,EAAAA,GACA,IAAAV,EACA,qBAAAH,EAAAW,EACAX,EAAAvD,KAAA8D,IAAA,EAAAJ,EAAA,MAAAQ,EAAA,SAdAtF,EAAAqE,aAAAI,EAAAgB,KAAA,KAAAC,GACA1F,EAAAsE,aAAAG,EAAAgB,KAAA,KAAAE,GAgBA3F,EAAAuE,YAAAY,EAAAM,KAAA,KAAAG,GACA5F,EAAAwE,YAAAW,EAAAM,KAAA,KAAAI,GAvCA,GA4CA,oBAAAC,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAnC,EAAA,IAAAC,WAAAmC,EAAArE,QACAmC,EAAA,MAAAF,EAAA,GAEA,SAAAqC,EAAAjC,EAAAC,EAAAC,GACA8B,EAAA,GAAAhC,EACAC,EAAAC,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GAGA,SAAAsC,EAAAlC,EAAAC,EAAAC,GACA8B,EAAA,GAAAhC,EACAC,EAAAC,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GACAK,EAAAC,EAAA,GAAAN,EAAA,GAQA,SAAAuC,EAAAlC,EAAAC,GASA,OARAN,EAAA,GAAAK,EAAAC,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACA8B,EAAA,GAGA,SAAAI,EAAAnC,EAAAC,GASA,OARAN,EAAA,GAAAK,EAAAC,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACAN,EAAA,GAAAK,EAAAC,EAAA,GACA8B,EAAA,GAzBA/F,EAAAoG,cAAAvC,EAAAmC,EAAAC,EAEAjG,EAAAqG,cAAAxC,EAAAoC,EAAAD,EA2BAhG,EAAAsG,aAAAzC,EAAAqC,EAAAC,EAEAnG,EAAAuG,aAAA1C,EAAAsC,EAAAD,EA9DA,GAiEA,WAEA,SAAAM,EAAA9B,EAAA+B,EAAAC,EAAA3C,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAAV,EAAAC,EAAAwC,GACA/B,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,EAAAyC,QACA,GAAA9B,MAAAb,GACAW,EAAA,EAAAV,EAAAC,EAAAwC,GACA/B,EAAA,WAAAV,EAAAC,EAAAyC,QACA,GAAA,sBAAA3C,EACAW,EAAA,EAAAV,EAAAC,EAAAwC,GACA/B,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,EAAAyC,OACA,CACA,IAAApB,EACA,GAAAvB,EAAA,uBAEAW,GADAY,EAAAvB,EAAA,UACA,EAAAC,EAAAC,EAAAwC,GACA/B,GAAAC,GAAA,GAAAW,EAAA,cAAA,EAAAtB,EAAAC,EAAAyC,OACA,CACA,IAAA5B,EAAA1D,KAAA2D,MAAA3D,KAAA4D,IAAAjB,GAAA3C,KAAA6D,KACA,OAAAH,IACAA,EAAA,MAEAJ,EAAA,kBADAY,EAAAvB,EAAA3C,KAAA8D,IAAA,GAAAJ,MACA,EAAAd,EAAAC,EAAAwC,GACA/B,GAAAC,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAQ,EAAA,WAAA,EAAAtB,EAAAC,EAAAyC,KAQA,SAAAC,EAAAvB,EAAAqB,EAAAC,EAAA1C,EAAAC,GACA,IAAA2C,EAAAxB,EAAApB,EAAAC,EAAAwC,GACAI,EAAAzB,EAAApB,EAAAC,EAAAyC,GACA/B,EAAA,GAAAkC,GAAA,IAAA,EACA/B,EAAA+B,IAAA,GAAA,KACAvB,EAAA,YAAA,QAAAuB,GAAAD,EACA,OAAA,OAAA9B,EACAQ,EACAC,IACAZ,GAAAa,EAAAA,GACA,IAAAV,EACA,OAAAH,EAAAW,EACAX,EAAAvD,KAAA8D,IAAA,EAAAJ,EAAA,OAAAQ,EAAA,kBAfAtF,EAAAoG,cAAAI,EAAAf,KAAA,KAAAC,EAAA,EAAA,GACA1F,EAAAqG,cAAAG,EAAAf,KAAA,KAAAE,EAAA,EAAA,GAiBA3F,EAAAsG,aAAAK,EAAAlB,KAAA,KAAAG,EAAA,EAAA,GACA5F,EAAAuG,aAAAI,EAAAlB,KAAA,KAAAI,EAAA,EAAA,GAnDA,GAuDA7F,EAKA,SAAA0F,EAAA3B,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAA4B,EAAA5B,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAA6B,EAAA5B,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAA4B,EAAA7B,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAlE,EAAAC,QAAAwD,EAAAA,2BCOA,SAAAsD,EAAAC,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAA1G,QAAA4G,OAAAC,KAAAH,GAAA1G,QACA,OAAA0G,EACA,MAAAI,IACA,OAAA,KAdArH,EAAAC,QAAA8G,wBCAA/G,EAAAC,QA6BA,SAAAqH,EAAAhF,EAAAiF,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAlH,EAAAgH,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAhH,EAAA+G,IACAG,EAAAJ,EAAAE,GACAhH,EAAA,GAEA,IAAAyD,EAAA3B,EAAAqF,KAAAD,EAAAlH,EAAAA,GAAA+G,GAGA,OAFA,EAAA/G,IACAA,EAAA,GAAA,EAAAA,IACAyD,4BCtCA,IAAA2D,EAAA3H,EAOA2H,EAAArH,OAAA,SAAAU,GAGA,IAFA,IAAA4G,EAAA,EACAnF,EAAA,EACAjB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAiB,EAAAzB,EAAA0B,WAAAlB,IACA,IACAoG,GAAA,EACAnF,EAAA,KACAmF,GAAA,EACA,QAAA,MAAAnF,IAAA,QAAA,MAAAzB,EAAA0B,WAAAlB,EAAA,OACAA,EACAoG,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAnG,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAmG,EAAAG,MAAA,SAAA9G,EAAAU,EAAAnB,GAIA,IAHA,IACAwH,EACAC,EAFArG,EAAApB,EAGAiB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAuG,EAAA/G,EAAA0B,WAAAlB,IACA,IACAE,EAAAnB,KAAAwH,GACAA,EAAA,KACArG,EAAAnB,KAAAwH,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAhH,EAAA0B,WAAAlB,EAAA,MACAuG,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KACAxG,EACAE,EAAAnB,KAAAwH,GAAA,GAAA,IACArG,EAAAnB,KAAAwH,GAAA,GAAA,GAAA,KAIArG,EAAAnB,KAAAwH,GAAA,GAAA,IAHArG,EAAAnB,KAAAwH,GAAA,EAAA,GAAA,KANArG,EAAAnB,KAAA,GAAAwH,EAAA,KAcA,OAAAxH,EAAAoB,2BCtGA,IAAA/B,EAAAI,EA2BA,SAAAiI,IACArI,EAAAsI,OAAAC,EAAAvI,EAAAwI,cACAxI,EAAAyI,KAAAF,IArBAvI,EAAA0I,MAAA,UAGA1I,EAAA2I,OAAAzI,EAAA,IACAF,EAAA4I,aAAA1I,EAAA,IACAF,EAAAsI,OAAApI,EAAA,GACAF,EAAAwI,aAAAtI,EAAA,IAGAF,EAAAyI,KAAAvI,EAAA,IACAF,EAAA6I,IAAA3I,EAAA,IACAF,EAAA8I,MAAA5I,EAAA,IACAF,EAAAqI,UAAAA,EAaArI,EAAA2I,OAAAJ,EAAAvI,EAAA4I,cACAP,iEClCAlI,EAAAC,QAAAkI,EAEA,IAEAE,EAFAC,EAAAvI,EAAA,IAIA6I,EAAAN,EAAAM,SACAhB,EAAAU,EAAAV,KAGA,SAAAiB,EAAAC,EAAAC,GACA,OAAAC,WAAA,uBAAAF,EAAA5E,IAAA,OAAA6E,GAAA,GAAA,MAAAD,EAAAjB,KASA,SAAAM,EAAAxG,GAMAoB,KAAAkB,IAAAtC,EAMAoB,KAAAmB,IAAA,EAMAnB,KAAA8E,IAAAlG,EAAApB,OAGA,IAwCA0I,EAxCAC,EAAA,oBAAArF,WACA,SAAAlC,GACA,GAAAA,aAAAkC,YAAAxD,MAAA8I,QAAAxH,GACA,OAAA,IAAAwG,EAAAxG,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAAtB,MAAA8I,QAAAxH,GACA,OAAA,IAAAwG,EAAAxG,GACA,MAAAiB,MAAA,mBAkEA,SAAAwG,IAEA,IAAAC,EAAA,IAAAT,EAAA,EAAA,GACAnH,EAAA,EACA,KAAA,EAAAsB,KAAA8E,IAAA9E,KAAAmB,KAaA,CACA,KAAAzC,EAAA,IAAAA,EAAA,CAEA,GAAAsB,KAAAmB,KAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,MAGA,GADAsG,EAAAxC,IAAAwC,EAAAxC,IAAA,IAAA9D,KAAAkB,IAAAlB,KAAAmB,OAAA,EAAAzC,KAAA,EACAsB,KAAAkB,IAAAlB,KAAAmB,OAAA,IACA,OAAAmF,EAIA,OADAA,EAAAxC,IAAAwC,EAAAxC,IAAA,IAAA9D,KAAAkB,IAAAlB,KAAAmB,SAAA,EAAAzC,KAAA,EACA4H,EAxBA,KAAA5H,EAAA,IAAAA,EAGA,GADA4H,EAAAxC,IAAAwC,EAAAxC,IAAA,IAAA9D,KAAAkB,IAAAlB,KAAAmB,OAAA,EAAAzC,KAAA,EACAsB,KAAAkB,IAAAlB,KAAAmB,OAAA,IACA,OAAAmF,EAKA,GAFAA,EAAAxC,IAAAwC,EAAAxC,IAAA,IAAA9D,KAAAkB,IAAAlB,KAAAmB,OAAA,MAAA,EACAmF,EAAAvC,IAAAuC,EAAAvC,IAAA,IAAA/D,KAAAkB,IAAAlB,KAAAmB,OAAA,KAAA,EACAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IACA,OAAAmF,EAgBA,GAfA5H,EAAA,EAeA,EAAAsB,KAAA8E,IAAA9E,KAAAmB,KACA,KAAAzC,EAAA,IAAAA,EAGA,GADA4H,EAAAvC,IAAAuC,EAAAvC,IAAA,IAAA/D,KAAAkB,IAAAlB,KAAAmB,OAAA,EAAAzC,EAAA,KAAA,EACAsB,KAAAkB,IAAAlB,KAAAmB,OAAA,IACA,OAAAmF,OAGA,KAAA5H,EAAA,IAAAA,EAAA,CAEA,GAAAsB,KAAAmB,KAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,MAGA,GADAsG,EAAAvC,IAAAuC,EAAAvC,IAAA,IAAA/D,KAAAkB,IAAAlB,KAAAmB,OAAA,EAAAzC,EAAA,KAAA,EACAsB,KAAAkB,IAAAlB,KAAAmB,OAAA,IACA,OAAAmF,EAIA,MAAAzG,MAAA,2BAkCA,SAAA0G,EAAArF,EAAApC,GACA,OAAAoC,EAAApC,EAAA,GACAoC,EAAApC,EAAA,IAAA,EACAoC,EAAApC,EAAA,IAAA,GACAoC,EAAApC,EAAA,IAAA,MAAA,EA+BA,SAAA0H,IAGA,GAAAxG,KAAAmB,IAAA,EAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAA,GAEA,OAAA,IAAA6F,EAAAU,EAAAvG,KAAAkB,IAAAlB,KAAAmB,KAAA,GAAAoF,EAAAvG,KAAAkB,IAAAlB,KAAAmB,KAAA,IArLAiE,EAAAqB,OAAAlB,EAAAmB,OACA,SAAA9H,GACA,OAAAwG,EAAAqB,OAAA,SAAA7H,GACA,OAAA2G,EAAAmB,OAAAC,SAAA/H,GACA,IAAA0G,EAAA1G,GAEAuH,EAAAvH,KACAA,IAGAuH,EAEAf,EAAAlF,UAAA0G,EAAArB,EAAAjI,MAAA4C,UAAA2G,UAAAtB,EAAAjI,MAAA4C,UAAAX,MAOA6F,EAAAlF,UAAA4G,QACAZ,EAAA,WACA,WACA,GAAAA,GAAA,IAAAlG,KAAAkB,IAAAlB,KAAAmB,QAAA,EAAAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IAAA,OAAA+E,EACA,GAAAA,GAAAA,GAAA,IAAAlG,KAAAkB,IAAAlB,KAAAmB,OAAA,KAAA,EAAAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IAAA,OAAA+E,EACA,GAAAA,GAAAA,GAAA,IAAAlG,KAAAkB,IAAAlB,KAAAmB,OAAA,MAAA,EAAAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IAAA,OAAA+E,EACA,GAAAA,GAAAA,GAAA,IAAAlG,KAAAkB,IAAAlB,KAAAmB,OAAA,MAAA,EAAAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IAAA,OAAA+E,EACA,GAAAA,GAAAA,GAAA,GAAAlG,KAAAkB,IAAAlB,KAAAmB,OAAA,MAAA,EAAAnB,KAAAkB,IAAAlB,KAAAmB,OAAA,IAAA,OAAA+E,EAGA,IAAAlG,KAAAmB,KAAA,GAAAnB,KAAA8E,IAEA,MADA9E,KAAAmB,IAAAnB,KAAA8E,IACAgB,EAAA9F,KAAA,IAEA,OAAAkG,IAQAd,EAAAlF,UAAA6G,MAAA,WACA,OAAA,EAAA/G,KAAA8G,UAOA1B,EAAAlF,UAAA8G,OAAA,WACA,IAAAd,EAAAlG,KAAA8G,SACA,OAAAZ,IAAA,IAAA,EAAAA,GAAA,GAqFAd,EAAAlF,UAAA+G,KAAA,WACA,OAAA,IAAAjH,KAAA8G,UAcA1B,EAAAlF,UAAAgH,QAAA,WAGA,GAAAlH,KAAAmB,IAAA,EAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAA,GAEA,OAAAuG,EAAAvG,KAAAkB,IAAAlB,KAAAmB,KAAA,IAOAiE,EAAAlF,UAAAiH,SAAA,WAGA,GAAAnH,KAAAmB,IAAA,EAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAA,GAEA,OAAA,EAAAuG,EAAAvG,KAAAkB,IAAAlB,KAAAmB,KAAA,IAmCAiE,EAAAlF,UAAAkH,MAAA,WAGA,GAAApH,KAAAmB,IAAA,EAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAA,GAEA,IAAAkG,EAAAX,EAAA6B,MAAA3F,YAAAzB,KAAAkB,IAAAlB,KAAAmB,KAEA,OADAnB,KAAAmB,KAAA,EACA+E,GAQAd,EAAAlF,UAAAmH,OAAA,WAGA,GAAArH,KAAAmB,IAAA,EAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAA,GAEA,IAAAkG,EAAAX,EAAA6B,MAAA5D,aAAAxD,KAAAkB,IAAAlB,KAAAmB,KAEA,OADAnB,KAAAmB,KAAA,EACA+E,GAOAd,EAAAlF,UAAAoH,MAAA,WACA,IAAA9J,EAAAwC,KAAA8G,SACAjI,EAAAmB,KAAAmB,IACArC,EAAAkB,KAAAmB,IAAA3D,EAGA,GAAAsB,EAAAkB,KAAA8E,IACA,MAAAgB,EAAA9F,KAAAxC,GAGA,OADAwC,KAAAmB,KAAA3D,EACAF,MAAA8I,QAAApG,KAAAkB,KACAlB,KAAAkB,IAAA3B,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAAkB,KAAAkB,IAAAqG,YAAA,GACAvH,KAAA4G,EAAAhC,KAAA5E,KAAAkB,IAAArC,EAAAC,IAOAsG,EAAAlF,UAAAhC,OAAA,WACA,IAAAoJ,EAAAtH,KAAAsH,QACA,OAAAzC,EAAAE,KAAAuC,EAAA,EAAAA,EAAA9J,SAQA4H,EAAAlF,UAAAsH,KAAA,SAAAhK,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAwC,KAAAmB,IAAA3D,EAAAwC,KAAA8E,IACA,MAAAgB,EAAA9F,KAAAxC,GACAwC,KAAAmB,KAAA3D,OAEA,GAEA,GAAAwC,KAAAmB,KAAAnB,KAAA8E,IACA,MAAAgB,EAAA9F,YACA,IAAAA,KAAAkB,IAAAlB,KAAAmB,QAEA,OAAAnB,MAQAoF,EAAAlF,UAAAuH,SAAA,SAAAC,GACA,OAAAA,GACA,KAAA,EACA1H,KAAAwH,OACA,MACA,KAAA,EACAxH,KAAAwH,KAAA,GACA,MACA,KAAA,EACAxH,KAAAwH,KAAAxH,KAAA8G,UACA,MACA,KAAA,EACA,KAAA,IAAAY,EAAA,EAAA1H,KAAA8G,WACA9G,KAAAyH,SAAAC,GAEA,MACA,KAAA,EACA1H,KAAAwH,KAAA,GACA,MAGA,QACA,MAAA3H,MAAA,qBAAA6H,EAAA,cAAA1H,KAAAmB,KAEA,OAAAnB,MAGAoF,EAAAC,EAAA,SAAAsC,GACArC,EAAAqC,EAEA,IAAAxK,EAAAoI,EAAAqC,KAAA,SAAA,WACArC,EAAAsC,MAAAzC,EAAAlF,UAAA,CAEA4H,MAAA,WACA,OAAAzB,EAAAzB,KAAA5E,MAAA7C,IAAA,IAGA4K,OAAA,WACA,OAAA1B,EAAAzB,KAAA5E,MAAA7C,IAAA,IAGA6K,OAAA,WACA,OAAA3B,EAAAzB,KAAA5E,MAAAiI,WAAA9K,IAAA,IAGA+K,QAAA,WACA,OAAA1B,EAAA5B,KAAA5E,MAAA7C,IAAA,IAGAgL,SAAA,WACA,OAAA3B,EAAA5B,KAAA5E,MAAA7C,IAAA,mCC/YAF,EAAAC,QAAAoI,EAGA,IAAAF,EAAApI,EAAA,IACAsI,EAAApF,UAAAkE,OAAAqC,OAAArB,EAAAlF,YAAAqH,YAAAjC,EAEA,IAAAC,EAAAvI,EAAA,IASA,SAAAsI,EAAA1G,GACAwG,EAAAR,KAAA5E,KAAApB,GAUA2G,EAAAmB,SACApB,EAAApF,UAAA0G,EAAArB,EAAAmB,OAAAxG,UAAAX,OAKA+F,EAAApF,UAAAhC,OAAA,WACA,IAAA4G,EAAA9E,KAAA8G,SACA,OAAA9G,KAAAkB,IAAAkH,UAAApI,KAAAmB,IAAAnB,KAAAmB,IAAA7C,KAAA+J,IAAArI,KAAAmB,IAAA2D,EAAA9E,KAAA8E,uCClCA7H,EAAAC,QAAA,4BCKAA,EA6BAoL,QAAAtL,EAAA,gCClCAC,EAAAC,QAAAoL,EAEA,IAAA/C,EAAAvI,EAAA,IAsCA,SAAAsL,EAAAC,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAAG,UAAA,8BAEAnD,EAAAxF,aAAA6E,KAAA5E,MAMAA,KAAAuI,QAAAA,EAMAvI,KAAAwI,mBAAAA,EAMAxI,KAAAyI,oBAAAA,IA1DAH,EAAApI,UAAAkE,OAAAqC,OAAAlB,EAAAxF,aAAAG,YAAAqH,YAAAe,GAwEApI,UAAAyI,QAAA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,IAAAD,EACA,MAAAL,UAAA,6BAEA,IAAAO,EAAAjJ,KACA,IAAAgJ,EACA,OAAAzD,EAAA2D,UAAAP,EAAAM,EAAAL,EAAAC,EAAAC,EAAAC,GAEA,IAAAE,EAAAV,QAEA,OADAY,WAAA,WAAAH,EAAAnJ,MAAA,mBAAA,GACAnD,EAGA,IACA,OAAAuM,EAAAV,QACAK,EACAC,EAAAI,EAAAT,iBAAA,kBAAA,UAAAO,GAAAK,SACA,SAAArL,EAAAsL,GAEA,GAAAtL,EAEA,OADAkL,EAAAzI,KAAA,QAAAzC,EAAA6K,GACAI,EAAAjL,GAGA,GAAA,OAAAsL,EAEA,OADAJ,EAAAnK,KAAA,GACApC,EAGA,KAAA2M,aAAAP,GACA,IACAO,EAAAP,EAAAG,EAAAR,kBAAA,kBAAA,UAAAY,GACA,MAAAtL,GAEA,OADAkL,EAAAzI,KAAA,QAAAzC,EAAA6K,GACAI,EAAAjL,GAKA,OADAkL,EAAAzI,KAAA,OAAA6I,EAAAT,GACAI,EAAA,KAAAK,KAGA,MAAAtL,GAGA,OAFAkL,EAAAzI,KAAA,QAAAzC,EAAA6K,GACAO,WAAA,WAAAH,EAAAjL,IAAA,GACArB,IASA4L,EAAApI,UAAApB,IAAA,SAAAwK,GAOA,OANAtJ,KAAAuI,UACAe,GACAtJ,KAAAuI,QAAA,KAAA,KAAA,MACAvI,KAAAuI,QAAA,KACAvI,KAAAQ,KAAA,OAAAH,OAEAL,kCC3IA/C,EAAAC,QAAA2I,EAEA,IAAAN,EAAAvI,EAAA,IAUA,SAAA6I,EAAA/B,EAAAC,GASA/D,KAAA8D,GAAAA,IAAA,EAMA9D,KAAA+D,GAAAA,IAAA,EAQA,IAAAwF,EAAA1D,EAAA0D,KAAA,IAAA1D,EAAA,EAAA,GAEA0D,EAAAC,SAAA,WAAA,OAAA,GACAD,EAAAE,SAAAF,EAAAtB,SAAA,WAAA,OAAAjI,MACAuJ,EAAA/L,OAAA,WAAA,OAAA,GAOA,IAAAkM,EAAA7D,EAAA6D,SAAA,mBAOA7D,EAAA8D,WAAA,SAAAzD,GACA,GAAA,IAAAA,EACA,OAAAqD,EACA,IAAA1H,EAAAqE,EAAA,EACArE,IACAqE,GAAAA,GACA,IAAApC,EAAAoC,IAAA,EACAnC,GAAAmC,EAAApC,GAAA,aAAA,EAUA,OATAjC,IACAkC,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAA8B,EAAA/B,EAAAC,IAQA8B,EAAA+D,KAAA,SAAA1D,GACA,GAAA,iBAAAA,EACA,OAAAL,EAAA8D,WAAAzD,GACA,GAAAX,EAAAsE,SAAA3D,GAAA,CAEA,IAAAX,EAAAqC,KAGA,OAAA/B,EAAA8D,WAAAG,SAAA5D,EAAA,KAFAA,EAAAX,EAAAqC,KAAAmC,WAAA7D,GAIA,OAAAA,EAAA8D,KAAA9D,EAAA+D,KAAA,IAAApE,EAAAK,EAAA8D,MAAA,EAAA9D,EAAA+D,OAAA,GAAAV,GAQA1D,EAAA3F,UAAAsJ,SAAA,SAAAU,GACA,IAAAA,GAAAlK,KAAA+D,KAAA,GAAA,CACA,IAAAD,EAAA,GAAA9D,KAAA8D,KAAA,EACAC,GAAA/D,KAAA+D,KAAA,EAGA,OAFAD,IACAC,EAAAA,EAAA,IAAA,KACAD,EAAA,WAAAC,GAEA,OAAA/D,KAAA8D,GAAA,WAAA9D,KAAA+D,IAQA8B,EAAA3F,UAAAiK,OAAA,SAAAD,GACA,OAAA3E,EAAAqC,KACA,IAAArC,EAAAqC,KAAA,EAAA5H,KAAA8D,GAAA,EAAA9D,KAAA+D,KAAAmG,GAEA,CAAAF,IAAA,EAAAhK,KAAA8D,GAAAmG,KAAA,EAAAjK,KAAA+D,GAAAmG,WAAAA,IAGA,IAAAtK,EAAAP,OAAAa,UAAAN,WAOAiG,EAAAuE,SAAA,SAAAC,GACA,OAAAA,IAAAX,EACAH,EACA,IAAA1D,GACAjG,EAAAgF,KAAAyF,EAAA,GACAzK,EAAAgF,KAAAyF,EAAA,IAAA,EACAzK,EAAAgF,KAAAyF,EAAA,IAAA,GACAzK,EAAAgF,KAAAyF,EAAA,IAAA,MAAA,GAEAzK,EAAAgF,KAAAyF,EAAA,GACAzK,EAAAgF,KAAAyF,EAAA,IAAA,EACAzK,EAAAgF,KAAAyF,EAAA,IAAA,GACAzK,EAAAgF,KAAAyF,EAAA,IAAA,MAAA,IAQAxE,EAAA3F,UAAAoK,OAAA,WACA,OAAAjL,OAAAC,aACA,IAAAU,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,GACA,IAAA9D,KAAA+D,GACA/D,KAAA+D,KAAA,EAAA,IACA/D,KAAA+D,KAAA,GAAA,IACA/D,KAAA+D,KAAA,KAQA8B,EAAA3F,UAAAuJ,SAAA,WACA,IAAAc,EAAAvK,KAAA+D,IAAA,GAGA,OAFA/D,KAAA+D,KAAA/D,KAAA+D,IAAA,EAAA/D,KAAA8D,KAAA,IAAAyG,KAAA,EACAvK,KAAA8D,IAAA9D,KAAA8D,IAAA,EAAAyG,KAAA,EACAvK,MAOA6F,EAAA3F,UAAA+H,SAAA,WACA,IAAAsC,IAAA,EAAAvK,KAAA8D,IAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,KAAA,EAAA9D,KAAA+D,IAAA,IAAAwG,KAAA,EACAvK,KAAA+D,IAAA/D,KAAA+D,KAAA,EAAAwG,KAAA,EACAvK,MAOA6F,EAAA3F,UAAA1C,OAAA,WACA,IAAAgN,EAAAxK,KAAA8D,GACA2G,GAAAzK,KAAA8D,KAAA,GAAA9D,KAAA+D,IAAA,KAAA,EACA2G,EAAA1K,KAAA+D,KAAA,GACA,OAAA,IAAA2G,EACA,IAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,kCCrMA,IAAAnF,EAAArI,EAoOA,SAAA2K,EAAA8C,EAAAC,EAAAC,GACA,IAAA,IAAAxG,EAAAD,OAAAC,KAAAuG,GAAAlM,EAAA,EAAAA,EAAA2F,EAAA7G,SAAAkB,EACAiM,EAAAtG,EAAA3F,MAAAhC,GAAAmO,IACAF,EAAAtG,EAAA3F,IAAAkM,EAAAvG,EAAA3F,KACA,OAAAiM,EAoBA,SAAAG,EAAAC,GAEA,SAAAC,EAAAC,EAAAC,GAEA,KAAAlL,gBAAAgL,GACA,OAAA,IAAAA,EAAAC,EAAAC,GAKA9G,OAAA+G,eAAAnL,KAAA,UAAA,CAAAoL,IAAA,WAAA,OAAAH,KAGApL,MAAAwL,kBACAxL,MAAAwL,kBAAArL,KAAAgL,GAEA5G,OAAA+G,eAAAnL,KAAA,QAAA,CAAAkG,MAAArG,QAAAyL,OAAA,KAEAJ,GACArD,EAAA7H,KAAAkL,GAWA,OARAF,EAAA9K,UAAAkE,OAAAqC,OAAA5G,MAAAK,YAAAqH,YAAAyD,EAEA5G,OAAA+G,eAAAH,EAAA9K,UAAA,OAAA,CAAAkL,IAAA,WAAA,OAAAL,KAEAC,EAAA9K,UAAAqL,SAAA,WACA,OAAAvL,KAAA+K,KAAA,KAAA/K,KAAAiL,SAGAD,EAvRAzF,EAAA2D,UAAAlM,EAAA,GAGAuI,EAAAtH,OAAAjB,EAAA,GAGAuI,EAAAxF,aAAA/C,EAAA,GAGAuI,EAAA6B,MAAApK,EAAA,GAGAuI,EAAAvB,QAAAhH,EAAA,GAGAuI,EAAAV,KAAA7H,EAAA,GAGAuI,EAAAiG,KAAAxO,EAAA,GAGAuI,EAAAM,SAAA7I,EAAA,IAGAuI,EAAAkG,OAAA,oBAAAC,QAAAA,QACA,oBAAAD,QAAAA,QACA,oBAAAxC,MAAAA,MACAjJ,KAQAuF,EAAAoG,WAAAvH,OAAAwH,OAAAxH,OAAAwH,OAAA,IAAA,GAOArG,EAAAsG,YAAAzH,OAAAwH,OAAAxH,OAAAwH,OAAA,IAAA,GAQArG,EAAAuG,UAAAvG,EAAAkG,OAAAM,SAAAxG,EAAAkG,OAAAM,QAAAC,UAAAzG,EAAAkG,OAAAM,QAAAC,SAAAC,MAQA1G,EAAA2G,UAAAC,OAAAD,WAAA,SAAAhG,GACA,MAAA,iBAAAA,GAAAkG,SAAAlG,IAAA5H,KAAA2D,MAAAiE,KAAAA,GAQAX,EAAAsE,SAAA,SAAA3D,GACA,MAAA,iBAAAA,GAAAA,aAAA7G,QAQAkG,EAAA8G,SAAA,SAAAnG,GACA,OAAAA,GAAA,iBAAAA,GAWAX,EAAA+G,MAQA/G,EAAAgH,MAAA,SAAAC,EAAAC,GACA,IAAAvG,EAAAsG,EAAAC,GACA,QAAA,MAAAvG,IAAAsG,EAAAE,eAAAD,MACA,iBAAAvG,GAAA,GAAA5I,MAAA8I,QAAAF,GAAAA,EAAA1I,OAAA4G,OAAAC,KAAA6B,GAAA1I,UAeA+H,EAAAmB,OAAA,WACA,IACA,IAAAA,EAAAnB,EAAAvB,QAAA,UAAA0C,OAEA,OAAAA,EAAAxG,UAAAyM,UAAAjG,EAAA,KACA,MAAApC,GAEA,OAAA,MAPA,GAYAiB,EAAAqH,EAAA,KAGArH,EAAAsH,EAAA,KAOAtH,EAAAuH,UAAA,SAAAC,GAEA,MAAA,iBAAAA,EACAxH,EAAAmB,OACAnB,EAAAsH,EAAAE,GACA,IAAAxH,EAAAjI,MAAAyP,GACAxH,EAAAmB,OACAnB,EAAAqH,EAAAG,GACA,oBAAAjM,WACAiM,EACA,IAAAjM,WAAAiM,IAOAxH,EAAAjI,MAAA,oBAAAwD,WAAAA,WAAAxD,MAeAiI,EAAAqC,KAAArC,EAAAkG,OAAAuB,SAAAzH,EAAAkG,OAAAuB,QAAApF,MACArC,EAAAkG,OAAA7D,MACArC,EAAAvB,QAAA,QAOAuB,EAAA0H,OAAA,mBAOA1H,EAAA2H,QAAA,wBAOA3H,EAAA4H,QAAA,6CAOA5H,EAAA6H,WAAA,SAAAlH,GACA,OAAAA,EACAX,EAAAM,SAAA+D,KAAA1D,GAAAoE,SACA/E,EAAAM,SAAA6D,UASAnE,EAAA8H,aAAA,SAAAhD,EAAAH,GACA,IAAA5D,EAAAf,EAAAM,SAAAuE,SAAAC,GACA,OAAA9E,EAAAqC,KACArC,EAAAqC,KAAA0F,SAAAhH,EAAAxC,GAAAwC,EAAAvC,GAAAmG,GACA5D,EAAAkD,WAAAU,IAkBA3E,EAAAsC,MAAAA,EAOAtC,EAAAgI,QAAA,SAAAC,GACA,OAAAA,EAAAnP,OAAA,GAAAoP,cAAAD,EAAAE,UAAA,IA0CAnI,EAAAuF,SAAAA,EAmBAvF,EAAAoI,cAAA7C,EAAA,iBAoBAvF,EAAAqI,YAAA,SAAAC,GAEA,IADA,IAAAC,EAAA,GACApP,EAAA,EAAAA,EAAAmP,EAAArQ,SAAAkB,EACAoP,EAAAD,EAAAnP,IAAA,EAOA,OAAA,WACA,IAAA,IAAA2F,EAAAD,OAAAC,KAAArE,MAAAtB,EAAA2F,EAAA7G,OAAA,GAAA,EAAAkB,IAAAA,EACA,GAAA,IAAAoP,EAAAzJ,EAAA3F,KAAAsB,KAAAqE,EAAA3F,MAAAhC,GAAA,OAAAsD,KAAAqE,EAAA3F,IACA,OAAA2F,EAAA3F,KAiBA6G,EAAAwI,YAAA,SAAAF,GAQA,OAAA,SAAA9C,GACA,IAAA,IAAArM,EAAA,EAAAA,EAAAmP,EAAArQ,SAAAkB,EACAmP,EAAAnP,KAAAqM,UACA/K,KAAA6N,EAAAnP,MAoBA6G,EAAAyI,cAAA,CACAC,MAAA5O,OACA6O,MAAA7O,OACAiI,MAAAjI,OACA8O,MAAA,GAIA5I,EAAAF,EAAA,WACA,IAAAqB,EAAAnB,EAAAmB,OAEAA,GAMAnB,EAAAqH,EAAAlG,EAAAkD,OAAA9I,WAAA8I,MAAAlD,EAAAkD,MAEA,SAAA1D,EAAAkI,GACA,OAAA,IAAA1H,EAAAR,EAAAkI,IAEA7I,EAAAsH,EAAAnG,EAAA2H,aAEA,SAAA7J,GACA,OAAA,IAAAkC,EAAAlC,KAbAe,EAAAqH,EAAArH,EAAAsH,EAAA,8DC7YA5P,EAAAC,QAAAuI,EAEA,IAEAC,EAFAH,EAAAvI,EAAA,IAIA6I,EAAAN,EAAAM,SACA5H,EAAAsH,EAAAtH,OACA4G,EAAAU,EAAAV,KAWA,SAAAyJ,EAAAnR,EAAA2H,EAAA7D,GAMAjB,KAAA7C,GAAAA,EAMA6C,KAAA8E,IAAAA,EAMA9E,KAAAuO,KAAA7R,EAMAsD,KAAAiB,IAAAA,EAIA,SAAAuN,KAUA,SAAAC,EAAAC,GAMA1O,KAAA2O,KAAAD,EAAAC,KAMA3O,KAAA4O,KAAAF,EAAAE,KAMA5O,KAAA8E,IAAA4J,EAAA5J,IAMA9E,KAAAuO,KAAAG,EAAAG,OAQA,SAAApJ,IAMAzF,KAAA8E,IAAA,EAMA9E,KAAA2O,KAAA,IAAAL,EAAAE,EAAA,EAAA,GAMAxO,KAAA4O,KAAA5O,KAAA2O,KAMA3O,KAAA6O,OAAA,KAqDA,SAAAC,EAAA7N,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAA8N,EAAAjK,EAAA7D,GACAjB,KAAA8E,IAAAA,EACA9E,KAAAuO,KAAA7R,EACAsD,KAAAiB,IAAAA,EA8CA,SAAA+N,EAAA/N,EAAAC,EAAAC,GACA,KAAAF,EAAA8C,IACA7C,EAAAC,KAAA,IAAAF,EAAA6C,GAAA,IACA7C,EAAA6C,IAAA7C,EAAA6C,KAAA,EAAA7C,EAAA8C,IAAA,MAAA,EACA9C,EAAA8C,MAAA,EAEA,KAAA,IAAA9C,EAAA6C,IACA5C,EAAAC,KAAA,IAAAF,EAAA6C,GAAA,IACA7C,EAAA6C,GAAA7C,EAAA6C,KAAA,EAEA5C,EAAAC,KAAAF,EAAA6C,GA2CA,SAAAmL,EAAAhO,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAtKAwE,EAAAgB,OAAAlB,EAAAmB,OACA,WACA,OAAAjB,EAAAgB,OAAA,WACA,OAAA,IAAAf,OAIA,WACA,OAAA,IAAAD,GAQAA,EAAAlB,MAAA,SAAAC,GACA,OAAA,IAAAe,EAAAjI,MAAAkH,IAKAe,EAAAjI,QAAAA,QACAmI,EAAAlB,MAAAgB,EAAAiG,KAAA/F,EAAAlB,MAAAgB,EAAAjI,MAAA4C,UAAA2G,WAUApB,EAAAvF,UAAAgP,EAAA,SAAA/R,EAAA2H,EAAA7D,GAGA,OAFAjB,KAAA4O,KAAA5O,KAAA4O,KAAAL,KAAA,IAAAD,EAAAnR,EAAA2H,EAAA7D,GACAjB,KAAA8E,KAAAA,EACA9E,OA8BA+O,EAAA7O,UAAAkE,OAAAqC,OAAA6H,EAAApO,YACA/C,GAxBA,SAAA8D,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BAwE,EAAAvF,UAAA4G,OAAA,SAAAZ,GAWA,OARAlG,KAAA8E,MAAA9E,KAAA4O,KAAA5O,KAAA4O,KAAAL,KAAA,IAAAQ,GACA7I,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAApB,IACA9E,MASAyF,EAAAvF,UAAA6G,MAAA,SAAAb,GACA,OAAAA,EAAA,EACAlG,KAAAkP,EAAAF,EAAA,GAAAnJ,EAAA8D,WAAAzD,IACAlG,KAAA8G,OAAAZ,IAQAT,EAAAvF,UAAA8G,OAAA,SAAAd,GACA,OAAAlG,KAAA8G,QAAAZ,GAAA,EAAAA,GAAA,MAAA,IAkCAT,EAAAvF,UAAA4H,MAZArC,EAAAvF,UAAA6H,OAAA,SAAA7B,GACA,IAAAI,EAAAT,EAAA+D,KAAA1D,GACA,OAAAlG,KAAAkP,EAAAF,EAAA1I,EAAA9I,SAAA8I,IAkBAb,EAAAvF,UAAA8H,OAAA,SAAA9B,GACA,IAAAI,EAAAT,EAAA+D,KAAA1D,GAAAuD,WACA,OAAAzJ,KAAAkP,EAAAF,EAAA1I,EAAA9I,SAAA8I,IAQAb,EAAAvF,UAAA+G,KAAA,SAAAf,GACA,OAAAlG,KAAAkP,EAAAJ,EAAA,EAAA5I,EAAA,EAAA,IAyBAT,EAAAvF,UAAAiH,SAVA1B,EAAAvF,UAAAgH,QAAA,SAAAhB,GACA,OAAAlG,KAAAkP,EAAAD,EAAA,EAAA/I,IAAA,IA6BAT,EAAAvF,UAAAiI,SAZA1C,EAAAvF,UAAAgI,QAAA,SAAAhC,GACA,IAAAI,EAAAT,EAAA+D,KAAA1D,GACA,OAAAlG,KAAAkP,EAAAD,EAAA,EAAA3I,EAAAxC,IAAAoL,EAAAD,EAAA,EAAA3I,EAAAvC,KAkBA0B,EAAAvF,UAAAkH,MAAA,SAAAlB,GACA,OAAAlG,KAAAkP,EAAA3J,EAAA6B,MAAA7F,aAAA,EAAA2E,IASAT,EAAAvF,UAAAmH,OAAA,SAAAnB,GACA,OAAAlG,KAAAkP,EAAA3J,EAAA6B,MAAA9D,cAAA,EAAA4C,IAGA,IAAAiJ,EAAA5J,EAAAjI,MAAA4C,UAAAkP,IACA,SAAAnO,EAAAC,EAAAC,GACAD,EAAAkO,IAAAnO,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAzC,EAAA,EAAAA,EAAAuC,EAAAzD,SAAAkB,EACAwC,EAAAC,EAAAzC,GAAAuC,EAAAvC,IAQA+G,EAAAvF,UAAAoH,MAAA,SAAApB,GACA,IAAApB,EAAAoB,EAAA1I,SAAA,EACA,IAAAsH,EACA,OAAA9E,KAAAkP,EAAAJ,EAAA,EAAA,GACA,GAAAvJ,EAAAsE,SAAA3D,GAAA,CACA,IAAAhF,EAAAuE,EAAAlB,MAAAO,EAAA7G,EAAAT,OAAA0I,IACAjI,EAAAyB,OAAAwG,EAAAhF,EAAA,GACAgF,EAAAhF,EAEA,OAAAlB,KAAA8G,OAAAhC,GAAAoK,EAAAC,EAAArK,EAAAoB,IAQAT,EAAAvF,UAAAhC,OAAA,SAAAgI,GACA,IAAApB,EAAAD,EAAArH,OAAA0I,GACA,OAAApB,EACA9E,KAAA8G,OAAAhC,GAAAoK,EAAArK,EAAAG,MAAAF,EAAAoB,GACAlG,KAAAkP,EAAAJ,EAAA,EAAA,IAQArJ,EAAAvF,UAAAmP,KAAA,WAIA,OAHArP,KAAA6O,OAAA,IAAAJ,EAAAzO,MACAA,KAAA2O,KAAA3O,KAAA4O,KAAA,IAAAN,EAAAE,EAAA,EAAA,GACAxO,KAAA8E,IAAA,EACA9E,MAOAyF,EAAAvF,UAAAoP,MAAA,WAUA,OATAtP,KAAA6O,QACA7O,KAAA2O,KAAA3O,KAAA6O,OAAAF,KACA3O,KAAA4O,KAAA5O,KAAA6O,OAAAD,KACA5O,KAAA8E,IAAA9E,KAAA6O,OAAA/J,IACA9E,KAAA6O,OAAA7O,KAAA6O,OAAAN,OAEAvO,KAAA2O,KAAA3O,KAAA4O,KAAA,IAAAN,EAAAE,EAAA,EAAA,GACAxO,KAAA8E,IAAA,GAEA9E,MAOAyF,EAAAvF,UAAAqP,OAAA,WACA,IAAAZ,EAAA3O,KAAA2O,KACAC,EAAA5O,KAAA4O,KACA9J,EAAA9E,KAAA8E,IAOA,OANA9E,KAAAsP,QAAAxI,OAAAhC,GACAA,IACA9E,KAAA4O,KAAAL,KAAAI,EAAAJ,KACAvO,KAAA4O,KAAAA,EACA5O,KAAA8E,KAAAA,GAEA9E,MAOAyF,EAAAvF,UAAAkJ,OAAA,WAIA,IAHA,IAAAuF,EAAA3O,KAAA2O,KAAAJ,KACArN,EAAAlB,KAAAuH,YAAAhD,MAAAvE,KAAA8E,KACA3D,EAAA,EACAwN,GACAA,EAAAxR,GAAAwR,EAAA1N,IAAAC,EAAAC,GACAA,GAAAwN,EAAA7J,IACA6J,EAAAA,EAAAJ,KAGA,OAAArN,GAGAuE,EAAAJ,EAAA,SAAAmK,GACA9J,EAAA8J,+BCxcAvS,EAAAC,QAAAwI,EAGA,IAAAD,EAAAzI,EAAA,KACA0I,EAAAxF,UAAAkE,OAAAqC,OAAAhB,EAAAvF,YAAAqH,YAAA7B,EAEA,IAAAH,EAAAvI,EAAA,IAEA0J,EAAAnB,EAAAmB,OAQA,SAAAhB,IACAD,EAAAb,KAAA5E,MAQA0F,EAAAnB,MAAA,SAAAC,GACA,OAAAkB,EAAAnB,MAAAgB,EAAAsH,GAAArI,IAGA,IAAAiL,EAAA/I,GAAAA,EAAAxG,qBAAAY,YAAA,QAAA4F,EAAAxG,UAAAkP,IAAArE,KACA,SAAA9J,EAAAC,EAAAC,GACAD,EAAAkO,IAAAnO,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAyO,KACAzO,EAAAyO,KAAAxO,EAAAC,EAAA,EAAAF,EAAAzD,aACA,IAAA,IAAAkB,EAAA,EAAAA,EAAAuC,EAAAzD,QACA0D,EAAAC,KAAAF,EAAAvC,MAgBA,SAAAiR,EAAA1O,EAAAC,EAAAC,GACAF,EAAAzD,OAAA,GACA+H,EAAAV,KAAAG,MAAA/D,EAAAC,EAAAC,GAEAD,EAAAyL,UAAA1L,EAAAE,GAdAuE,EAAAxF,UAAAoH,MAAA,SAAApB,GACAX,EAAAsE,SAAA3D,KACAA,EAAAX,EAAAqH,EAAA1G,EAAA,WACA,IAAApB,EAAAoB,EAAA1I,SAAA,EAIA,OAHAwC,KAAA8G,OAAAhC,GACAA,GACA9E,KAAAkP,EAAAO,EAAA3K,EAAAoB,GACAlG,MAaA0F,EAAAxF,UAAAhC,OAAA,SAAAgI,GACA,IAAApB,EAAA4B,EAAAkJ,WAAA1J,GAIA,OAHAlG,KAAA8G,OAAAhC,GACAA,GACA9E,KAAAkP,EAAAS,EAAA7K,EAAAoB,GACAlG,uBjBvEApD,KAAAC,MAcAC,EAPA,SAAA+S,EAAA9E,GACA,IAAA+E,EAAAlT,EAAAmO,GAGA,OAFA+E,GACAnT,EAAAoO,GAAA,GAAAnG,KAAAkL,EAAAlT,EAAAmO,GAAA,CAAA7N,QAAA,IAAA2S,EAAAC,EAAAA,EAAA5S,SACA4S,EAAA5S,QAGA2S,CAAAhT,EAAA,IAGAC,EAAAyI,KAAAkG,OAAA3O,SAAAA,EAGA,mBAAAiT,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAnI,GAKA,OAJAA,GAAAA,EAAAqI,SACAnT,EAAAyI,KAAAqC,KAAAA,EACA9K,EAAAqI,aAEArI,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\r\n\r\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\r\n    // sources through a conflict-free require shim and is again wrapped within an iife that\r\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\r\n    // so that minification can remove the directives of each module.\r\n\r\n    function $require(name) {\r\n        var $module = cache[name];\r\n        if (!$module)\r\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\r\n        return $module.exports;\r\n    }\r\n\r\n    var protobuf = $require(entries[0]);\r\n\r\n    // Expose globally\r\n    protobuf.util.global.protobuf = protobuf;\r\n\r\n    // Be nice to AMD\r\n    if (typeof define === \"function\" && define.amd)\r\n        define([\"long\"], function(Long) {\r\n            if (Long && Long.isLong) {\r\n                protobuf.util.Long = Long;\r\n                protobuf.configure();\r\n            }\r\n            return protobuf;\r\n        });\r\n\r\n    // Be nice to CommonJS\r\n    if (typeof module === \"object\" && module && module.exports)\r\n        module.exports = protobuf;\r\n\r\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\r\nvar protobuf = exports;\r\n\r\n/**\r\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\r\n * @name build\r\n * @type {string}\r\n * @const\r\n */\r\nprotobuf.build = \"minimal\";\r\n\r\n// Serialization\r\nprotobuf.Writer       = require(16);\r\nprotobuf.BufferWriter = require(17);\r\nprotobuf.Reader       = require(9);\r\nprotobuf.BufferReader = require(10);\r\n\r\n// Utility\r\nprotobuf.util         = require(15);\r\nprotobuf.rpc          = require(12);\r\nprotobuf.roots        = require(11);\r\nprotobuf.configure    = configure;\r\n\r\n/* istanbul ignore next */\r\n/**\r\n * Reconfigures the library according to the environment.\r\n * @returns {undefined}\r\n */\r\nfunction configure() {\r\n    protobuf.Reader._configure(protobuf.BufferReader);\r\n    protobuf.util._configure();\r\n}\r\n\r\n// Set up buffer utility according to the environment\r\nprotobuf.Writer._configure(protobuf.BufferWriter);\r\nconfigure();\r\n", "\"use strict\";\r\nmodule.exports = Reader;\r\n\r\nvar util      = require(15);\r\n\r\nvar BufferReader; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    utf8      = util.utf8;\r\n\r\n/* istanbul ignore next */\r\nfunction indexOutOfRange(reader, writeLength) {\r\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\r\n}\r\n\r\n/**\r\n * Constructs a new reader instance using the specified buffer.\r\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n * @param {Uint8Array} buffer Buffer to read from\r\n */\r\nfunction Reader(buffer) {\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @type {Uint8Array}\r\n     */\r\n    this.buf = buffer;\r\n\r\n    /**\r\n     * Read buffer position.\r\n     * @type {number}\r\n     */\r\n    this.pos = 0;\r\n\r\n    /**\r\n     * Read buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = buffer.length;\r\n}\r\n\r\nvar create_array = typeof Uint8Array !== \"undefined\"\r\n    ? function create_typed_array(buffer) {\r\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array(buffer) {\r\n        if (Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    };\r\n\r\n/**\r\n * Creates a new reader using the specified buffer.\r\n * @function\r\n * @param {Uint8Array|Buffer} buffer Buffer to read from\r\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\r\n * @throws {Error} If `buffer` is not a valid buffer\r\n */\r\nReader.create = util.Buffer\r\n    ? function create_buffer_setup(buffer) {\r\n        return (Reader.create = function create_buffer(buffer) {\r\n            return util.Buffer.isBuffer(buffer)\r\n                ? new BufferReader(buffer)\r\n                /* istanbul ignore next */\r\n                : create_array(buffer);\r\n        })(buffer);\r\n    }\r\n    /* istanbul ignore next */\r\n    : create_array;\r\n\r\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\r\n\r\n/**\r\n * Reads a varint as an unsigned 32 bit value.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.uint32 = (function read_uint32_setup() {\r\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\r\n    return function read_uint32() {\r\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n\r\n        /* istanbul ignore if */\r\n        if ((this.pos += 5) > this.len) {\r\n            this.pos = this.len;\r\n            throw indexOutOfRange(this, 10);\r\n        }\r\n        return value;\r\n    };\r\n})();\r\n\r\n/**\r\n * Reads a varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.int32 = function read_int32() {\r\n    return this.uint32() | 0;\r\n};\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sint32 = function read_sint32() {\r\n    var value = this.uint32();\r\n    return value >>> 1 ^ -(value & 1) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readLongVarint() {\r\n    // tends to deopt with local vars for octet etc.\r\n    var bits = new LongBits(0, 0);\r\n    var i = 0;\r\n    if (this.len - this.pos > 4) { // fast route (lo)\r\n        for (; i < 4; ++i) {\r\n            // 1st..4th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 5th\r\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\r\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\r\n        if (this.buf[this.pos++] < 128)\r\n            return bits;\r\n        i = 0;\r\n    } else {\r\n        for (; i < 3; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 1st..3th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 4th\r\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\r\n        return bits;\r\n    }\r\n    if (this.len - this.pos > 4) { // fast route (hi)\r\n        for (; i < 5; ++i) {\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    } else {\r\n        for (; i < 5; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    }\r\n    /* istanbul ignore next */\r\n    throw Error(\"invalid varint encoding\");\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads a varint as a signed 64 bit value.\r\n * @name Reader#int64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as an unsigned 64 bit value.\r\n * @name Reader#uint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 64 bit value.\r\n * @name Reader#sint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as a boolean.\r\n * @returns {boolean} Value read\r\n */\r\nReader.prototype.bool = function read_bool() {\r\n    return this.uint32() !== 0;\r\n};\r\n\r\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\r\n    return (buf[end - 4]\r\n          | buf[end - 3] << 8\r\n          | buf[end - 2] << 16\r\n          | buf[end - 1] << 24) >>> 0;\r\n}\r\n\r\n/**\r\n * Reads fixed 32 bits as an unsigned 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.fixed32 = function read_fixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4);\r\n};\r\n\r\n/**\r\n * Reads fixed 32 bits as a signed 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sfixed32 = function read_sfixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readFixed64(/* this: Reader */) {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 8);\r\n\r\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads fixed 64 bits.\r\n * @name Reader#fixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads zig-zag encoded fixed 64 bits.\r\n * @name Reader#sfixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a float (32 bit) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.float = function read_float() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readFloatLE(this.buf, this.pos);\r\n    this.pos += 4;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a double (64 bit float) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.double = function read_double() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readDoubleLE(this.buf, this.pos);\r\n    this.pos += 8;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @returns {Uint8Array} Value read\r\n */\r\nReader.prototype.bytes = function read_bytes() {\r\n    var length = this.uint32(),\r\n        start  = this.pos,\r\n        end    = this.pos + length;\r\n\r\n    /* istanbul ignore if */\r\n    if (end > this.len)\r\n        throw indexOutOfRange(this, length);\r\n\r\n    this.pos += length;\r\n    if (Array.isArray(this.buf)) // plain array\r\n        return this.buf.slice(start, end);\r\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\r\n        ? new this.buf.constructor(0)\r\n        : this._slice.call(this.buf, start, end);\r\n};\r\n\r\n/**\r\n * Reads a string preceeded by its byte length as a varint.\r\n * @returns {string} Value read\r\n */\r\nReader.prototype.string = function read_string() {\r\n    var bytes = this.bytes();\r\n    return utf8.read(bytes, 0, bytes.length);\r\n};\r\n\r\n/**\r\n * Skips the specified number of bytes if specified, otherwise skips a varint.\r\n * @param {number} [length] Length if known, otherwise a varint is assumed\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skip = function skip(length) {\r\n    if (typeof length === \"number\") {\r\n        /* istanbul ignore if */\r\n        if (this.pos + length > this.len)\r\n            throw indexOutOfRange(this, length);\r\n        this.pos += length;\r\n    } else {\r\n        do {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n        } while (this.buf[this.pos++] & 128);\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Skips the next element of the specified wire type.\r\n * @param {number} wireType Wire type received\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skipType = function(wireType) {\r\n    switch (wireType) {\r\n        case 0:\r\n            this.skip();\r\n            break;\r\n        case 1:\r\n            this.skip(8);\r\n            break;\r\n        case 2:\r\n            this.skip(this.uint32());\r\n            break;\r\n        case 3:\r\n            while ((wireType = this.uint32() & 7) !== 4) {\r\n                this.skipType(wireType);\r\n            }\r\n            break;\r\n        case 5:\r\n            this.skip(4);\r\n            break;\r\n\r\n        /* istanbul ignore next */\r\n        default:\r\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\r\n    }\r\n    return this;\r\n};\r\n\r\nReader._configure = function(BufferReader_) {\r\n    BufferReader = BufferReader_;\r\n\r\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\r\n    util.merge(Reader.prototype, {\r\n\r\n        int64: function read_int64() {\r\n            return readLongVarint.call(this)[fn](false);\r\n        },\r\n\r\n        uint64: function read_uint64() {\r\n            return readLongVarint.call(this)[fn](true);\r\n        },\r\n\r\n        sint64: function read_sint64() {\r\n            return readLongVarint.call(this).zzDecode()[fn](false);\r\n        },\r\n\r\n        fixed64: function read_fixed64() {\r\n            return readFixed64.call(this)[fn](true);\r\n        },\r\n\r\n        sfixed64: function read_sfixed64() {\r\n            return readFixed64.call(this)[fn](false);\r\n        }\r\n\r\n    });\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erReader;\r\n\r\n// extends Reader\r\nvar Reader = require(9);\r\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\r\n\r\nvar util = require(15);\r\n\r\n/**\r\n * Constructs a new buffer reader instance.\r\n * @classdesc Wire format reader using node buffers.\r\n * @extends Reader\r\n * @constructor\r\n * @param {Buffer} buffer Buffer to read from\r\n */\r\nfunction BufferReader(buffer) {\r\n    Reader.call(this, buffer);\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @name BufferReader#buf\r\n     * @type {Buffer}\r\n     */\r\n}\r\n\r\n/* istanbul ignore else */\r\nif (util.Buffer)\r\n    BufferReader.prototype._slice = util.Buffer.prototype.slice;\r\n\r\n/**\r\n * @override\r\n */\r\nBufferReader.prototype.string = function read_string_buffer() {\r\n    var len = this.uint32(); // modifies pos\r\n    return this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len));\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @name BufferReader#bytes\r\n * @function\r\n * @returns {<PERSON><PERSON>er} Value read\r\n */\r\n", "\"use strict\";\r\nmodule.exports = {};\r\n\r\n/**\r\n * Named roots.\r\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\r\n * Can also be used manually to make roots available accross modules.\r\n * @name roots\r\n * @type {Object.<string,Root>}\r\n * @example\r\n * // pbjs -r myroot -o compiled.js ...\r\n *\r\n * // in another module:\r\n * require(\"./compiled.js\");\r\n *\r\n * // in any subsequent module:\r\n * var root = protobuf.roots[\"myroot\"];\r\n */\r\n", "\"use strict\";\r\n\r\n/**\r\n * Streaming RPC helpers.\r\n * @namespace\r\n */\r\nvar rpc = exports;\r\n\r\n/**\r\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\r\n * @typedef RPCImpl\r\n * @type {function}\r\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\r\n * @param {Uint8Array} requestData Request data\r\n * @param {RPCImplCallback} callback Callback function\r\n * @returns {undefined}\r\n * @example\r\n * function rpcImpl(method, requestData, callback) {\r\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\r\n *         throw Error(\"no such method\");\r\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\r\n *         callback(err, responseData);\r\n *     });\r\n * }\r\n */\r\n\r\n/**\r\n * Node-style callback as used by {@link RPCImpl}.\r\n * @typedef RPCImplCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any, otherwise `null`\r\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\nrpc.Service = require(13);\r\n", "\"use strict\";\r\nmodule.exports = Service;\r\n\r\nvar util = require(15);\r\n\r\n// Extends EventEmitter\r\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\r\n\r\n/**\r\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\r\n *\r\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\r\n * @typedef rpc.ServiceMethodCallback\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {TRes} [response] Response message\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\r\n * @typedef rpc.ServiceMethod\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\r\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\r\n */\r\n\r\n/**\r\n * Constructs a new RPC service instance.\r\n * @classdesc An RPC service as returned by {@link Service#create}.\r\n * @exports rpc.Service\r\n * @extends util.EventEmitter\r\n * @constructor\r\n * @param {RPCImpl} rpcImpl RPC implementation\r\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\r\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\r\n */\r\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\r\n\r\n    if (typeof rpcImpl !== \"function\")\r\n        throw TypeError(\"rpcImpl must be a function\");\r\n\r\n    util.EventEmitter.call(this);\r\n\r\n    /**\r\n     * RPC implementation. Becomes `null` once the service is ended.\r\n     * @type {RPCImpl|null}\r\n     */\r\n    this.rpcImpl = rpcImpl;\r\n\r\n    /**\r\n     * Whether requests are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.requestDelimited = Boolean(requestDelimited);\r\n\r\n    /**\r\n     * Whether responses are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.responseDelimited = Boolean(responseDelimited);\r\n}\r\n\r\n/**\r\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\r\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\r\n * @param {Constructor<TReq>} requestCtor Request constructor\r\n * @param {Constructor<TRes>} responseCtor Response constructor\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\r\n * @returns {undefined}\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n */\r\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\r\n\r\n    if (!request)\r\n        throw TypeError(\"request must be specified\");\r\n\r\n    var self = this;\r\n    if (!callback)\r\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\r\n\r\n    if (!self.rpcImpl) {\r\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\r\n        return undefined;\r\n    }\r\n\r\n    try {\r\n        return self.rpcImpl(\r\n            method,\r\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\r\n            function rpcCallback(err, response) {\r\n\r\n                if (err) {\r\n                    self.emit(\"error\", err, method);\r\n                    return callback(err);\r\n                }\r\n\r\n                if (response === null) {\r\n                    self.end(/* endedByRPC */ true);\r\n                    return undefined;\r\n                }\r\n\r\n                if (!(response instanceof responseCtor)) {\r\n                    try {\r\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\r\n                    } catch (err) {\r\n                        self.emit(\"error\", err, method);\r\n                        return callback(err);\r\n                    }\r\n                }\r\n\r\n                self.emit(\"data\", response, method);\r\n                return callback(null, response);\r\n            }\r\n        );\r\n    } catch (err) {\r\n        self.emit(\"error\", err, method);\r\n        setTimeout(function() { callback(err); }, 0);\r\n        return undefined;\r\n    }\r\n};\r\n\r\n/**\r\n * Ends this service and emits the `end` event.\r\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\r\n * @returns {rpc.Service} `this`\r\n */\r\nService.prototype.end = function end(endedByRPC) {\r\n    if (this.rpcImpl) {\r\n        if (!endedByRPC) // signal end to rpcImpl\r\n            this.rpcImpl(null, null, null);\r\n        this.rpcImpl = null;\r\n        this.emit(\"end\").off();\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = LongBits;\r\n\r\nvar util = require(15);\r\n\r\n/**\r\n * Constructs new long bits.\r\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\r\n * @memberof util\r\n * @constructor\r\n * @param {number} lo Low 32 bits, unsigned\r\n * @param {number} hi High 32 bits, unsigned\r\n */\r\nfunction LongBits(lo, hi) {\r\n\r\n    // note that the casts below are theoretically unnecessary as of today, but older statically\r\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\r\n\r\n    /**\r\n     * Low bits.\r\n     * @type {number}\r\n     */\r\n    this.lo = lo >>> 0;\r\n\r\n    /**\r\n     * High bits.\r\n     * @type {number}\r\n     */\r\n    this.hi = hi >>> 0;\r\n}\r\n\r\n/**\r\n * Zero bits.\r\n * @memberof util.LongBits\r\n * @type {util.LongBits}\r\n */\r\nvar zero = LongBits.zero = new LongBits(0, 0);\r\n\r\nzero.toNumber = function() { return 0; };\r\nzero.zzEncode = zero.zzDecode = function() { return this; };\r\nzero.length = function() { return 1; };\r\n\r\n/**\r\n * Zero hash.\r\n * @memberof util.LongBits\r\n * @type {string}\r\n */\r\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\r\n\r\n/**\r\n * Constructs new long bits from the specified number.\r\n * @param {number} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.fromNumber = function fromNumber(value) {\r\n    if (value === 0)\r\n        return zero;\r\n    var sign = value < 0;\r\n    if (sign)\r\n        value = -value;\r\n    var lo = value >>> 0,\r\n        hi = (value - lo) / 4294967296 >>> 0;\r\n    if (sign) {\r\n        hi = ~hi >>> 0;\r\n        lo = ~lo >>> 0;\r\n        if (++lo > 4294967295) {\r\n            lo = 0;\r\n            if (++hi > 4294967295)\r\n                hi = 0;\r\n        }\r\n    }\r\n    return new LongBits(lo, hi);\r\n};\r\n\r\n/**\r\n * Constructs new long bits from a number, long or string.\r\n * @param {Long|number|string} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.from = function from(value) {\r\n    if (typeof value === \"number\")\r\n        return LongBits.fromNumber(value);\r\n    if (util.isString(value)) {\r\n        /* istanbul ignore else */\r\n        if (util.Long)\r\n            value = util.Long.fromString(value);\r\n        else\r\n            return LongBits.fromNumber(parseInt(value, 10));\r\n    }\r\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a possibly unsafe JavaScript number.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {number} Possibly unsafe number\r\n */\r\nLongBits.prototype.toNumber = function toNumber(unsigned) {\r\n    if (!unsigned && this.hi >>> 31) {\r\n        var lo = ~this.lo + 1 >>> 0,\r\n            hi = ~this.hi     >>> 0;\r\n        if (!lo)\r\n            hi = hi + 1 >>> 0;\r\n        return -(lo + hi * 4294967296);\r\n    }\r\n    return this.lo + this.hi * 4294967296;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a long.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long} Long\r\n */\r\nLongBits.prototype.toLong = function toLong(unsigned) {\r\n    return util.Long\r\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\r\n        /* istanbul ignore next */\r\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\r\n};\r\n\r\nvar charCodeAt = String.prototype.charCodeAt;\r\n\r\n/**\r\n * Constructs new long bits from the specified 8 characters long hash.\r\n * @param {string} hash Hash\r\n * @returns {util.LongBits} Bits\r\n */\r\nLongBits.fromHash = function fromHash(hash) {\r\n    if (hash === zeroHash)\r\n        return zero;\r\n    return new LongBits(\r\n        ( charCodeAt.call(hash, 0)\r\n        | charCodeAt.call(hash, 1) << 8\r\n        | charCodeAt.call(hash, 2) << 16\r\n        | charCodeAt.call(hash, 3) << 24) >>> 0\r\n    ,\r\n        ( charCodeAt.call(hash, 4)\r\n        | charCodeAt.call(hash, 5) << 8\r\n        | charCodeAt.call(hash, 6) << 16\r\n        | charCodeAt.call(hash, 7) << 24) >>> 0\r\n    );\r\n};\r\n\r\n/**\r\n * Converts this long bits to a 8 characters long hash.\r\n * @returns {string} Hash\r\n */\r\nLongBits.prototype.toHash = function toHash() {\r\n    return String.fromCharCode(\r\n        this.lo        & 255,\r\n        this.lo >>> 8  & 255,\r\n        this.lo >>> 16 & 255,\r\n        this.lo >>> 24      ,\r\n        this.hi        & 255,\r\n        this.hi >>> 8  & 255,\r\n        this.hi >>> 16 & 255,\r\n        this.hi >>> 24\r\n    );\r\n};\r\n\r\n/**\r\n * Zig-zag encodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzEncode = function zzEncode() {\r\n    var mask =   this.hi >> 31;\r\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\r\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Zig-zag decodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzDecode = function zzDecode() {\r\n    var mask = -(this.lo & 1);\r\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\r\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Calculates the length of this longbits when encoded as a varint.\r\n * @returns {number} Length\r\n */\r\nLongBits.prototype.length = function length() {\r\n    var part0 =  this.lo,\r\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\r\n        part2 =  this.hi >>> 24;\r\n    return part2 === 0\r\n         ? part1 === 0\r\n           ? part0 < 16384\r\n             ? part0 < 128 ? 1 : 2\r\n             : part0 < 2097152 ? 3 : 4\r\n           : part1 < 16384\r\n             ? part1 < 128 ? 5 : 6\r\n             : part1 < 2097152 ? 7 : 8\r\n         : part2 < 128 ? 9 : 10;\r\n};\r\n", "\"use strict\";\r\nvar util = exports;\r\n\r\n// used to return a Promise where callback is omitted\r\nutil.asPromise = require(1);\r\n\r\n// converts to / from base64 encoded strings\r\nutil.base64 = require(2);\r\n\r\n// base class of rpc.Service\r\nutil.EventEmitter = require(3);\r\n\r\n// float handling accross browsers\r\nutil.float = require(4);\r\n\r\n// requires modules optionally and hides the call from bundlers\r\nutil.inquire = require(5);\r\n\r\n// converts to / from utf8 encoded strings\r\nutil.utf8 = require(7);\r\n\r\n// provides a node-like buffer pool in the browser\r\nutil.pool = require(6);\r\n\r\n// utility to work with the low and high bits of a 64 bit value\r\nutil.LongBits = require(14);\r\n\r\n// global object reference\r\nutil.global = typeof window !== \"undefined\" && window\r\n           || typeof global !== \"undefined\" && global\r\n           || typeof self   !== \"undefined\" && self\r\n           || this; // eslint-disable-line no-invalid-this\r\n\r\n/**\r\n * An immuable empty array.\r\n * @memberof util\r\n * @type {Array.<*>}\r\n * @const\r\n */\r\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\r\n\r\n/**\r\n * An immutable empty object.\r\n * @type {Object}\r\n * @const\r\n */\r\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\r\n\r\n/**\r\n * Whether running within node or not.\r\n * @memberof util\r\n * @type {boolean}\r\n * @const\r\n */\r\nutil.isNode = Boolean(util.global.process && util.global.process.versions && util.global.process.versions.node);\r\n\r\n/**\r\n * Tests if the specified value is an integer.\r\n * @function\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is an integer\r\n */\r\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\r\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a string.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a string\r\n */\r\nutil.isString = function isString(value) {\r\n    return typeof value === \"string\" || value instanceof String;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a non-null object.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a non-null object\r\n */\r\nutil.isObject = function isObject(value) {\r\n    return value && typeof value === \"object\";\r\n};\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * This is an alias of {@link util.isSet}.\r\n * @function\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isset =\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isSet = function isSet(obj, prop) {\r\n    var value = obj[prop];\r\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\r\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Any compatible Buffer instance.\r\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\r\n * @interface Buffer\r\n * @extends Uint8Array\r\n */\r\n\r\n/**\r\n * Node's Buffer class if available.\r\n * @type {Constructor<Buffer>}\r\n */\r\nutil.Buffer = (function() {\r\n    try {\r\n        var Buffer = util.inquire(\"buffer\").Buffer;\r\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\r\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\r\n    } catch (e) {\r\n        /* istanbul ignore next */\r\n        return null;\r\n    }\r\n})();\r\n\r\n// Internal alias of or polyfull for Buffer.from.\r\nutil._Buffer_from = null;\r\n\r\n// Internal alias of or polyfill for Buffer.allocUnsafe.\r\nutil._Buffer_allocUnsafe = null;\r\n\r\n/**\r\n * Creates a new buffer of whatever type supported by the environment.\r\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\r\n * @returns {Uint8Array|Buffer} Buffer\r\n */\r\nutil.newBuffer = function newBuffer(sizeOrArray) {\r\n    /* istanbul ignore next */\r\n    return typeof sizeOrArray === \"number\"\r\n        ? util.Buffer\r\n            ? util._Buffer_allocUnsafe(sizeOrArray)\r\n            : new util.Array(sizeOrArray)\r\n        : util.Buffer\r\n            ? util._Buffer_from(sizeOrArray)\r\n            : typeof Uint8Array === \"undefined\"\r\n                ? sizeOrArray\r\n                : new Uint8Array(sizeOrArray);\r\n};\r\n\r\n/**\r\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\r\n * @type {Constructor<Uint8Array>}\r\n */\r\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\r\n\r\n/**\r\n * Any compatible Long instance.\r\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\r\n * @interface Long\r\n * @property {number} low Low bits\r\n * @property {number} high High bits\r\n * @property {boolean} unsigned Whether unsigned or not\r\n */\r\n\r\n/**\r\n * Long.js's Long class if available.\r\n * @type {Constructor<Long>}\r\n */\r\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\r\n         || /* istanbul ignore next */ util.global.Long\r\n         || util.inquire(\"long\");\r\n\r\n/**\r\n * Regular expression used to verify 2 bit (`bool`) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key2Re = /^true|false|0|1$/;\r\n\r\n/**\r\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\r\n\r\n/**\r\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\r\n\r\n/**\r\n * Converts a number or long to an 8 characters long hash string.\r\n * @param {Long|number} value Value to convert\r\n * @returns {string} Hash\r\n */\r\nutil.longToHash = function longToHash(value) {\r\n    return value\r\n        ? util.LongBits.from(value).toHash()\r\n        : util.LongBits.zeroHash;\r\n};\r\n\r\n/**\r\n * Converts an 8 characters long hash string to a long or number.\r\n * @param {string} hash Hash\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long|number} Original value\r\n */\r\nutil.longFromHash = function longFromHash(hash, unsigned) {\r\n    var bits = util.LongBits.fromHash(hash);\r\n    if (util.Long)\r\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\r\n    return bits.toNumber(Boolean(unsigned));\r\n};\r\n\r\n/**\r\n * Merges the properties of the source object into the destination object.\r\n * @memberof util\r\n * @param {Object.<string,*>} dst Destination object\r\n * @param {Object.<string,*>} src Source object\r\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\r\n * @returns {Object.<string,*>} Destination object\r\n */\r\nfunction merge(dst, src, ifNotSet) { // used by converters\r\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\r\n        if (dst[keys[i]] === undefined || !ifNotSet)\r\n            dst[keys[i]] = src[keys[i]];\r\n    return dst;\r\n}\r\n\r\nutil.merge = merge;\r\n\r\n/**\r\n * Converts the first character of a string to lower case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.lcFirst = function lcFirst(str) {\r\n    return str.charAt(0).toLowerCase() + str.substring(1);\r\n};\r\n\r\n/**\r\n * Creates a custom error constructor.\r\n * @memberof util\r\n * @param {string} name Error name\r\n * @returns {Constructor<Error>} Custom error constructor\r\n */\r\nfunction newError(name) {\r\n\r\n    function CustomError(message, properties) {\r\n\r\n        if (!(this instanceof CustomError))\r\n            return new CustomError(message, properties);\r\n\r\n        // Error.call(this, message);\r\n        // ^ just returns a new error instance because the ctor can be called as a function\r\n\r\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\r\n\r\n        /* istanbul ignore next */\r\n        if (Error.captureStackTrace) // node\r\n            Error.captureStackTrace(this, CustomError);\r\n        else\r\n            Object.defineProperty(this, \"stack\", { value: (new Error()).stack || \"\" });\r\n\r\n        if (properties)\r\n            merge(this, properties);\r\n    }\r\n\r\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\r\n\r\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\r\n\r\n    CustomError.prototype.toString = function toString() {\r\n        return this.name + \": \" + this.message;\r\n    };\r\n\r\n    return CustomError;\r\n}\r\n\r\nutil.newError = newError;\r\n\r\n/**\r\n * Constructs a new protocol error.\r\n * @classdesc Error subclass indicating a protocol specifc error.\r\n * @memberof util\r\n * @extends Error\r\n * @template T extends Message<T>\r\n * @constructor\r\n * @param {string} message Error message\r\n * @param {Object.<string,*>} [properties] Additional properties\r\n * @example\r\n * try {\r\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\r\n * } catch (e) {\r\n *     if (e instanceof ProtocolError && e.instance)\r\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\r\n * }\r\n */\r\nutil.ProtocolError = newError(\"ProtocolError\");\r\n\r\n/**\r\n * So far decoded message instance.\r\n * @name util.ProtocolError#instance\r\n * @type {Message<T>}\r\n */\r\n\r\n/**\r\n * A OneOf getter as returned by {@link util.oneOfGetter}.\r\n * @typedef OneOfGetter\r\n * @type {function}\r\n * @returns {string|undefined} Set field name, if any\r\n */\r\n\r\n/**\r\n * Builds a getter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfGetter} Unbound getter\r\n */\r\nutil.oneOfGetter = function getOneOf(fieldNames) {\r\n    var fieldMap = {};\r\n    for (var i = 0; i < fieldNames.length; ++i)\r\n        fieldMap[fieldNames[i]] = 1;\r\n\r\n    /**\r\n     * @returns {string|undefined} Set field name, if any\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function() { // eslint-disable-line consistent-return\r\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\r\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\r\n                return keys[i];\r\n    };\r\n};\r\n\r\n/**\r\n * A OneOf setter as returned by {@link util.oneOfSetter}.\r\n * @typedef OneOfSetter\r\n * @type {function}\r\n * @param {string|undefined} value Field name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Builds a setter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfSetter} Unbound setter\r\n */\r\nutil.oneOfSetter = function setOneOf(fieldNames) {\r\n\r\n    /**\r\n     * @param {string} name Field name\r\n     * @returns {undefined}\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function(name) {\r\n        for (var i = 0; i < fieldNames.length; ++i)\r\n            if (fieldNames[i] !== name)\r\n                delete this[fieldNames[i]];\r\n    };\r\n};\r\n\r\n/**\r\n * Default conversion options used for {@link Message#toJSON} implementations.\r\n *\r\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\r\n *\r\n * - Longs become strings\r\n * - Enums become string keys\r\n * - Bytes become base64 encoded strings\r\n * - (Sub-)Messages become plain objects\r\n * - Maps become plain objects with all string keys\r\n * - Repeated fields become arrays\r\n * - NaN and Infinity for float and double fields become strings\r\n *\r\n * @type {IConversionOptions}\r\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\r\n */\r\nutil.toJSONOptions = {\r\n    longs: String,\r\n    enums: String,\r\n    bytes: String,\r\n    json: true\r\n};\r\n\r\n// Sets up buffer utility according to the environment (called in index-minimal)\r\nutil._configure = function() {\r\n    var Buffer = util.Buffer;\r\n    /* istanbul ignore if */\r\n    if (!Buffer) {\r\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\r\n        return;\r\n    }\r\n    // because node 4.x buffers are incompatible & immutable\r\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\r\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\r\n        /* istanbul ignore next */\r\n        function Buffer_from(value, encoding) {\r\n            return new Buffer(value, encoding);\r\n        };\r\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\r\n        /* istanbul ignore next */\r\n        function Buffer_allocUnsafe(size) {\r\n            return new Buffer(size);\r\n        };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Writer;\r\n\r\nvar util      = require(15);\r\n\r\nvar BufferWriter; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    base64    = util.base64,\r\n    utf8      = util.utf8;\r\n\r\n/**\r\n * Constructs a new writer operation instance.\r\n * @classdesc Scheduled writer operation.\r\n * @constructor\r\n * @param {function(*, Uint8Array, number)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {*} val Value to write\r\n * @ignore\r\n */\r\nfunction Op(fn, len, val) {\r\n\r\n    /**\r\n     * Function to call.\r\n     * @type {function(Uint8Array, number, *)}\r\n     */\r\n    this.fn = fn;\r\n\r\n    /**\r\n     * Value byte length.\r\n     * @type {number}\r\n     */\r\n    this.len = len;\r\n\r\n    /**\r\n     * Next operation.\r\n     * @type {Writer.Op|undefined}\r\n     */\r\n    this.next = undefined;\r\n\r\n    /**\r\n     * Value to write.\r\n     * @type {*}\r\n     */\r\n    this.val = val; // type varies\r\n}\r\n\r\n/* istanbul ignore next */\r\nfunction noop() {} // eslint-disable-line no-empty-function\r\n\r\n/**\r\n * Constructs a new writer state instance.\r\n * @classdesc Copied writer state.\r\n * @memberof Writer\r\n * @constructor\r\n * @param {Writer} writer Writer to copy state from\r\n * @ignore\r\n */\r\nfunction State(writer) {\r\n\r\n    /**\r\n     * Current head.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.head = writer.head;\r\n\r\n    /**\r\n     * Current tail.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.tail = writer.tail;\r\n\r\n    /**\r\n     * Current buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = writer.len;\r\n\r\n    /**\r\n     * Next state.\r\n     * @type {State|null}\r\n     */\r\n    this.next = writer.states;\r\n}\r\n\r\n/**\r\n * Constructs a new writer instance.\r\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n */\r\nfunction Writer() {\r\n\r\n    /**\r\n     * Current length.\r\n     * @type {number}\r\n     */\r\n    this.len = 0;\r\n\r\n    /**\r\n     * Operations head.\r\n     * @type {Object}\r\n     */\r\n    this.head = new Op(noop, 0, 0);\r\n\r\n    /**\r\n     * Operations tail\r\n     * @type {Object}\r\n     */\r\n    this.tail = this.head;\r\n\r\n    /**\r\n     * Linked forked states.\r\n     * @type {Object|null}\r\n     */\r\n    this.states = null;\r\n\r\n    // When a value is written, the writer calculates its byte length and puts it into a linked\r\n    // list of operations to perform when finish() is called. This both allows us to allocate\r\n    // buffers of the exact required size and reduces the amount of work we have to do compared\r\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\r\n    // part is just a linked list walk calling operations with already prepared values.\r\n}\r\n\r\n/**\r\n * Creates a new writer.\r\n * @function\r\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\r\n */\r\nWriter.create = util.Buffer\r\n    ? function create_buffer_setup() {\r\n        return (Writer.create = function create_buffer() {\r\n            return new BufferWriter();\r\n        })();\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array() {\r\n        return new Writer();\r\n    };\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\nWriter.alloc = function alloc(size) {\r\n    return new util.Array(size);\r\n};\r\n\r\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\r\n/* istanbul ignore else */\r\nif (util.Array !== Array)\r\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\r\n\r\n/**\r\n * Pushes a new operation to the queue.\r\n * @param {function(Uint8Array, number, *)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @returns {Writer} `this`\r\n * @private\r\n */\r\nWriter.prototype._push = function push(fn, len, val) {\r\n    this.tail = this.tail.next = new Op(fn, len, val);\r\n    this.len += len;\r\n    return this;\r\n};\r\n\r\nfunction writeByte(val, buf, pos) {\r\n    buf[pos] = val & 255;\r\n}\r\n\r\nfunction writeVarint32(val, buf, pos) {\r\n    while (val > 127) {\r\n        buf[pos++] = val & 127 | 128;\r\n        val >>>= 7;\r\n    }\r\n    buf[pos] = val;\r\n}\r\n\r\n/**\r\n * Constructs a new varint writer operation instance.\r\n * @classdesc Scheduled varint writer operation.\r\n * @extends Op\r\n * @constructor\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @ignore\r\n */\r\nfunction VarintOp(len, val) {\r\n    this.len = len;\r\n    this.next = undefined;\r\n    this.val = val;\r\n}\r\n\r\nVarintOp.prototype = Object.create(Op.prototype);\r\nVarintOp.prototype.fn = writeVarint32;\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as a varint.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.uint32 = function write_uint32(value) {\r\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\r\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\r\n    this.len += (this.tail = this.tail.next = new VarintOp(\r\n        (value = value >>> 0)\r\n                < 128       ? 1\r\n        : value < 16384     ? 2\r\n        : value < 2097152   ? 3\r\n        : value < 268435456 ? 4\r\n        :                     5,\r\n    value)).len;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as a varint.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.int32 = function write_int32(value) {\r\n    return value < 0\r\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\r\n        : this.uint32(value);\r\n};\r\n\r\n/**\r\n * Writes a 32 bit value as a varint, zig-zag encoded.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sint32 = function write_sint32(value) {\r\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\r\n};\r\n\r\nfunction writeVarint64(val, buf, pos) {\r\n    while (val.hi) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\r\n        val.hi >>>= 7;\r\n    }\r\n    while (val.lo > 127) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = val.lo >>> 7;\r\n    }\r\n    buf[pos++] = val.lo;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as a varint.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.uint64 = function write_uint64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.int64 = Writer.prototype.uint64;\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sint64 = function write_sint64(value) {\r\n    var bits = LongBits.from(value).zzEncode();\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a boolish value as a varint.\r\n * @param {boolean} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bool = function write_bool(value) {\r\n    return this._push(writeByte, 1, value ? 1 : 0);\r\n};\r\n\r\nfunction writeFixed32(val, buf, pos) {\r\n    buf[pos    ] =  val         & 255;\r\n    buf[pos + 1] =  val >>> 8   & 255;\r\n    buf[pos + 2] =  val >>> 16  & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as fixed 32 bits.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fixed32 = function write_fixed32(value) {\r\n    return this._push(writeFixed32, 4, value >>> 0);\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as fixed 32 bits.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as fixed 64 bits.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.fixed64 = function write_fixed64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as fixed 64 bits.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\r\n\r\n/**\r\n * Writes a float (32 bit).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.float = function write_float(value) {\r\n    return this._push(util.float.writeFloatLE, 4, value);\r\n};\r\n\r\n/**\r\n * Writes a double (64 bit float).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.double = function write_double(value) {\r\n    return this._push(util.float.writeDoubleLE, 8, value);\r\n};\r\n\r\nvar writeBytes = util.Array.prototype.set\r\n    ? function writeBytes_set(val, buf, pos) {\r\n        buf.set(val, pos); // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytes_for(val, buf, pos) {\r\n        for (var i = 0; i < val.length; ++i)\r\n            buf[pos + i] = val[i];\r\n    };\r\n\r\n/**\r\n * Writes a sequence of bytes.\r\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bytes = function write_bytes(value) {\r\n    var len = value.length >>> 0;\r\n    if (!len)\r\n        return this._push(writeByte, 1, 0);\r\n    if (util.isString(value)) {\r\n        var buf = Writer.alloc(len = base64.length(value));\r\n        base64.decode(value, buf, 0);\r\n        value = buf;\r\n    }\r\n    return this.uint32(len)._push(writeBytes, len, value);\r\n};\r\n\r\n/**\r\n * Writes a string.\r\n * @param {string} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.string = function write_string(value) {\r\n    var len = utf8.length(value);\r\n    return len\r\n        ? this.uint32(len)._push(utf8.write, len, value)\r\n        : this._push(writeByte, 1, 0);\r\n};\r\n\r\n/**\r\n * Forks this writer's state by pushing it to a stack.\r\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fork = function fork() {\r\n    this.states = new State(this);\r\n    this.head = this.tail = new Op(noop, 0, 0);\r\n    this.len = 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets this instance to the last state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.reset = function reset() {\r\n    if (this.states) {\r\n        this.head   = this.states.head;\r\n        this.tail   = this.states.tail;\r\n        this.len    = this.states.len;\r\n        this.states = this.states.next;\r\n    } else {\r\n        this.head = this.tail = new Op(noop, 0, 0);\r\n        this.len  = 0;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.ldelim = function ldelim() {\r\n    var head = this.head,\r\n        tail = this.tail,\r\n        len  = this.len;\r\n    this.reset().uint32(len);\r\n    if (len) {\r\n        this.tail.next = head.next; // skip noop\r\n        this.tail = tail;\r\n        this.len += len;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @returns {Uint8Array} Finished buffer\r\n */\r\nWriter.prototype.finish = function finish() {\r\n    var head = this.head.next, // skip noop\r\n        buf  = this.constructor.alloc(this.len),\r\n        pos  = 0;\r\n    while (head) {\r\n        head.fn(head.val, buf, pos);\r\n        pos += head.len;\r\n        head = head.next;\r\n    }\r\n    // this.head = this.tail = null;\r\n    return buf;\r\n};\r\n\r\nWriter._configure = function(BufferWriter_) {\r\n    BufferWriter = BufferWriter_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erWriter;\r\n\r\n// extends Writer\r\nvar Writer = require(16);\r\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\r\n\r\nvar util = require(15);\r\n\r\nvar Buffer = util.Buffer;\r\n\r\n/**\r\n * Constructs a new buffer writer instance.\r\n * @classdesc Wire format writer using node buffers.\r\n * @extends Writer\r\n * @constructor\r\n */\r\nfunction BufferWriter() {\r\n    Writer.call(this);\r\n}\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Buffer} Buffer\r\n */\r\nBufferWriter.alloc = function alloc_buffer(size) {\r\n    return (BufferWriter.alloc = util._Buffer_allocUnsafe)(size);\r\n};\r\n\r\nvar writeBytesBuffer = Buffer && Buffer.prototype instanceof Uint8Array && Buffer.prototype.set.name === \"set\"\r\n    ? function writeBytesBuffer_set(val, buf, pos) {\r\n        buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\r\n                           // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytesBuffer_copy(val, buf, pos) {\r\n        if (val.copy) // Buffer values\r\n            val.copy(buf, pos, 0, val.length);\r\n        else for (var i = 0; i < val.length;) // plain array values\r\n            buf[pos++] = val[i++];\r\n    };\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\r\n    if (util.isString(value))\r\n        value = util._Buffer_from(value, \"base64\");\r\n    var len = value.length >>> 0;\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeBytesBuffer, len, value);\r\n    return this;\r\n};\r\n\r\nfunction writeStringBuffer(val, buf, pos) {\r\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\r\n        util.utf8.write(val, buf, pos);\r\n    else\r\n        buf.utf8Write(val, pos);\r\n}\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.string = function write_string_buffer(value) {\r\n    var len = Buffer.byteLength(value);\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeStringBuffer, len, value);\r\n    return this;\r\n};\r\n\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @name BufferWriter#finish\r\n * @function\r\n * @returns {Buffer} Finished buffer\r\n */\r\n"], "sourceRoot": "."}