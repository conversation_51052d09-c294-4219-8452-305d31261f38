RegisterCommand('robtransport', function(source, args, rawCommand)
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsUsing(playerPed, false)

    if vehicle == 0 then
        ShowNotification("Vous n'êtes pas dans un véhicule.", 5000)
        return
    end

    local model = GetEntityModel(vehicle)
    if not IsThisModelATrain(model) then
        ShowNotification("Vous devez être sur un train pour utiliser cette commande.", 5000)
        return
    end

    -- La vérification de faction est gérée côté serveur.

    print("Attempting to rob the train...")
    TriggerServerEvent('sheriffs_vs_outlaws:startTrainRobbery')
end, false)

-- Les notifications sont maintenant gérées par le serveur via 'sheriffs_vs_outlaws:showNotification'
-- et la fonction ShowNotification globale dans cl_main.lua
-- Aucun handler spécifique n'est requis ici pour les messages simples.