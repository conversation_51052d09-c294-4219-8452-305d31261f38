rdr3_warning 'I acknowledge that this is a prerelease build of RedM, and I am aware my resources *will* become incompatible once RedM ships.'
fx_version 'cerulean'
game 'rdr3'
author 'Tysor'
description 'Fonctionnalités de base pour les gamemodes (MainCore)'
version '1.0.0'

server_scripts {
    'server/sv_player_manager.lua',
    'server/sv_maincore.lua'
}

client_scripts {
    'client/cl_maincore.lua',
    'client/cl_player_actions.lua'
}

shared_scripts {
    -- Ajoutez ici des scripts partagés si vous en avez, par exemple un fichier de configuration commun.
    -- '@ox_lib/init.lua', -- Exemple si ox_lib est utilisé et partagé
}

exports {
    'GetPlayerFaction',
    'UpdatePlayerMoney',
    'UpdatePlayerDirtyMoney',
    'AddPlayerXP',
    'UpdatePlayerBounty',
    'GetPlayerBounty',
    'GetPlayersByFaction',
    'SetPlayerJailedStatus', -- A<PERSON><PERSON> pour sv_arrest
    'ResetPlayerBounty',     -- A<PERSON><PERSON> pour sv_arrest
    'ResetPlayerBountyAlertLevel', -- <PERSON><PERSON><PERSON> pour sv_arrest
    'IsPlayerJailed',
    'GetPlayerJailReleaseTime',
    'GetAllPlayersData',
    'GetPlayerMoney',
    'GetPlayerRank',
    'GetPlayerDirtyMoney',   -- Ajouté pour sv_theft
    'GetPlayerWeapons',      -- Ajouté pour sv_penalties
    'RemovePlayerWeapon',    -- Ajouté pour sv_penalties
    'GetCoreObject'
}

-- Si MainCore a des dépendances (par exemple, ox_lib si vous l'utilisez pour des UI ou des fonctions utilitaires)
-- dependencies {
--     'ox_lib'
-- }

-- Pour l'instant, MainCore n'a pas de dépendances directes listées ici,
-- mais il est conçu pour être une dépendance pour d'autres ressources.