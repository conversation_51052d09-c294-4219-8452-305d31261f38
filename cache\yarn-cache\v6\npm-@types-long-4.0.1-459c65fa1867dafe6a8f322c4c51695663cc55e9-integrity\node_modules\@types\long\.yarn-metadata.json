{"manifest": {"name": "@types/long", "version": "4.0.1", "description": "TypeScript definitions for long.js", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/peter<PERSON><PERSON>jmans"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/long"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5a2ae1424989c49d7303e1f5cc510288bfab1e71e0e2143cdcb9d24ff1c3dc8e", "typeScriptVersion": "2.8", "_registry": "npm", "_loc": "D:\\Redm\\txData\\RedMBasicServerCFXDefault_1DC7C9.base\\cache\\yarn-cache\\v6\\npm-@types-long-4.0.1-459c65fa1867dafe6a8f322c4c51695663cc55e9-integrity\\node_modules\\@types\\long\\package.json", "readmeFilename": "README.md", "readme": "# Installation\n> `npm install --save @types/long`\n\n# Summary\nThis package contains type definitions for long.js (https://github.com/dcodeIO/long.js).\n\n# Details\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/long.\n\n### Additional Details\n * Last updated: Wed, 22 Jan 2020 19:19:46 GMT\n * Dependencies: none\n * Global values: `Long`\n\n# Credits\nThese definitions were written by <PERSON> (https://github.com/peter<PERSON><PERSON><PERSON>).\n", "licenseText": "    MIT License\n\n    Copyright (c) Microsoft Corporation. All rights reserved.\n\n    Permission is hereby granted, free of charge, to any person obtaining a copy\n    of this software and associated documentation files (the \"Software\"), to deal\n    in the Software without restriction, including without limitation the rights\n    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the Software is\n    furnished to do so, subject to the following conditions:\n\n    The above copyright notice and this permission notice shall be included in all\n    copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n    AUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n    SOFTWARE\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@types/long/-/long-4.0.1.tgz#459c65fa1867dafe6a8f322c4c51695663cc55e9", "type": "tarball", "reference": "https://registry.yarnpkg.com/@types/long/-/long-4.0.1.tgz", "hash": "459c65fa1867dafe6a8f322c4c51695663cc55e9", "integrity": "sha512-5tXH6Bx/kNGd3MgffdmP4dy2Z+G4eaXw0SE81Tq3BNadtnMR5/ySMzX4SLEzHJzSmPNn4HIdpQsBvXMUykr58w==", "registry": "npm", "packageName": "@types/long", "cacheIntegrity": "sha512-5tXH6Bx/kNGd3MgffdmP4dy2Z+G4eaXw0SE81Tq3BNadtnMR5/ySMzX4SLEzHJzSmPNn4HIdpQsBvXMUykr58w== sha1-RZxl+hhn2v5qjzIsTFFpVmPMVek="}, "registry": "npm", "hash": "459c65fa1867dafe6a8f322c4c51695663cc55e9"}