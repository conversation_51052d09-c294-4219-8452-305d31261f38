--[[
********************************************************************************
* cl_manhunt.lua - Logique client pour l'événement de Chasse à l'Homme         *
********************************************************************************
]]

local isManhuntActiveForPlayer = false
local targetBlip = nil
local manhuntTargetId = nil
local manhuntTimerDisplay = 0 -- Pour afficher le temps restant

-- Fonction pour afficher les notifications (placeholder, à adapter à votre UI)
local function ShowNotification(message)
    -- Exemple : exports['mythic_notify']:SendAlert('inform', message, 5000)
    -- Ou utiliser le chat : TriggerEvent('chat:addMessage', { args = { '^2[CHASSE]', message } })
    print(string.format("Notification Client: %s", message))
    -- Pour RedM, vous pourriez utiliser des fonctions natives pour afficher du texte à l'écran.
    -- Par exemple, avec Wait(0) dans un thread :
    -- SetTextFontForCurrentCommand(0)
    -- SetTextScale(0.0, 0.4)
    -- SetTextColour(255, 255, 255, 255)
    -- SetTextDropshadow(0, 0, 0, 0, 255)
    -- SetTextEdge(1, 0, 0, 0, 255)
    -- SetTextCentre(true)
    -- DisplayText("STRING", 0.5, 0.1) -- (text, x, y)
    -- AddTextComponentSubstringPlayerName(message)
    -- EndTextCommandDisplayText()
end

-- Gestion du blip de la cible
local function CreateOrUpdateTargetBlip(targetServerId)
    manhuntTargetId = targetServerId
    local targetPed = GetPlayerPedFromServerId(targetServerId)

    if not DoesEntityExist(targetPed) then
        if targetBlip then RemoveBlip(targetBlip) end
        targetBlip = nil
        return
    end

    if targetBlip and DoesBlipExist(targetBlip) then
        -- Le blip existe déjà, on met juste à jour sa position (si nécessaire, sinon RedM le fait)
        -- SetBlipCoords(targetBlip, GetEntityCoords(targetPed)) -- Normalement pas nécessaire pour les blips attachés à une entité
    else
        if targetBlip then RemoveBlip(targetBlip) end -- S'assure qu'un ancien blip est retiré

        targetBlip = AddBlipForEntity(targetPed)
        SetBlipSprite(targetBlip, GetHashKey("BLIP_STYLE_ENEMY")) -- Sprite de blip "ennemi"
        SetBlipColour(targetBlip, 1) -- Rouge
        SetBlipScale(targetBlip, 1.5)
        SetBlipAsShortRange(targetBlip, false)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentSubstringPlayerName("Cible de la Chasse")
        EndTextCommandSetBlipName(targetBlip)
        SetBlipDisplay(targetBlip, 2) -- Affiche sur la carte principale et le radar
        print(string.format("Blip créé/mis à jour pour la cible %d", targetServerId))
    end
end

local function RemoveTargetBlip()
    if targetBlip and DoesBlipExist(targetBlip) then
        RemoveBlip(targetBlip)
        targetBlip = nil
        print("Blip de la cible retiré.")
    end
    manhuntTargetId = nil
end

-- Événements serveur pour gérer la Chasse à l'Homme côté client
RegisterNetEvent('sheriffs_vs_outlaws:manhunt_notifyRanger')
AddEventHandler('sheriffs_vs_outlaws:manhunt_notifyRanger', function(message, targetSrvId)
    ShowNotification(message)
    isManhuntActiveForPlayer = true
    manhuntTimerDisplay = Config.ManhuntDuration -- Initialise le timer pour l'affichage
    if targetSrvId then
        CreateOrUpdateTargetBlip(targetSrvId)
    end
end)

RegisterNetEvent('sheriffs_vs_outlaws:manhunt_notifyTarget')
AddEventHandler('sheriffs_vs_outlaws:manhunt_notifyTarget', function(message)
    ShowNotification(message)
    isManhuntActiveForPlayer = true
    manhuntTimerDisplay = Config.ManhuntDuration -- Initialise le timer pour l'affichage
end)

RegisterNetEvent('sheriffs_vs_outlaws:manhunt_updateTargetBlip')
AddEventHandler('sheriffs_vs_outlaws:manhunt_updateTargetBlip', function(targetSrvId)
    if isManhuntActiveForPlayer and GetPlayerPedFromServerId(manhuntTargetId) then -- Vérifie si le joueur est toujours un ranger actif dans la chasse
        CreateOrUpdateTargetBlip(targetSrvId)
    end
end)

RegisterNetEvent('sheriffs_vs_outlaws:manhunt_removeTargetBlip')
AddEventHandler('sheriffs_vs_outlaws:manhunt_removeTargetBlip', function()
    RemoveTargetBlip()
    isManhuntActiveForPlayer = false
    manhuntTimerDisplay = 0
end)

RegisterNetEvent('sheriffs_vs_outlaws:manhunt_ended')
AddEventHandler('sheriffs_vs_outlaws:manhunt_ended', function(message)
    ShowNotification(message)
    RemoveTargetBlip()
    isManhuntActiveForPlayer = false
    manhuntTimerDisplay = 0
end)


-- Thread pour afficher le temps restant (optionnel, basique)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Toutes les secondes
        if isManhuntActiveForPlayer and manhuntTimerDisplay > 0 then
            manhuntTimerDisplay = manhuntTimerDisplay - 1

            -- Afficher le timer à l'écran
            -- Ceci est un exemple très basique. Vous voudrez une UI plus propre.
            SetTextFontForCurrentCommand(0)
            SetTextScale(0.0, 0.45)
            SetTextColour(255, 165, 0, 255) -- Orange
            SetTextDropshadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextCentre(false) -- Alignement à gauche
            -- Positionner en bas à droite par exemple
            local xPos = 0.85 
            local yPos = 0.95
            
            local minutes = math.floor(manhuntTimerDisplay / 60)
            local seconds = manhuntTimerDisplay % 60
            local timerText = string.format("CHASSE A L'HOMME: %02d:%02d", minutes, seconds)

            if manhuntTargetId and GetPlayerPedFromServerId(manhuntTargetId) == PlayerPedId() then
                 timerText = string.format("SURVIVRE: %02d:%02d", minutes, seconds)
            end

            BeginTextCommandDisplayText("STRING")
            AddTextComponentSubstringPlayerName(timerText)
            EndTextCommandDisplayText(xPos, yPos)

            if manhuntTimerDisplay <= 0 then
                isManhuntActiveForPlayer = false -- Le serveur devrait envoyer un événement pour confirmer la fin
            end
        end
    end
end)

-- S'assurer que le blip est retiré si le script est rechargé ou arrêté
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        RemoveTargetBlip()
    end
end)

print("cl_manhunt.lua chargé.")

-- Tentative de récupération de la config côté client si nécessaire
-- Cela suppose que Config est partagé ou envoyé au client.
-- Si Config.ManhuntDuration n'est pas disponible ici, il faudra l'envoyer via un événement.
Citizen.CreateThread(function()
    Citizen.Wait(1000) -- Attendre que les scripts serveur soient potentiellement chargés
    if Config == nil then Config = {} end -- S'assurer que la table Config existe
    if Config.ManhuntDuration == nil then
        -- Demander la config au serveur ou utiliser une valeur par défaut
        -- TriggerServerEvent('sheriffs_vs_outlaws:requestManhuntConfig')
        -- Pour l'instant, on met une valeur par défaut si non trouvée, mais elle devrait être synchro
        Config.ManhuntDuration = 600 -- Valeur par défaut au cas où
        print("Config.ManhuntDuration non trouvée côté client, utilisation de la valeur par défaut.")
    end
end)

-- RegisterNetEvent('sheriffs_vs_outlaws:receiveManhuntConfig')
-- AddEventHandler('sheriffs_vs_outlaws:receiveManhuntConfig', function(serverConfig)
-- Config.ManhuntDuration = serverConfig.ManhuntDuration
-- print("Configuration de la chasse à l'homme reçue du serveur.")
-- end)