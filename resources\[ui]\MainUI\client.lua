local money = 0
local showUI = true

-- Commande pour obtenir les coordonnées
RegisterCommand('getcoords', function()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = true,
        args = {"Système", string.format("Coordonnées: X=%.2f, Y=%.2f, Z=%.2f, Heading=%.2f", coords.x, coords.y, coords.z, heading)}
    })
end)

-- Commande pour basculer l'interface
RegisterCommand('toggleui', function()
    showUI = not showUI
    SendNUIMessage({
        type = "toggleUI",
        show = showUI
    })
end)

-- Mise à jour de l'argent volé
RegisterNetEvent('updateStolenMoney')
AddEventHandler('updateStolenMoney', function(newAmount)
    money = newAmount
    SendNUIMessage({
        type = "updateMoney",
        amount = money
    })
end)

-- Affichage de l'interface
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if showUI then
            SendNUIMessage({
                type = "updateUI",
                money = money
            })
        end
    end
end) 