document.addEventListener('DOMContentLoaded', () => {
    const factionMenu = document.getElementById('factionMenu');
    const joinSheriffsButton = document.getElementById('joinSheriffs');
    const joinOutlawsButton = document.getElementById('joinOutlaws');

    // Fonction pour afficher le menu
    function showMenu() {
        if (factionMenu) {
            factionMenu.style.display = 'flex'; // Ou 'block', selon le style CSS
        }
    }

    // Fonction pour masquer le menu
    function hideMenu() {
        if (factionMenu) {
            factionMenu.style.display = 'none';
        }
    }

    // Gestionnaire de clic pour le bouton Sheriffs
    if (joinSheriffsButton) {
        joinSheriffsButton.addEventListener('click', () => {
            selectFaction('sheriffs');
        });
    }

    // Gestionnaire de clic pour le bouton Outlaws
    if (joinOutlawsButton) {
        joinOutlawsButton.addEventListener('click', () => {
            selectFaction('outlaws');
        });
    }

    // Fonction pour envoyer la faction choisie à Lua
    async function selectFaction(factionName) {
        hideMenu();
        try {
            const resp = await fetch(`https://${GetParentResourceName()}/selectFaction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({ faction: factionName }),
            });
            const data = await resp.json();
            console.log('Faction selected:', factionName, 'Response:', data);
        } catch (error) {
            console.error('Error selecting faction:', error);
        }
    }

    // Écouteur d'événements pour les messages de Lua
    window.addEventListener('message', function(event) {
        if (event.data.action === 'showFactionMenu') {
            showMenu();
        }
    });

    // Pour tester l'affichage directement dans le navigateur (à commenter/supprimer pour le jeu)
    // showMenu(); 
});