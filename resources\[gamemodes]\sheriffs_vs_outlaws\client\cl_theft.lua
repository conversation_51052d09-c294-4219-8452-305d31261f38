print('^2[SvsO - Theft] Le script cl_theft.lua a démarré.^0')

local nearNPC = nil
local nearFence = nil
local nearLaunderer = nil
local interactionDistance = 2.0 -- Distance pour interagir avec les PNJ/points

-- Fonction pour afficher les notifications (utilise le système de notification de RedM)
local notificationQueue = {}
local notificationActive = false

function ShowNotification(text, duration)
    table.insert(notificationQueue, {text = text, duration = duration or 5000})
    ProcessNotificationQueue()
end

function ProcessNotificationQueue()
    if notificationActive or #notificationQueue == 0 then
        return
    end

    notificationActive = true
    local notif = table.remove(notificationQueue, 1)

    local scaleform = Citizen.InvokeNative(0x25996527E59CD47B, "INGAME_WARNING_MESSAGE", Citizen.ResultAsInteger())
    while not Citizen.InvokeNative(0xBD06C611BB958794, scaleform) do
        Citizen.Wait(0)
    end

    Citizen.InvokeNative(0x5E30FD72914A3868, scaleform, "SET_WARNING_MESSAGE")
    Citizen.InvokeNative(0x04A138A8084396F9, "STRING")
    Citizen.InvokeNative(0x3093A432A082F156, notif.text)
    Citizen.InvokeNative(0x362E2D3FE93A9959)
    Citizen.InvokeNative(0xCF537FDE4FBD4613, 100, true, true)

    local startTime = GetGameTimer()
    CreateThread(function()
        while GetGameTimer() - startTime < notif.duration do
            Citizen.InvokeNative(0xDD03FCB40746F31A, scaleform, 255, 255, 255, 255)
            Citizen.Wait(0)
        end
        Citizen.InvokeNative(0x1D4B1610A8E2E063, scaleform)
        notificationActive = false
        ProcessNotificationQueue()
    end)
end

-- Fonction pour obtenir le PNJ le plus proche (simplifié)
function GetClosestNPC()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local closestDistance = -1
    local closestPed = nil

    for _, ped in ipairs(GetGamePool('CPed')) do
        if DoesEntityExist(ped) and not IsPedAPlayer(ped) and not IsEntityDead(ped) then
            -- Exclure les PNJ importants ou hostiles si nécessaire (logique à ajouter)
            -- if IsPedHostile(ped, playerPed, false) or IsEntityAMissionEntity(ped) then continue end
            
            local pedCoords = GetEntityCoords(ped)
            local distance = #(playerCoords - pedCoords)

            if closestDistance == -1 or distance < closestDistance then
                closestDistance = distance
                closestPed = ped
            end
        end
    end

    if closestDistance ~= -1 and closestDistance < interactionDistance then
        return closestPed
    end
    return nil
end

-- Thread pour gérer la détection de proximité et les interactions
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- Vérifier toutes les 500ms
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local currentNearNPC = nil
        local currentNearFence = nil
        local currentNearLaunderer = nil

        -- Détection PNJ pour vol
        currentNearNPC = GetClosestNPC()
        if currentNearNPC then
            nearNPC = currentNearNPC
            -- Afficher un hint si on veut (ex: "Appuyez sur [E] pour voler")
            -- ShowHint("Appuyez sur [G] pour tenter de voler ce PNJ.") -- Utiliser une touche différente pour le vol
        else
            nearNPC = nil
        end

        -- Détection Receleur
        for i, fenceData in ipairs(Config.FenceLocations or {}) do
            local distanceToFence = #(playerCoords - fenceData.coords)
            if distanceToFence < interactionDistance then
                currentNearFence = i -- Stocker l'ID du receleur
                -- ShowHint("Appuyez sur [E] pour parler au Receleur.")
                break
            end
        end
        nearFence = currentNearFence

        -- Détection Blanchisseur
        for i, laundererData in ipairs(Config.MoneyLaunderingLocations or {}) do
            local distanceToLaunderer = #(playerCoords - laundererData.coords)
            if distanceToLaunderer < interactionDistance then
                currentNearLaunderer = i -- Stocker l'ID du blanchisseur
                -- ShowHint("Appuyez sur [E] pour blanchir de l'argent.")
                break
            end
        end
        nearLaunderer = currentNearLaunderer
    end
end)

-- Thread pour gérer les inputs
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if IsControlJustPressed(0, 0x760A9C6C) then -- Touche E (INPUT_CONTEXT)
            if nearFence then
                ShowNotification("Vous interagissez avec le receleur. Vente en cours...") -- UI à venir
                TriggerServerEvent('sheriffs_vs_outlaws:sellStolenGoods', nearFence)
            elseif nearLaunderer then
                ShowNotification("Vous interagissez avec le blanchisseur. Blanchiment en cours...") -- UI à venir
                TriggerServerEvent('sheriffs_vs_outlaws:launderMoney', nearLaunderer)
            end
        end

        if IsControlJustPressed(0, 0xCEFD9220) then -- Touche G (INPUT_VEH_DUCK) - Exemple pour le vol
            if nearNPC then
                local npcNetId = PedToNet(nearNPC)
                if npcNetId ~= 0 then
                    ShowNotification("Vous tentez de voler le PNJ...")
                    TriggerServerEvent('sheriffs_vs_outlaws:attemptTheftOnNPC', npcNetId)
                else
                    ShowNotification("Impossible de cibler ce PNJ pour le vol.")
                end
            end
        end
    end
end)

-- Commande pour voir l'inventaire
RegisterCommand('inventory', function(source, args, rawCommand)
    -- Pour l'instant, on demande au serveur de nous envoyer les données.
    -- Idéalement, le client aurait déjà une copie synchronisée ou le serveur enverrait juste un message formaté.
    TriggerServerEvent('sheriffs_vs_outlaws:requestPlayerInventory')
end, false)

-- Événement pour afficher l'inventaire (envoyé par le serveur)
RegisterNetEvent('sheriffs_vs_outlaws:displayPlayerInventory')
AddEventHandler('sheriffs_vs_outlaws:displayPlayerInventory', function(inventoryItems)
    if #inventoryItems > 0 then
        local message = "Votre inventaire d'objets volés :\n"
        for _, itemName in ipairs(inventoryItems) do
            message = message .. "- " .. itemName .. "\n"
        end
        ShowNotification(message) -- Afficher dans le chat ou via une UI dédiée plus tard
    else
        ShowNotification("Votre inventaire d'objets volés est vide.")
    end
end)

-- Commande pour voir le solde (argent propre et sale)
RegisterCommand('balance', function(source, args, rawCommand)
    TriggerServerEvent('sheriffs_vs_outlaws:requestPlayerBalance')
end, false)

-- Événement pour afficher le solde (envoyé par le serveur)
RegisterNetEvent('sheriffs_vs_outlaws:displayPlayerBalance')
AddEventHandler('sheriffs_vs_outlaws:displayPlayerBalance', function(money, dirtyMoney)
    ShowNotification(string.format("Votre solde : Argent propre: $%s, Argent sale: $%s", money, dirtyMoney))
end)

-- Événement pour les notifications générales du serveur (vol, vente, blanchiment)
RegisterNetEvent('sheriffs_vs_outlaws:showNotification')
AddEventHandler('sheriffs_vs_outlaws:showNotification', function(message, duration)
    ShowNotification(message)
    -- On pourrait utiliser la durée ici si la fonction ShowNotification la supportait
end)

-- Fonction pour afficher un hint (message d'aide contextuel)
-- function ShowHint(text)
--     AddTextEntry("SVS_HINT", text)
--     BeginTextCommandDisplayHelp("SVS_HINT")
--     EndTextCommandDisplayHelp(0, false, true, -1)
-- end

print('^2[SvsO - Theft] Le script cl_theft.lua est chargé et fonctionnel.^0')