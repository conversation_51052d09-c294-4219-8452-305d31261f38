--[[
********************************************************************************
* sv_manhunt.lua - Logique serveur pour l'événement de Chasse à l'Homme        *
********************************************************************************
]]

-- Variables d'état de la Chasse à l'Homme
local isManhuntActive = false
local manhuntTarget = nil
local manhuntRangers = {}
local manhuntTimer = 0
local lastManhuntEndTime = 0

-- Fonction pour notifier les joueurs (placeholder, à implémenter avec votre système de notification)
local function NotifyPlayer(playerId, message)
    -- Exemple : TriggerClientEvent('chat:addMessage', playerId, { args = { '^1[CHASSE A L\'HOMME]', message } })
    print(string.format("Notification pour %s: %s", playerId, message))
end

-- Fonction pour les annonces globales via le Journal (placeholder)
local function AnnounceToJournal(message)
    exports['sheriffs_vs_outlaws']:AddJournalEntry(message)
    print(string.format("Journal: %s", message))
end

-- Fonction pour vérifier si un joueur a le grade requis
local function HasRequiredRank(playerId)
    local playerData = PlayersData[playerId]
    if playerData and playerData.faction == "sheriffs" and playerData.rank == Config.ManhuntRequiredRank then
        return true
    end
    return false
end

-- Fonction pour obtenir les joueurs avec le grade requis
local function GetEligibleRangers()
    local rangers = {}
    -- S'assurer que PlayersData est une table avant d'itérer dessus
    PlayersData = PlayersData or {}
    for playerId, data in pairs(PlayersData) do
        if data.faction == "sheriffs" and data.rank == Config.ManhuntRequiredRank then
            -- Vérifier si le joueur est actuellement connecté
            if GetPlayerName(tonumber(playerId)) ~= nil then
                 table.insert(rangers, tonumber(playerId))
            end
        end
    end
    return rangers
end

-- Fonction pour trouver un hors-la-loi éligible
local function FindEligibleOutlaw()
    local outlaws = {}
    for playerId, data in pairs(PlayersData) do
        if data.faction == "outlaws" and data.bounty and data.bounty >= Config.ManhuntBountyThreshold then
            -- Vérifier si le joueur est actuellement connecté
            if GetPlayerName(tonumber(playerId)) ~= nil then
                table.insert(outlaws, tonumber(playerId))
            end
        end
    end
    if #outlaws > 0 then
        return outlaws[math.random(#outlaws)] -- Sélectionne un hors-la-loi au hasard
    end
    return nil
end

-- Fonction pour démarrer la Chasse à l'Homme
local function StartManhunt(targetId, rangers)
    isManhuntActive = true
    manhuntTarget = targetId
    manhuntRangers = rangers
    manhuntTimer = Config.ManhuntDuration
    lastManhuntEndTime = GetGameTimer() -- Utilisé pour le cooldown global

    local targetName = GetPlayerName(targetId)
    local targetCoords = GetEntityCoords(GetPlayerPed(targetId))

    AnnounceToJournal(string.format("[JOURNAL] Une chasse à l'homme est lancée ! Les redoutables Rangers traquent %s !", targetName))

    NotifyPlayer(targetId, string.format("Vous êtes la cible d'une chasse à l'homme menée par les Rangers ! Survivez pendant %d minutes.", Config.ManhuntDuration / 60))
    TriggerClientEvent('sheriffs_vs_outlaws:manhunt_notifyTarget', targetId, string.format("Vous êtes la cible d'une chasse à l'homme ! Survivez %d min.", Config.ManhuntDuration / 60))

    for _, rangerId in ipairs(rangers) do
        local message = string.format("Chasse à l'homme initiée ! Cible : %s. Dernière position connue : X:%.2f, Y:%.2f, Z:%.2f.", targetName, targetCoords.x, targetCoords.y, targetCoords.z)
        NotifyPlayer(rangerId, message)
        TriggerClientEvent('sheriffs_vs_outlaws:manhunt_notifyRanger', rangerId, message, targetId)
    end

    print(string.format("Chasse à l'homme démarrée. Cible: %s (%d), Rangers: %d", targetName, targetId, #rangers))
end

-- Fonction pour terminer la Chasse à l'Homme
function EndManhunt(reason, involvedPlayerId)
    if not isManhuntActive then return end

    local targetName = GetPlayerName(manhuntTarget) or "Inconnu"

    if reason == "survived" then
        AnnounceToJournal(string.format("[JOURNAL] %s a réussi à échapper aux Rangers lors d'une traque intense !", targetName))
        local survivalMessage = "Vous avez survécu à la chasse à l'homme et gagnez de l'XP et de l'argent !"
        NotifyPlayer(manhuntTarget, survivalMessage)
        TriggerClientEvent('sheriffs_vs_outlaws:manhunt_ended', manhuntTarget, survivalMessage)
        if exports.MainCore:GetPlayerFaction(manhuntTarget) then -- Vérifie si le joueur existe via MainCore
            exports.MainCore:AddPlayerXP(manhuntTarget, Config.ManhuntTargetSurvivedXP)
            exports.MainCore:UpdatePlayerMoney(manhuntTarget, Config.ManhuntTargetSurvivedMoney)
        end
        print(string.format("Chasse à l'homme terminée. %s a survécu.", targetName))

    elseif reason == "neutralized" then
        local rangerName = GetPlayerName(involvedPlayerId) or "Un Ranger"
        AnnounceToJournal(string.format("[JOURNAL] Justice ! %s a été neutralisé par %s après une poursuite acharnée !", targetName, rangerName))
        local targetNeutralizedMsg = string.format("Vous avez été neutralisé par %s.", rangerName)
        NotifyPlayer(manhuntTarget, targetNeutralizedMsg)
        TriggerClientEvent('sheriffs_vs_outlaws:manhunt_ended', manhuntTarget, targetNeutralizedMsg)

        local rangerSuccessMsg = string.format("Vous avez neutralisé %s et gagnez de l'XP et de l'argent !", targetName)
        NotifyPlayer(involvedPlayerId, rangerSuccessMsg)
        TriggerClientEvent('sheriffs_vs_outlaws:manhunt_ended', involvedPlayerId, rangerSuccessMsg)
        if exports.MainCore:GetPlayerFaction(involvedPlayerId) then -- Vérifie si le joueur existe via MainCore
            exports.MainCore:AddPlayerXP(involvedPlayerId, Config.ManhuntRangerRewardXP)
            exports.MainCore:UpdatePlayerMoney(involvedPlayerId, Config.ManhuntRangerRewardMoney)
        end
        print(string.format("Chasse à l'homme terminée. %s a été neutralisé par %s.", targetName, rangerName))

    elseif reason == "disconnected" then
        local disconnectMsg = string.format("[JOURNAL] %s s'est déconnecté pendant la chasse à l'homme. La traque est annulée.", targetName)
        AnnounceToJournal(disconnectMsg)
        -- Notifier les rangers restants
        for _, rangerId in ipairs(manhuntRangers) do
            if GetPlayerName(rangerId) then -- S'assurer que le ranger est toujours connecté
                NotifyPlayer(rangerId, string.format("La cible %s s'est déconnectée. La chasse est terminée.", targetName))
                TriggerClientEvent('sheriffs_vs_outlaws:manhunt_ended', rangerId, string.format("La cible %s s'est déconnectée.", targetName))
            end
        end
        print(string.format("Chasse à l'homme terminée. %s s'est déconnecté.", targetName))
    end

    -- Réinitialisation
    isManhuntActive = false
    manhuntTarget = nil
    manhuntRangers = {}
    manhuntTimer = 0
    lastManhuntEndTime = GetGameTimer() -- Réinitialise le cooldown à partir de maintenant

    -- Nettoyer les blips côté client pour les rangers
    for _, rangerId in ipairs(GetPlayers()) do -- Notifier tous les joueurs au cas où certains étaient rangers
         TriggerClientEvent('sheriffs_vs_outlaws:manhunt_removeTargetBlip', rangerId)
    end
end

-- Boucle principale pour vérifier et déclencher la Chasse à l'Homme
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000) -- Vérifie toutes les 5 secondes (configurable)

        if not isManhuntActive then
            local currentTime = GetGameTimer()
            if (currentTime - (lastManhuntEndTime or 0)) / 1000 < Config.ManhuntCooldown then
                -- print("Chasse à l'homme en cooldown.")
                goto continue_loop -- Saute le reste de la boucle si en cooldown
            end

            local eligibleRangers = GetEligibleRangers()
            if #eligibleRangers == 0 then
                -- print("Aucun Ranger éligible connecté pour la chasse à l'homme.")
                goto continue_loop
            end

            local targetOutlaw = FindEligibleOutlaw()
            if not targetOutlaw then
                -- print("Aucun hors-la-loi éligible pour la chasse à l'homme.")
                goto continue_loop
            end

            print(string.format("Conditions remplies pour la chasse à l'homme. Cible potentielle: %s, Rangers: %d", GetPlayerName(targetOutlaw), #eligibleRangers))
            StartManhunt(targetOutlaw, eligibleRangers)
        else
            manhuntTimer = manhuntTimer - (5000 / 1000) -- Décrémente le timer (5 secondes)
            if manhuntTimer <= 0 then
                EndManhunt("survived")
            else
                -- Mettre à jour périodiquement la position de la cible pour les Rangers
                if manhuntTarget and #manhuntRangers > 0 then
                    for _, rangerId in ipairs(manhuntRangers) do
                        if GetPlayerName(rangerId) then -- S'assurer que le ranger est toujours connecté
                            TriggerClientEvent('sheriffs_vs_outlaws:manhunt_updateTargetBlip', rangerId, manhuntTarget)
                        end
                    end
                end
            end
        end
        ::continue_loop::
    end
end)

-- Événements pour interagir avec d'autres systèmes

-- Écoute l'événement de MainCore lorsque qqn meurt
AddEventHandler('MainCore:playerDied', function(deadPlayerId, killerPlayerId, killerType, deathCoords)
    if isManhuntActive and deadPlayerId == manhuntTarget then
        local killerIsRanger = false
        if killerPlayerId then
            for _, rangerId in ipairs(manhuntRangers) do
                if rangerId == killerPlayerId then
                    killerIsRanger = true
                    break
                end
            end
        end

        if killerIsRanger then
            print(string.format("Manhunt: Cible %s (ID: %s) neutralisée par Ranger %s (ID: %s).", GetPlayerName(deadPlayerId), deadPlayerId, GetPlayerName(killerPlayerId), killerPlayerId))
            EndManhunt("neutralized", killerPlayerId)
        else
            print(string.format("Manhunt: Cible %s (ID: %s) neutralisée, mais pas par un Ranger participant (Tueur: %s, ID: %s).", GetPlayerName(deadPlayerId), deadPlayerId, killerPlayerId and GetPlayerName(killerPlayerId) or "N/A", killerPlayerId or "N/A"))
            EndManhunt("target_died_other_causes", killerPlayerId)
        end
    elseif isManhuntActive and IsPlayerManhuntRanger(deadPlayerId) then
        -- Un ranger participant est mort
        print(string.format("Manhunt: Un Ranger participant, %s (ID: %s), est mort.", GetPlayerName(deadPlayerId), deadPlayerId))
        -- La logique de playerDropped devrait déjà le retirer de la liste manhuntRangers s'il se déconnecte après la mort.
        -- Si la mort ne cause pas un playerDropped, il faut le retirer ici.
        for i, rangerId in ipairs(manhuntRangers) do
            if rangerId == deadPlayerId then
                table.remove(manhuntRangers, i)
                print(string.format("Manhunt: Ranger %s (ID: %s) retiré de la liste des participants actifs. Rangers restants: %d", GetPlayerName(deadPlayerId), deadPlayerId, #manhuntRangers))
                if #manhuntRangers == 0 then
                    print("Manhunt: Plus aucun Ranger participant actif après une mort. La chasse pourrait être annulée.")
                    -- EndManhunt("no_rangers_left") -- Décommenter si c'est le comportement souhaité.
                end
                break
            end
        end
    end
end)


-- Gérer la déconnexion des joueurs
local originalPlayerDroppedHandler = nil -- Pour stocker le handler original s'il y en a un

-- On cherche un handler existant pour 'playerDropped' pour ne pas l'écraser
-- Note: FiveM/RedM ne fournit pas de moyen direct de "chaîner" les handlers d'événements de la même ressource facilement.
-- Cette approche est une tentative, mais peut nécessiter une refonte si d'autres scripts utilisent AddEventHandler pour playerDropped.
-- Idéalement, playerDropped dans sv_main.lua devrait appeler une fonction de ce script.

AddEventHandler('playerDropped', function(reason)
    -- if originalPlayerDroppedHandler then
    --     originalPlayerDroppedHandler(reason) -- Appeler le handler original s'il existe
    -- end

    local playerId = source -- 'source' est l'ID du joueur qui s'est déconnecté dans ce contexte
    if isManhuntActive then
        if playerId == manhuntTarget then
            print(string.format("Manhunt: Cible %s (ID: %d) déconnectée.", GetPlayerName(playerId) or "Inconnu", playerId))
            EndManhunt("disconnected")
        else
            local wasRanger = false
            for i, rangerId in ipairs(manhuntRangers) do
                if rangerId == playerId then
                    table.remove(manhuntRangers, i)
                    wasRanger = true
                    print(string.format("Manhunt: Ranger %s (ID: %d) déconnecté. Rangers restants: %d", GetPlayerName(playerId) or "Inconnu", playerId, #manhuntRangers))
                    break
                end
            end

            if wasRanger and #manhuntRangers == 0 then
                -- S'il n'y a plus de rangers et que la chasse nécessite des rangers.
                -- On pourrait annuler la chasse.
                print("Manhunt: Plus aucun Ranger participant. La chasse pourrait être annulée.")
                -- EndManhunt("no_rangers_left") -- Décommenter si c'est le comportement souhaité.
            end
        end
    end
end)


-- Exportations pour que sv_main puisse notifier ce script
exports('IsManhuntActive', function()
    return isManhuntActive
end)

exports('GetManhuntTarget', function()
    return manhuntTarget
end)

exports('IsPlayerManhuntRanger', function(playerId)
    if not isManhuntActive then return false end
    for _, id in ipairs(manhuntRangers) do
        if id == playerId then return true end
    end
    return false
end)


-- Commande admin pour tester (optionnel)
RegisterCommand('testmanhunt', function(cmdSource, args, rawCommand)
    local playerId = tonumber(args[1])
    local eligibleRangers = GetEligibleRangers()
    if not playerId or not GetPlayerName(playerId) then
        print("Usage: /testmanhunt <targetPlayerId>")
        NotifyPlayer(cmdSource, "Usage: /testmanhunt <targetPlayerId>")
        return
    end
    if #eligibleRangers == 0 then
        print("Aucun ranger éligible pour démarrer la chasse test.")
        NotifyPlayer(cmdSource, "Aucun ranger éligible pour démarrer la chasse test.")
        return
    end
    if isManhuntActive then
        print("Une chasse est déjà active.")
        NotifyPlayer(cmdSource, "Une chasse est déjà active.")
        return
    end
    
    -- Forcer le joueur à être un hors-la-loi avec une prime suffisante pour le test
    -- Pour tester, nous allons directement utiliser les fonctions de MainCore pour affecter la faction et la prime
    -- Cela suppose que SetJob dans le shim de MainCore met à jour la faction dans PlayersData de MainCore
    local core = exports.MainCore:GetCoreObject()
    if core and core.Functions.GetPlayer then
        local TempPlayer = core.Functions.GetPlayer(playerId)
        if TempPlayer and TempPlayer.Functions.SetJob then
            TempPlayer.Functions.SetJob('outlaws', 1) -- grade 1 pour outlaws
            exports.MainCore:UpdatePlayerBounty(playerId, Config.ManhuntBountyThreshold + 500 - (exports.MainCore:GetPlayerBounty(playerId) or 0) ) -- Ajuste la prime
            print(string.format("Joueur %s (ID: %d) configuré comme cible de test via MainCore.", GetPlayerName(playerId), playerId))
            StartManhunt(playerId, eligibleRangers)
        else
            print("Impossible de configurer le joueur test via MainCore (SetJob non trouvé).")
            NotifyPlayer(cmdSource, "Impossible de configurer le joueur test via MainCore (SetJob non trouvé).")
        end
    else
        print("Impossible de récupérer MainCore pour configurer le joueur test.")
        NotifyPlayer(cmdSource, "Impossible de récupérer MainCore pour configurer le joueur test.")
        print(string.format("Joueur %s (ID: %d) configuré comme cible de test.", GetPlayerName(playerId), playerId))
        StartManhunt(playerId, eligibleRangers)
    end -- Ajouté pour fermer le if/else précédent
end, true) -- true pour admin seulement


print("sv_manhunt.lua chargé et mis à jour avec la logique de PlayersData.")