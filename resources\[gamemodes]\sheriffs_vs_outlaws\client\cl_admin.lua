-- Variables locales pour les commandes admin
local isAdmin = false -- Cette variable sera mise à jour par le serveur
local fullMapEnabled = false -- Variable statique pour garder l'état

-- Variables pour le noclip
local noclip = false
local noclipSpeed = 1.0
local noclipSpeedMultiplier = 1.0

-- Événement pour définir le statut d'administrateur
RegisterNetEvent('sheriffs_vs_outlaws:setAdminStatus')
AddEventHandler('sheriffs_vs_outlaws:setAdminStatus', function(status)
    isAdmin = status
    print('^3[SvsO Admin] Statut administrateur mis à jour: ' .. tostring(status) .. '^0')
    
    -- Afficher un message dans le chat pour confirmer
    if status then
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = false,
            args = {"[Admin]", "Vous avez maintenant le statut d'administrateur."}
        })
        
        -- Activer automatiquement le fullmap pour les admins
        fullMapEnabled = true
        Citizen.InvokeNative(0x4B8F743A4A6D2FF8, true) -- SET_MINIMAP_HIDE_FOW
        print("^3[SvsO Admin] Mode carte complète activé automatiquement^0")
    end
end)

-- Fonction alternative pour obtenir les coords du waypoint via blip
local function GetWaypointCoords2()
    print("^3[SvsO Admin] Tentative de récupération des coords du waypoint via blip...^0")

    -- 8 = BLIP_ID_WAYPOINT (route)
    local blip = GetFirstBlipInfoId(8)
    if blip == 0 then
        print("^3[SvsO Admin] Aucun waypoint (blip ID 8) trouvé.^0")
        TriggerEvent('chat:addMessage', {
            color = {255, 165, 0},
            multiline = false,
            args = {"[Admin]", "Aucun waypoint détecté. Placez un repère sur la carte !"}
        })
        return nil
    end

    local x, y, z = table.unpack(GetBlipInfoIdCoord(blip))
    print(string.format("^3[SvsO Admin] Waypoint trouvé via blip : X=%.2f, Y=%.2f, Z=%.2f^0", x, y, z))
    return vector3(x, y, z)
end


-- Fonction pour trouver l'altitude (axe Z) à une position XY donnée
local function GetGroundZFor(x, y)
    local z = 0.0
    local groundFound = false
    local attempts = 0
    
    -- Commencer à une hauteur élevée et descendre
    for height = 1000.0, -500.0, -25.0 do
        attempts = attempts + 1
        
        Citizen.InvokeNative(0x1DD55701034110E5, x, y, height, 0, 0, 0) -- SET_FOCUS_POS_AND_VEL (N_0x1dd55701034110e5)
        Citizen.Wait(5) -- Réduire un peu le délai d'attente
        
        -- boolean _GET_GROUND_Z_FOR_3D_COORD(float x, float y, float z, float* groundZ, BOOL p4)
        local success, groundZ = GetGroundZFor_3dCoord(x, y, height, true)
        
        if success then
            z = groundZ
            groundFound = true
            break
        end
        
        if attempts > 65 then
            break -- Éviter une boucle trop longue
        end
    end
    
    Citizen.InvokeNative(0x31B73D1EA9F01DA2) -- CLEAR_FOCUS (N_0x31b73d1ea9f01da2)
    
    return z, groundFound
end

-- Commande pour afficher les coordonnées actuelles (utile pour le débogage)
RegisterCommand('coords', function()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)
    
    local coordsString = string.format("Coords: X: %.2f, Y: %.2f, Z: %.2f, H: %.2f", 
                                       coords.x, coords.y, coords.z, heading)
    
    -- Afficher dans la console
    print("^3[SvsO Admin] " .. coordsString .. "^0")
    
    -- Afficher dans le chat
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = false,
        args = {"[Coords]", coordsString}
    })
    
    -- Copier dans le presse-papiers (non disponible dans RedM)
    -- Citizen.InvokeNative(0X...)
end, false)

-- Commande pour se téléporter vers un waypoint
RegisterCommand('tpwaypoint', function()
    -- Vérifier si le joueur est administrateur
    if not isAdmin then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Admin]", "Vous n'avez pas les permissions nécessaires pour utiliser cette commande."}
        })
        return
    end
    -- Afficher un message de chargement
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = false,
        args = {"[Admin]", "Recherche du waypoint et téléportation en cours..."}
    })
    -- Obtenir les coordonnées du waypoint
    local waypointCoords = GetWaypointCoords2()
    if not waypointCoords then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = false,
            args = {"[Admin]", "Aucun waypoint trouvé sur la carte. Placez un repère avant d'utiliser la commande !"}
        })
        return
    end
    
    local playerPed = PlayerPedId()
    local x, y = waypointCoords.x, waypointCoords.y
    
    -- Informer que nous avons trouvé des coordonnées
    print(string.format("^3[SvsO Admin] Coordonnées de téléportation trouvées: X=%.2f, Y=%.2f^0", x, y))
    
    -- Rechercher la hauteur du sol
    local z, groundFound = GetGroundZFor(x, y)
    
    -- Informer sur la hauteur trouvée
    print(string.format("^3[SvsO Admin] Hauteur du sol trouvée: Z=%.2f, Success=%s^0", z, tostring(groundFound)))
    
    if not groundFound then
        z = waypointCoords.z or 0.0 -- Utiliser la hauteur du waypoint ou 0 par défaut
        TriggerEvent('chat:addMessage', {
            color = {255, 165, 0},
            multiline = false,
            args = {"[Admin]", "Attention: Sol non trouvé à cette position, téléportation risquée."}
        })
    end
    
    -- Méthode de téléportation améliorée pour RedM
    print(string.format("^3[SvsO Admin] Tentative de téléportation aux coordonnées: X=%.2f, Y=%.2f, Z=%.2f^0", x, y, z))
    
    -- Commenté temporairement pour tester
    -- -- Désactiver les collisions pendant la téléportation
    -- SetEntityCollision(playerPed, false, false)
    
    -- Si le joueur est sur un cheval/véhicule
    local isOnMount = IsPedOnMount(playerPed)
    local vehicle = nil
    
    if isOnMount then
        vehicle = GetMount(playerPed)
        print("^3[SvsO Admin] Joueur sur une monture, ID: " .. tostring(vehicle) .. "^0")
    elseif IsPedInAnyVehicle(playerPed, false) then
        vehicle = GetVehiclePedIsIn(playerPed, false)
        print("^3[SvsO Admin] Joueur dans un véhicule, ID: " .. tostring(vehicle) .. "^0")
    end
    
    -- Arrêter toutes les animations
    ClearPedTasksImmediately(playerPed)
    
    -- Téléportation avec thread pour s'assurer que ça fonctionne
    Citizen.CreateThread(function()
        local attempts = 0
        local teleported = false
        
        while not teleported and attempts < 5 do
            attempts = attempts + 1
            
            if vehicle then
                -- Téléporter le véhicule/monture et le joueur
                SetEntityCoords(vehicle, x, y, z, false, false, false, false)
                print(string.format("^3[SvsO Admin] Tentative %d de téléportation du véhicule à: X=%.2f, Y=%.2f, Z=%.2f^0", 
                    attempts, x, y, z))
            else
                -- Téléporter juste le joueur
                SetEntityCoords(playerPed, x, y, z, false, false, false, false)
                print(string.format("^3[SvsO Admin] Tentative %d de téléportation du joueur à: X=%.2f, Y=%.2f, Z=%.2f^0", 
                    attempts, x, y, z))
            end
            
            -- Vérifier si la téléportation a réussi
            Citizen.Wait(250)
            local currentCoords = GetEntityCoords(playerPed)
            local distance = #(vector3(x, y, z) - currentCoords)
            
            if distance < 50.0 then
                teleported = true
                print(string.format("^3[SvsO Admin] Téléportation réussie! Distance: %.2f^0", distance))
            else
                print(string.format("^3[SvsO Admin] Échec de téléportation! Distance: %.2f^0", distance))
                Citizen.Wait(250)
            end
        end
        
        -- Restaurer les collisions
        SetEntityCollision(playerPed, true, true)
        
        -- Notification finale
        if teleported then
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                multiline = false,
                args = {"[Admin]", "Téléportation réussie vers les coordonnées: " .. math.floor(x) .. ", " .. math.floor(y) .. ", " .. math.floor(z)}
            })
        else
            TriggerEvent('chat:addMessage', {
                color = {255, 0, 0},
                multiline = false,
                args = {"[Admin]", "Échec de la téléportation après plusieurs tentatives."}
            })
        end
    end)
end, false)

-- Enregistrer l'alias 'tpwp' pour la commande 'tpwaypoint'
RegisterCommand('tpwp', function()
    ExecuteCommand('tpwaypoint')
end, false)

-- Demander au serveur notre statut d'administrateur après le chargement de la ressource
AddEventHandler('onClientResourceStart', function(resourceName) 
    if(GetCurrentResourceName() ~= resourceName) then
        return
    end
    print("^3[SvsO Admin] Demande du statut admin au serveur...^0")
    Citizen.Wait(2000) -- Attendre un peu pour s'assurer que le serveur est prêt
    TriggerServerEvent('sheriffs_vs_outlaws:checkAdminStatus')
    
    -- Activer le fullmap avec un délai pour être sûr que le statut admin est vérifié
    Citizen.CreateThread(function()
        Citizen.Wait(5000)
        if isAdmin then
            fullMapEnabled = true
            Citizen.InvokeNative(0x4B8F743A4A6D2FF8, true) -- SET_MINIMAP_HIDE_FOW
            print("^3[SvsO Admin] Mode carte complète activé automatiquement^0")
        end
    end)
end)

-- Autre commande pour demander explicitement le statut admin (utile si la vérification automatique a échoué)
RegisterCommand('checkadmin', function()
    TriggerServerEvent('sheriffs_vs_outlaws:checkAdminStatus')
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = false,
        args = {"[Admin]", "Vérification du statut administrateur en cours..."}
    })
end, false)

-- Commande pour basculer la visibilité complète de la carte
RegisterCommand('fullmap', function()
    -- Vérifier si le joueur est administrateur
    if not isAdmin then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Admin]", "Vous n'avez pas les permissions nécessaires pour utiliser cette commande."}
        })
        return
    end

    -- Variable statique pour garder l'état
    fullMapEnabled = not (fullMapEnabled or false)
    
    -- Utiliser la bonne native pour RedM - SetMinimapHideFow
    Citizen.InvokeNative(0x4B8F743A4A6D2FF8, fullMapEnabled) -- SET_MINIMAP_HIDE_FOW
    
    -- Notification
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = false,
        args = {"[Admin]", "Carte " .. (fullMapEnabled and "complètement visible" or "avec brouillard")}
    })
    
    -- Logging pour debug
    print("^3[SvsO Admin] État de la carte complète: " .. tostring(fullMapEnabled) .. "^0")
end, false)

-- Enregistrer l'alias 'map' pour la commande 'fullmap'
RegisterCommand('map', function()
    ExecuteCommand('fullmap')
end, false)

-- Commande pour se téléporter vers un blip avec un sprite ID spécifique
RegisterCommand('tpsprite', function(source, args)
    if not isAdmin then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Admin]", "Vous n'avez pas les permissions nécessaires pour utiliser cette commande."}
        })
        return
    end
    
    if not args[1] or not tonumber(args[1]) then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = false,
            args = {"[Admin]", "Usage: /tpsprite [ID] - Exemple: /tpsprite 8"}
        })
        return
    end
    
    local targetSpriteId = tonumber(args[1])
    local found = false
    local coords = nil
    
    print("^3[SvsO Admin] Recherche d'un blip avec sprite ID: " .. targetSpriteId .. "^0")
    
    for i = 0, 1024 do
        if DoesBlipExist(i) then
            local currentBlipSprite = Citizen.InvokeNative(0xBE72CF5A5227153F, i, Citizen.ResultAsInteger())
            
            if currentBlipSprite == targetSpriteId then
                coords = GetBlipCoords(i)
                if coords and (coords.x ~= 0.0 or coords.y ~= 0.0 or coords.z ~= 0.0) then
                    found = true
                    print(string.format("^3[SvsO Admin] Blip trouvé! ID: %d, Sprite: %d, Coords: X=%.2f, Y=%.2f, Z=%.2f^0",
                        i, currentBlipSprite, coords.x, coords.y, coords.z))
                    break
                end
            end
        end
    end
    
    if not found then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = false,
            args = {"[Admin]", "Aucun blip avec sprite ID " .. targetSpriteId .. " trouvé."}
        })
        return
    end
    
    -- Téléporter vers les coordonnées (réutiliser le code de téléportation)
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = false,
        args = {"[Admin]", "Téléportation vers blip avec sprite ID " .. targetSpriteId .. "..."}
    })
    
    local playerPed = PlayerPedId()
    local x, y = coords.x, coords.y
    
    -- Rechercher la hauteur du sol
    local z, groundFound = GetGroundZFor(x, y)
    
    if not groundFound then
        z = coords.z or 0.0
        TriggerEvent('chat:addMessage', {
            color = {255, 165, 0},
            multiline = false,
            args = {"[Admin]", "Attention: Sol non trouvé à cette position, téléportation risquée."}
        })
    end
    
    -- Réutiliser la méthode de téléportation existante
    print(string.format("^3[SvsO Admin] Tentative de téléportation aux coordonnées: X=%.2f, Y=%.2f, Z=%.2f^0", x, y, z))
    
    -- Commenté temporairement pour tester
    -- -- Désactiver les collisions pendant la téléportation
    -- SetEntityCollision(playerPed, false, false)
    
    -- Si le joueur est sur un cheval/véhicule
    local isOnMount = IsPedOnMount(playerPed)
    local vehicle = nil
    
    if isOnMount then
        vehicle = GetMount(playerPed)
    elseif IsPedInAnyVehicle(playerPed, false) then
        vehicle = GetVehiclePedIsIn(playerPed, false)
    end
    
    -- Arrêter toutes les animations
    ClearPedTasksImmediately(playerPed)
    
    -- Téléportation avec thread pour s'assurer que ça fonctionne
    Citizen.CreateThread(function()
        local attempts = 0
        local teleported = false
        
        while not teleported and attempts < 5 do
            attempts = attempts + 1
            
            if vehicle then
                SetEntityCoords(vehicle, x, y, z, false, false, false, false)
            else
                SetEntityCoords(playerPed, x, y, z, false, false, false, false)
            end
            
            Citizen.Wait(250)
            local currentCoords = GetEntityCoords(playerPed)
            local distance = #(vector3(x, y, z) - currentCoords)
            
            if distance < 50.0 then
                teleported = true
            else
                Citizen.Wait(250)
            end
        end
        
        -- Restaurer les collisions
        SetEntityCollision(playerPed, true, true)
        
        -- Notification finale
        if teleported then
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                multiline = false,
                args = {"[Admin]", "Téléportation réussie vers les coordonnées: " .. math.floor(x) .. ", " .. math.floor(y) .. ", " .. math.floor(z)}
            })
        else
            TriggerEvent('chat:addMessage', {
                color = {255, 0, 0},
                multiline = false,
                args = {"[Admin]", "Échec de la téléportation après plusieurs tentatives."}
            })
        end
    end)
end, false)

-- Ajouter une commande pour la téléportation par coordonnées manuelles
RegisterCommand('tpcoords', function(source, args)
    -- Vérifier si le joueur est administrateur
    if not isAdmin then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Admin]", "Vous n'avez pas les permissions nécessaires pour utiliser cette commande."}
        })
        return
    end
    
    -- Vérifier si les coordonnées sont fournies
    if not args[1] or not args[2] or not tonumber(args[1]) or not tonumber(args[2]) then
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = false,
            args = {"[Admin]", "Usage: /tpcoords X Y [Z] - Exemple: /tpcoords 2508.15 -1450.25 44.61"}
        })
        return
    end
    
    -- Récupérer les coordonnées fournies
    local x = tonumber(args[1])
    local y = tonumber(args[2])
    local z = tonumber(args[3]) or 0.0 -- Z est optionnel
    
    -- Si Z n'est pas fourni, chercher la hauteur du sol
    if args[3] == nil then
        local groundZ, found = GetGroundZFor(x, y)
        if found then
            z = groundZ
        end
    end
    
    -- Utiliser la fonction existante de téléportation
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = false,
        args = {"[Admin]", "Téléportation aux coordonnées: " .. x .. ", " .. y .. ", " .. z}
    })
    
    local playerPed = PlayerPedId()
    
    -- Commenté temporairement pour tester
    -- -- Désactiver les collisions pendant la téléportation
    -- SetEntityCollision(playerPed, false, false)
    
    -- Si le joueur est sur un cheval/véhicule
    local isOnMount = IsPedOnMount(playerPed)
    local vehicle = nil
    
    if isOnMount then
        vehicle = GetMount(playerPed)
    elseif IsPedInAnyVehicle(playerPed, false) then
        vehicle = GetVehiclePedIsIn(playerPed, false)
    end
    
    -- Arrêter toutes les animations
    ClearPedTasksImmediately(playerPed)
    
    -- Téléportation
    if vehicle then
        SetEntityCoords(vehicle, x, y, z, false, false, false, false)
    else
        SetEntityCoords(playerPed, x, y, z, false, false, false, false)
    end
    
    -- Restaurer les collisions
    Citizen.Wait(500)
    SetEntityCollision(playerPed, true, true)
end, false)

-- Fonction pour activer/désactiver le noclip
local function ToggleNoClip()
    if not isAdmin then return end
    
    noclip = not noclip
    local playerPed = PlayerPedId()
    
    if noclip then
        -- Activer le noclip
        SetEntityInvincible(playerPed, true)
        SetEntityVisible(playerPed, false, false)
        FreezeEntityPosition(playerPed, true)
        SetEntityCollision(playerPed, false, false)
        
        -- Notification
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = false,
            args = {"[Admin]", "Noclip activé"}
        })
    else
        -- Désactiver le noclip
        SetEntityInvincible(playerPed, false)
        SetEntityVisible(playerPed, true, false)
        FreezeEntityPosition(playerPed, false)
        SetEntityCollision(playerPed, true, true)
        
        -- Notification
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = false,
            args = {"[Admin]", "Noclip désactivé"}
        })
    end
end

-- Thread pour gérer le noclip
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if noclip then
            local playerPed = PlayerPedId()
            local x, y, z = table.unpack(GetEntityCoords(playerPed))
            
            -- Obtenir la rotation actuelle de la caméra
            local heading = GetGameplayCamRot(2).z
            SetEntityHeading(playerPed, heading)
            
            -- Contrôles du noclip
            if IsControlPressed(0, 0x8FD015D8) then -- Z
                local heading = math.rad(heading)
                x = x + math.sin(-heading) * noclipSpeed * noclipSpeedMultiplier
                y = y + math.cos(-heading) * noclipSpeed * noclipSpeedMultiplier
            end
            if IsControlPressed(0, 0xD27782E3) then -- S
                local heading = math.rad(heading)
                x = x - math.sin(-heading) * noclipSpeed * noclipSpeedMultiplier
                y = y - math.cos(-heading) * noclipSpeed * noclipSpeedMultiplier
            end
            if IsControlPressed(0, 0xB4E465B4) then -- Q
                local heading = math.rad(heading - 90.0)
                x = x + math.sin(-heading) * noclipSpeed * noclipSpeedMultiplier
                y = y + math.cos(-heading) * noclipSpeed * noclipSpeedMultiplier
            end
            if IsControlPressed(0, 0x7065027D) then -- D
                local heading = math.rad(heading + 90.0)
                x = x + math.sin(-heading) * noclipSpeed * noclipSpeedMultiplier
                y = y + math.cos(-heading) * noclipSpeed * noclipSpeedMultiplier
            end
            if IsControlPressed(0, 0xD9D0E1C0) then -- SPACE
                z = z + noclipSpeed * noclipSpeedMultiplier
            end
            if IsControlPressed(0, 0x4D8FB4C1) then -- LEFT CTRL
                noclipSpeedMultiplier = 2.0
            else
                noclipSpeedMultiplier = 1.0
            end
            
            -- Appliquer les nouvelles coordonnées
            SetEntityCoordsNoOffset(playerPed, x, y, z, true, true, true)
        end
    end
end)

-- Enregistrer la touche F6 pour le noclip
RegisterCommand('noclip', function()
    if isAdmin then
        ToggleNoClip()
    end
end, false)

-- Enregistrer la touche F6
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if IsControlJustPressed(0, 0x3C0A40F2) then -- F6
            ExecuteCommand('noclip')
        end
    end
end)

print('^3[SheriffsVsOutlaws] Commandes administratives chargées.^0') 