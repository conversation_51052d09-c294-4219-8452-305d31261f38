-- cl_sprint.lua
-- Gestion du sprint illimité pour RedM

local sprintEnabled = true

-- Thread pour gérer l'endurance illimitée
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if sprintEnabled then
            -- Restaurer l'endurance du joueur
            local playerId = PlayerId()
            RestorePlayerStamina(playerId, 1.0)
            
            -- Alternative pour RedM si la native ci-dessus ne fonctionne pas
            -- Citizen.InvokeNative(0xA352C1678B5F4389, playerId, 1.0)
        else
            Citizen.Wait(1000) -- Attendre plus longtemps si désactivé
        end
    end
end)

-- Commande pour activer/désactiver le sprint illimité
RegisterCommand('togglesprint', function(source, args, rawCommand)
    sprintEnabled = not sprintEnabled
    local status = sprintEnabled and "activé" or "désactivé"
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = true,
        args = {"Sprint", "Sprint illimité " .. status}
    })
end, false)

print('^2[SvsO] cl_sprint.lua chargé - Sprint illimité disponible^0')
