local money = 0

-- Commande pour obtenir les coordonnées
RegisterCommand('getcoords', function()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = true,
        args = {"Système", string.format("Coordonnées: X=%.2f, Y=%.2f, Z=%.2f, Heading=%.2f", coords.x, coords.y, coords.z, heading)}
    })
end)

-- Mise à jour de l'argent volé
RegisterNetEvent('updateStolenMoney')
AddEventHandler('updateStolenMoney', function(newAmount)
    money = newAmount
    SendNUIMessage({
        type = "updateMoney",
        amount = money
    })
end)

-- Affichage de l'interface
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if money > 0 then
            SendNUIMessage({
                type = "showUI",
                amount = money
            })
        else
            SendNUIMessage({
                type = "hideUI"
            })
        end
    end
end) 