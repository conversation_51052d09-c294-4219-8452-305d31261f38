document.addEventListener('DOMContentLoaded', () => {
    const factionMenu = document.getElementById('factionMenu');
    const joinSheriffsButton = document.getElementById('joinSheriffs');
    const joinOutlawsButton = document.getElementById('joinOutlaws');

    // Fonction pour afficher le menu
    function showMenu() {
        if (factionMenu) {
            document.body.classList.remove('hidden');
            factionMenu.style.display = 'flex'; // Ou 'block', selon le style CSS
        }
    }

    // Fonction pour masquer le menu
    function hideMenu() {
        if (factionMenu) {
            document.body.classList.add('hidden');
            factionMenu.style.display = 'none';
            // Envoyer un message à Lua pour masquer la prévisualisation
            fetch(`https://MainCoreUI_FactionSelect/hideFactionMenu`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({}), // Pas besoin de données spécifiques pour juste masquer
            }).then(resp => resp.json()).then(data => {
                console.log('[MainCoreUI_FactionSelect] hideFactionMenu callback response:', data);
            }).catch(error => {
                console.error('[MainCoreUI_FactionSelect] Error sending hideFactionMenu callback:', error);
            });
        }
    }

    // Gestionnaire de clic pour le bouton Sheriffs
    if (joinSheriffsButton) {
        joinSheriffsButton.addEventListener('click', () => {
            selectFaction('sheriffs');
        });
    }

    // Gestionnaire de clic pour le bouton Outlaws
    if (joinOutlawsButton) {
        joinOutlawsButton.addEventListener('click', () => {
            selectFaction('outlaws');
        });
    }

    // Fonction pour envoyer la faction choisie à Lua
    async function selectFaction(factionName) {
        hideMenu();
        try {
            const resp = await fetch(`https://MainCoreUI_FactionSelect/selectFaction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8',
                },
                body: JSON.stringify({ faction: factionName }),
            });
            const data = await resp.json();
            console.log('Faction selected:', factionName, 'Response:', data);
        } catch (error) {
            console.error('Error selecting faction:', error);
        }
    }

    // Fonction pour afficher la prévisualisation du personnage (conceptuel, l'implémentation réelle dépendra de l'API NUI de RedM)
    function showCharacterPreview(modelName) {
        const previewArea = document.getElementById('characterPreview');
        if (previewArea) {
            // Ici, vous intégreriez la logique pour charger et afficher le modèle 3D
            // Cela pourrait impliquer une bibliothèque JS comme Three.js ou une API NUI spécifique
            // Pour l'instant, nous allons juste afficher le nom du modèle comme placeholder
            previewArea.innerHTML = `Prévisualisation de : ${modelName}`;
            console.log('[MainCoreUI_FactionSelect] Affichage de la prévisualisation pour le modèle:', modelName);
        }
    }

    // Fonction pour masquer la prévisualisation du personnage
    function hideCharacterPreview() {
        const previewArea = document.getElementById('characterPreview');
        if (previewArea) {
            previewArea.innerHTML = ''; // Vider la zone de prévisualisation
            console.log('[MainCoreUI_FactionSelect] Prévisualisation masquée.');
        }
    }

    // Écouteur d'événements pour les messages de Lua
    window.addEventListener('message', function(event) {
        console.log('[MainCoreUI_FactionSelect] Message received:', JSON.stringify(event.data));
        if (event.data.action === 'showFactionMenu') {
            console.log('[MainCoreUI_FactionSelect] Action "showFactionMenu" received. Calling showMenu().');
            showMenu();
        } else if (event.data.action === 'showCharacterPreview' && event.data.modelName) {
            console.log('[MainCoreUI_FactionSelect] Action "showCharacterPreview" received. Calling showCharacterPreview().');
            showCharacterPreview(event.data.modelName);
        } else if (event.data.action === 'hideCharacterPreview') {
            console.log('[MainCoreUI_FactionSelect] Action "hideCharacterPreview" received. Calling hideCharacterPreview().');
            hideCharacterPreview();
        }
    });

    // Pour tester l'affichage directement dans le navigateur (à commenter/supprimer pour le jeu)
    // showMenu();
});