print("[CL_FACTION_SELECT] Script Loaded")

Citizen.CreateThread(function()
    print("[FactionSelect] En attente de l'export de spawnmanager...")
    while not exports.spawnmanager do
        Citizen.Wait(100)
    end
    print("[FactionSelect] Export de spawnmanager trouvé.")
end)

local MainCore = exports.MainCore:GetCoreObject()
local hasFaction = false
local PlayerData = {}

local previewCamera = nil
local previewPed = nil

-- Coordonnées pour la scène de prévisualisation (à ajuster selon votre carte)
-- Assurez-vous que cet endroit est hors de vue des joueurs normaux
local previewCoords = vector3(-1000.0, -1000.0, 50.0) -- Exemple de coordonnées
local cameraCoords = vector3(-998.0, -1000.0, 50.5) -- Exemple de coordonnées pour la caméra
local cameraLookAt = vector3(-1000.0, -1000.0, 50.0) -- La caméra regarde le personnage

-- Helper pour afficher les tables dans les prints
local function tableToString(tbl, indent)
    indent = indent or 0
    local str_parts = {"{\n"}
    for k, v in pairs(tbl) do
        table.insert(str_parts, string.rep(" ", indent + 2) .. "[" .. tostring(k) .. "] = ")
        if type(v) == "table" then
            table.insert(str_parts, tableToString(v, indent + 2))
        else
            table.insert(str_parts, tostring(v))
        end
        table.insert(str_parts, ",\n")
    end
    table.insert(str_parts, string.rep(" ", indent) .. "}")
    return table.concat(str_parts)
end

-- Remplacer la fonction json.encode simple par une qui gère mieux les tables pour le print
local function serializeTableForPrint(data)
    if type(data) == "table" then
        return tableToString(data)
    else
        return tostring(data)
    end
end

-- Fonction pour vérifier si le joueur a une faction (côté client, après récupération du serveur)
function HasAlreadyChosenFaction()
    print("[FactionSelect] HasAlreadyChosenFaction() called, current hasFaction: " .. tostring(hasFaction))
    return hasFaction
end

-- Fonction pour créer la scène de prévisualisation (caméra et personnage)
local function createPreviewScene(modelName)
    print("[FactionSelect] Creating preview scene for model:", modelName)
    -- Nettoyer l'ancienne scène si elle existe
    destroyPreviewScene()

    -- Commenté temporairement pour tester
    -- -- Charger le modèle du personnage
    -- local modelHash = GetHashKey(modelName)
    -- RequestModel(modelHash)
    -- while not HasModelLoaded(modelHash) do
    --     Citizen.Wait(0)
    -- end

    -- -- Créer le personnage (ped)
    -- previewPed = CreatePed(modelHash, previewCoords.x, previewCoords.y, previewCoords.z, 0.0, false, false)
    -- SetEntityHeading(previewPed, 0.0) -- Orientation par défaut
    -- FreezeEntityPosition(previewPed, true) -- Empêcher le personnage de bouger
    -- SetEntityInvincible(previewPed, true) -- Rendre le personnage invincible
    -- SetBlockingOfNonTemporaryEvents(previewPed, true) -- Empêcher les événements temporaires

    -- -- Créer la caméra
    -- previewCamera = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    -- SetCamCoord(previewCamera, cameraCoords.x, cameraCoords.y, cameraCoords.z)
    -- PointCamAtCoord(previewCamera, cameraLookAt.x, cameraLookAt.y, cameraLookAt.z)
    -- SetCamActive(previewCamera, true)
    -- RenderScriptCams(true, false, 0, true, false)

    print("[FactionSelect] Preview scene created.")
end

-- Fonction pour détruire la scène de prévisualisation
function destroyPreviewScene()
    print("[FactionSelect] Destroying preview scene.")
    if DoesCamExist(previewCamera) then
        SetCamActive(previewCamera, false)
        DestroyCam(previewCamera, true)
        RenderScriptCams(false, false, 0, true, false)
        previewCamera = nil
        print("[FactionSelect] Preview camera destroyed.")
    end
    if DoesEntityExist(previewPed) then
        DeletePed(previewPed)
        previewPed = nil
        print("[FactionSelect] Preview ped deleted.")
    end
end

-- Événement déclenché lorsque le joueur spawn
AddEventHandler('MainCore:Client:OnPlayerLoaded', function()
    print("[FactionSelect] Event: MainCore:Client:OnPlayerLoaded triggered")
    print("[FactionSelect] Attempting to fetch PlayerData...")
    PlayerData = MainCore.Functions.GetPlayerData()
    print("[FactionSelect] PlayerData fetched: " .. serializeTableForPrint(PlayerData))
    if PlayerData.job and PlayerData.job.name ~= 'unemployed' then
        print("[FactionSelect] PlayerData.job.name is '" .. PlayerData.job.name .. "'. Setting hasFaction to true.")
        hasFaction = true
    else
        if PlayerData.job then
            print("[FactionSelect] PlayerData.job.name is '" .. PlayerData.job.name .. "'. Setting hasFaction to false.")
        else
            print("[FactionSelect] PlayerData.job is nil. Setting hasFaction to false.")
        end
        hasFaction = false
    end
    print("[FactionSelect] hasFaction is now: " .. tostring(hasFaction))

    if not HasAlreadyChosenFaction() then
        print("[FactionSelect] Condition 'not HasAlreadyChosenFaction()' is true. Triggering 'MainCore:server:checkFactionStatus'")
        TriggerServerEvent('MainCore:server:checkFactionStatus')
    else
        print("[FactionSelect] Condition 'not HasAlreadyChosenFaction()' is false. NOT triggering 'MainCore:server:checkFactionStatus'")
    end
end)

-- Événement pour afficher le menu de sélection de faction depuis le serveur MainCore
RegisterNetEvent('MainCore:client:showFactionMenu', function()
    print("[FactionSelect] Event: 'MainCore:client:showFactionMenu' received.")

    if _G.isSuicideCommand then
        print("[FactionSelect] Suicide command detected. Not showing faction menu.")
        _G.isSuicideCommand = false -- Réinitialiser le flag de suicide
        _G.allowTeamChangeOnNextDeath = false -- Réinitialiser aussi ce flag par sécurité
        return -- Ne pas continuer pour afficher le menu
    end

    local normalConditionToShow = not HasAlreadyChosenFaction()
    local teamChangeRequested = _G.allowTeamChangeOnNextDeath == true

    print("[FactionSelect] normalConditionToShow: " .. tostring(normalConditionToShow))
    print("[FactionSelect] teamChangeRequested (_G.allowTeamChangeOnNextDeath): " .. tostring(teamChangeRequested))

    local shouldShow = normalConditionToShow or teamChangeRequested
    
    print("[FactionSelect] Combined shouldShow: " .. tostring(shouldShow))

    if shouldShow then
        if teamChangeRequested then
            print("[FactionSelect] Faction menu shown due to F4 request. Resetting _G.allowTeamChangeOnNextDeath.")
            _G.allowTeamChangeOnNextDeath = false -- Réinitialiser le flag car il a été utilisé
        end
        print("[FactionSelect] 'shouldShow' is true. Logic to show menu is now handled by MainCore.")
        
        -- Créer la scène de prévisualisation avec un modèle par défaut
        -- REMPLACER 'mp_male' par le nom du modèle de personnage par défaut souhaité
        createPreviewScene('mp_male') 
        print("[FactionSelect] Preview scene created.")

        -- Envoyer un message NUI pour afficher le menu (géré par cl_ui_handler.lua)
        SendNUIMessage({
            action = 'showFactionMenu'
        })
        print("[FactionSelect] NUI message 'showFactionMenu' sent.")

        -- SendNUIMessage et SetNuiFocus sont maintenant gérés par MainCore/client/cl_maincore.lua
        -- print("[FactionSelect] DEBUG: Current resource name: " .. GetCurrentResourceName())
        -- SendNUIMessage({
        --     action = 'showFactionMenu'
        -- })
        -- print("[FactionSelect] DEBUG: SendNUIMessage would have been called here.")
        -- SetNuiFocus(true, true)
        -- print("[FactionSelect] NUI focus would have been set here.")
        print("[FactionSelect] Intention to show menu noted. MainCore should handle NUI display.")
    else
        print("[FactionSelect] 'shouldShow' is false. NUI message 'showFactionMenu' NOT sent.")
        -- S'assurer que la prévisualisation est masquée si le menu n'est pas affiché
        destroyPreviewScene()
        print("[FactionSelect] Preview scene destroyed.")
    end
end)

-- Callback NUI pour masquer le menu (appelé depuis le JS)
RegisterNUICallback('hideFactionMenu', function(data, cb)
    print("[FactionSelect] NUI Callback: 'hideFactionMenu' received.")
    destroyPreviewScene()
    print("[FactionSelect] Preview scene destroyed via NUI callback.")
    SetNuiFocus(false, false)
    print("[FactionSelect] NUI focus set to false.")
    cb({ status = 'ok' })
end)


--[[
-- Le Callback NUI pour la sélection de faction est maintenant géré par MainCoreUI_FactionSelect/client/cl_ui_handler.lua
-- RegisterNUICallback('MainCoreUI_FactionSelect', 'selectFaction', function(data, cb)
--     print("[FactionSelect] NUI Callback: 'selectFaction' received. Data: " .. serializeTableForPrint(data))
--     local factionChosen = data.faction
--     if factionChosen == 'sheriffs' or factionChosen == 'outlaws' then
--         print("[FactionSelect] Faction chosen: '" .. factionChosen .. "'. Triggering 'MainCore:server:joinFaction'.")
--         TriggerServerEvent('MainCore:server:joinFaction', factionChosen)
--         hasFaction = true -- Marquer comme ayant choisi une faction côté client pour éviter réaffichage immédiat
--         print("[FactionSelect] 'hasFaction' set to true after selection.")
--     else
--         print('[FactionSelect] Invalid faction received from NUI: ' .. tostring(factionChosen))
--     end
--
--     print("[FactionSelect] Setting NUI focus to false.")
--     SetNuiFocus(false, false)
--     cb({ status = 'ok' })
-- end)

--[[
-- Assurez-vous que le joueur a des données avant de vérifier la faction
-- Cet événement est souvent utilisé pour s'assurer que le joueur est complètement chargé.
AddEventHandler('MainCore:Client:OnPlayerLoaded', function()
    PlayerData = MainCore.Functions.GetPlayerData()
    if PlayerData.job and PlayerData.job.name ~= 'unemployed' then
        hasFaction = true
    else
        hasFaction = false
        -- Si toujours pas de faction après le chargement complet, on revérifie.
        -- Cela peut être redondant si le premier check fonctionne bien.
        TriggerServerEvent('sheriffs_vs_outlaws:server:checkFactionStatus')
    end
end)
--]]

-- Commande de test pour afficher le menu (à supprimer en production)
RegisterCommand('testfactionmenu', function()
    print("[FactionSelect] Command 'testfactionmenu' executed.")
    local shouldShow = not HasAlreadyChosenFaction()
    print("[FactionSelect] Inside testfactionmenu, HasAlreadyChosenFaction() returned: " .. tostring(not shouldShow) .. ". So, shouldShow is: " .. tostring(shouldShow))
    if shouldShow then
        print("[FactionSelect] 'shouldShow' is true in testfactionmenu. Sending NUI message 'showFactionMenu'.")
        
        -- Créer la scène de prévisualisation avec un modèle par défaut pour le test
        -- REMPLACER 'mp_male' par le nom du modèle de personnage par défaut souhaité
        createPreviewScene('mp_male')
        print("[FactionSelect] Preview scene created by testfactionmenu.")

        SendNUIMessage({
            action = 'showFactionMenu'
        })
        SetNuiFocus(true, true)
        print("[FactionSelect] NUI focus set to true by testfactionmenu.")
    else
        print("[FactionSelect] 'shouldShow' is false in testfactionmenu. Player already has a faction.")
        -- S'assurer que la prévisualisation est masquée si le menu n'est pas affiché
        destroyPreviewScene()
        print("[FactionSelect] Preview scene destroyed by testfactionmenu.")
        MainCore.Functions.Notify("Vous avez déjà choisi une faction.", "error")
    end
end, false)
-- Événement déclenché par le serveur APRES la mise à jour de la faction pour téléporter le joueur
RegisterNetEvent('sheriffs_vs_outlaws:client:spawnAtFactionLocation', function(faction)
    print("[FactionSelect] Event: 'sheriffs_vs_outlaws:client:spawnAtFactionLocation' received for faction: " .. faction)
    
    -- S'assurer que la scène de prévisualisation est détruite
    destroyPreviewScene()
    
    local spawnLocationData = Config.FactionSpawns[faction]

    if spawnLocationData then
        print("[FactionSelect] Found spawn location for faction '" .. faction .. "': " .. serializeTableForPrint(spawnLocationData))
        
        local spawnCoords = spawnLocationData.coords
        local playerPed = PlayerPedId()
        
        -- Désactiver le contrôle du joueur
        SetPlayerControl(PlayerId(), false, 0)
        
        -- Faire un fade out
        DoScreenFadeOut(0)
        Citizen.Wait(1000)
        
        -- Trouver une hauteur valide
        local x, y = spawnCoords.x, spawnCoords.y
        local z = spawnCoords.z
        local groundZ, found = GetGroundZFor_3dCoord(x, y, z + 100.0, 0)
        
        if found and groundZ then
            z = groundZ + 1.0 -- Ajouter 1.0 pour être sûr d'être au-dessus du sol
            print("[FactionSelect] Hauteur du sol trouvée: " .. z)
        else
            print("[FactionSelect] Impossible de trouver le sol, utilisation de la hauteur par défaut: " .. z)
        end
        
        -- Téléporter le joueur
        SetEntityCoords(playerPed, x, y, z, false, false, false, true)
        SetEntityHeading(playerPed, spawnCoords.w)
        
        -- S'assurer que le joueur est visible et débloqué
        SetEntityVisible(playerPed, true, false)
        FreezeEntityPosition(playerPed, false)
        SetEntityCollision(playerPed, true, true)
        
        -- Fermer le loading screen
        ShutdownLoadingScreen()
        ShutdownLoadingScreenNui()
        
        -- Attendre un peu
        Citizen.Wait(1000)
        
        -- Faire un fade in
        DoScreenFadeIn(1000)
        
        -- Redonner le contrôle au joueur
        Citizen.Wait(1000)
        SetPlayerControl(PlayerId(), true, 0)
        
        -- S'assurer que la caméra du joueur est réinitialisée
        RenderScriptCams(false, false, 0, true, false)
        
        -- Notification de succès
        MainCore.Functions.Notify("Vous avez rejoint " .. spawnLocationData.name, "success")
        print("[FactionSelect] Joueur spawné avec succès aux coordonnées de la faction.")
    else
        print("[FactionSelect] ERROR: No spawn location defined in Config.FactionSpawns for faction: " .. faction)
        MainCore.Functions.Notify("Erreur: Point de spawn non défini pour votre faction.", "error")
    end
end)

-- Événement pour gérer le respawn à la mort
RegisterNetEvent('baseevents:onPlayerWasted', function(coords)
    print("[FactionSelect] Event: 'baseevents:onPlayerWasted' received.")
    
    -- Récupérer la faction du joueur
    local playerFaction = GetPlayerFaction()
    
    if playerFaction then
        print("[FactionSelect] Player has faction: " .. playerFaction)
        -- Déclencher l'événement de spawn à la faction
        TriggerEvent('sheriffs_vs_outlaws:client:spawnAtFactionLocation', playerFaction)
    else
        print("[FactionSelect] Player has no faction, using default spawn.")
        -- Utiliser le spawn par défaut si pas de faction
        exports.spawnmanager:spawnPlayer()
    end
end)

-- Fonction pour récupérer la faction du joueur
function GetPlayerFaction()
    local playerFaction = nil
    
    -- Appeler le serveur pour obtenir la faction du joueur
    TriggerServerEvent('sheriffs_vs_outlaws:server:getPlayerFaction')
    
    -- Attendre la réponse du serveur
    local p = promise.new()
    
    RegisterNetEvent('sheriffs_vs_outlaws:client:receivePlayerFaction', function(faction)
        p:resolve(faction)
    end)
    
    -- Attendre la réponse avec un timeout de 5 secondes
    local result = Citizen.Await(p)
    
    if result then
        playerFaction = result
        print("[FactionSelect] Received player faction from server: " .. playerFaction)
    else
        print("[FactionSelect] No faction received from server or timeout")
    end
    
    return playerFaction
end