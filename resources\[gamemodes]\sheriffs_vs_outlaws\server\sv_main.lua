-- Ce fichier a été déplacé vers MainCore/server/sv_core_main.lua
-- Il peut être supprimé.

-- Gestionnaire d'événement pour la mort du joueur
RegisterNetEvent('sv_sheriffs_vs_outlaws:playerDied')
AddEventHandler('sv_sheriffs_vs_outlaws:playerDied', function()
    local playerSource = source
    print(string.format('^1[SvsO Server] Le joueur %s est mort. Préparation à la réapparition.^0', playerSource))

    -- Logique de réapparition simple avec un délai
    Citizen.CreateThread(function()
        Citizen.Wait(5000) -- Attendre 5 secondes

        -- Position de réapparition de test
        local spawnX = 0.0
        local spawnY = 0.0
        local spawnZ = 70.0
        local spawnHeading = 0.0

        print(string.format('^2[SvsO Server] Réapparition du joueur %s à (%s, %s, %s).^0', playerSource, spawnX, spawnY, spawnZ))
        NetworkResurrectPlayer(playerSource, spawnX, spawnY, spawnZ, spawnHeading, true, false)
        -- Vous pourriez vouloir ajouter ici une logique pour restaurer la santé, les armes, etc.
    end)
end)

print('^2[SheriffsVsOutlaws] Logique serveur principale chargée (incluant gestion mort/réapparition).^0')