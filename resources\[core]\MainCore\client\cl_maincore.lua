local MainCore = {}

function GetCoreObject()
    return MainCore
end

MainCore.Client = {}
MainCore.Functions = {}

MainCore.Client.OnPlayerLoaded = function()
    -- Simulate player loaded event
    print("[MainCore_SHIM_CL] Triggering MainCore:Client:OnPlayerLoaded event...")
    TriggerEvent("MainCore:Client:OnPlayerLoaded") 
end

MainCore.Functions.GetPlayerData = function()
    print("[MainCore_SHIM_CL] MainCore.Functions.GetPlayerData called")
    return {
        job = { name = 'unemployed' }
        -- Add other necessary player data fields here if needed
    }
end

MainCore.Functions.Notify = function(message, type, duration)
    print("[MainCore_SHIM_CL] MainCore.Functions.Notify called: " .. tostring(message) .. " (Type: " .. tostring(type) .. ", Duration: " .. tostring(duration) .. ")")
    -- Example using native notification (optional, adapt as needed)
    -- AddTextEntry('MainCoreNotify', message)
    -- BeginTextCommandDisplayHelp('MainCoreNotify')
    -- EndTextCommandDisplayHelp(0, false, true, duration or 5000)
end

local firstSpawn = true
AddEventHandler('playerSpawned', function()
    if firstSpawn then
        firstSpawn = false
        print("[MainCore_SHIM_CL] playerSpawned, attempting to call MainCore.Client.OnPlayerLoaded()")
        if MainCore and MainCore.Client and MainCore.Client.OnPlayerLoaded then
            MainCore.Client.OnPlayerLoaded() 
        else
            print("[MainCore_SHIM_CL] ERROR: MainCore.Client.OnPlayerLoaded is not defined!")
        end
    end
end)

exports('GetCoreObject', GetCoreObject)

print('^2[MainCore_SHIM_CL] Shim côté client chargé.^0')