-- sv_journal.lua
-- Gère les entrées du journal du serveur

-- Fonction pour ajouter une entrée au journal et l'envoyer aux clients
function AddJournalEntry(message)
    if message and type(message) == "string" then
        print("[JOURNAL] " .. message) -- Affiche aussi dans la console serveur pour le débogage
        TriggerClientEvent('sheriffs_vs_outlaws:DisplayJournalMessage', -1, message)
    else
        print("[ERREUR JOURNAL] Tentative d'ajout d'une entrée de journal invalide.")
    end
end

-- Exporter la fonction pour qu'elle soit utilisable par d'autres ressources/scripts
exports('AddJournalEntry', AddJournalEntry)

print("sv_journal.lua chargé et AddJournalEntry exportée.")