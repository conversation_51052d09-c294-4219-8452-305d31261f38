print('^2[SheriffsVsOutlaws] Le script côté client a démarré.^0')

-- Variables locales pour stocker l'état du joueur
local LocalPlayer = {
    faction = nil,
    rank = nil,
    bounty = 0,
    isJailed = false,
    jailReleaseTime = 0
}

local isDead = false -- Variable d'état pour la détection de mort

-- Quand la ressource démarre côté client, envoyer une demande d'enregistrement au serveur
AddEventHandler('onClientResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then
        return -- N'exécute pas si ce n'est pas notre ressource qui démarre
    end
    print("^2[SvsO Client] La ressource a démarré. Envoi de la demande d'enregistrement au serveur...^0")
    Citizen.Wait(2000) -- Attendre un peu pour s'assurer que le serveur est prêt
    TriggerServerEvent('MainCore:server:checkFactionStatus')
    print("^2[SvsO Client] Demande de vérification de faction envoyée au serveur MainCore.^0")
end)

-- Fonction pour afficher du texte à l'écran (corrigée pour RDR3)
function DrawTxt(text, x, y, scale, fontId, r, g, b, a, alignment, useDropShadow, useOutline)
    Citizen.InvokeNative(0x07C837F9BB0374A4, fontId) -- SET_TEXT_FONT (N_0x07c837f9bb0374a4)
    Citizen.InvokeNative(0x3FEF53A8B44F0845, scale, scale) -- SET_TEXT_SCALE (N_0x3fef53a8b44f0845)
    Citizen.InvokeNative(0xBE6B9BECADC53099, r, g, b, a) -- SET_TEXT_COLOUR (N_0xbe6b9becadc53099)

    if alignment then -- 0: Center, 1: Left, 2: Right
        Citizen.InvokeNative(0x4E096588B13FF0DB, alignment) -- SET_TEXT_JUSTIFICATION (N_0x4e096588b13ff0db)
    end

    if useDropShadow then
        Citizen.InvokeNative(0x1E3E71FD6934E04C, 2, 0, 0, 0, 255) -- SET_TEXT_DROPSHADOW (N_0x1e3e71fd6934e04c)
    end

    if useOutline then
        Citizen.InvokeNative(0x2513DFB0FB8400FE) -- SET_TEXT_OUTLINE (N_0x2513dfb0fb8400fe)
    end

    Citizen.InvokeNative(0x76DBB88078C83714, "STRING") -- _BEGIN_TEXT_COMMAND_DISPLAY_TEXT (N_0x76dbb88078c83714)
    -- Using AddTextComponentSubstringPlayerName as it generally works for any string.
    Citizen.InvokeNative(0x3093A432A082F156, text) -- ADD_TEXT_COMPONENT_SUBSTRING_PLAYER_NAME (N_0x3093a432a082f156)
    Citizen.InvokeNative(0xFA925AC00EB830B9, x, y) -- _END_TEXT_COMMAND_DISPLAY_TEXT (N_0xfa925ac00eb830b9)
end

-- Fonction pour afficher une notification simple (style RDR3)
local notificationQueue = {}
local notificationActive = false

function ShowNotification(text, duration)
    table.insert(notificationQueue, {text = text, duration = duration or 5000})
    ProcessNotificationQueue()
end

function ProcessNotificationQueue()
    if notificationActive or #notificationQueue == 0 then
        return
    end

    notificationActive = true
    local notif = table.remove(notificationQueue, 1)

    local scaleform = Citizen.InvokeNative(0x25996527E59CD47B, "INGAME_WARNING_MESSAGE", Citizen.ResultAsInteger()) -- REQUEST_SCALEFORM_MOVIE (N_0x25996527e59cd47b)
    while not Citizen.InvokeNative(0xBD06C611BB958794, scaleform) do -- HAS_SCALEFORM_MOVIE_LOADED (N_0xbd06c611bb958794)
        Citizen.Wait(0)
    end

    Citizen.InvokeNative(0x5E30FD72914A3868, scaleform, "SET_WARNING_MESSAGE") -- _PUSH_SCALEFORM_MOVIE_FUNCTION (N_0x5e30fd72914a3868)
    Citizen.InvokeNative(0x04A138A8084396F9, "STRING") -- _BEGIN_TEXT_COMPONENT (N_0x04a138a8084396f9)
    Citizen.InvokeNative(0x3093A432A082F156, notif.text) -- ADD_TEXT_COMPONENT_SUBSTRING_PLAYER_NAME (N_0x3093a432a082f156)
    Citizen.InvokeNative(0x362E2D3FE93A9959) -- _END_TEXT_COMPONENT (N_0x362e2d3fe93a9959)
    Citizen.InvokeNative(0xCF537FDE4FBD4613, 100, true, true) -- _POP_SCALEFORM_MOVIE_FUNCTION_VOID (N_0xcf537fde4fbd4613)

    local startTime = GetGameTimer()
    CreateThread(function()
        while GetGameTimer() - startTime < notif.duration do
            Citizen.InvokeNative(0xDD03FCB40746F31A, scaleform, 255, 255, 255, 255) -- DRAW_SCALEFORM_MOVIE_FULLSCREEN (N_0xdd03fcb40746f31a)
            Citizen.Wait(0)
        end
        Citizen.InvokeNative(0x1D4B1610A8E2E063, scaleform) -- SET_SCALEFORM_MOVIE_AS_NO_LONGER_NEEDED (N_0x1d4b1610a8e2e063)
        notificationActive = false
        ProcessNotificationQueue() -- Traiter la suivante
    end)
end

-- Événement pour gérer la réception des informations de faction de MainCore
RegisterNetEvent('MainCore:factionJoined')
AddEventHandler('MainCore:factionJoined', function(faction, rank)
    LocalPlayer.faction = faction
    LocalPlayer.rank = rank
    print('^2[SvsO Client] Faction rejointe: ' .. tostring(faction) .. ', Rang: ' .. tostring(rank) .. '^0')
end)

-- Événement pour gérer la mise à jour de la prime de MainCore
RegisterNetEvent('MainCore:bountyUpdated')
AddEventHandler('MainCore:bountyUpdated', function(bounty)
    LocalPlayer.bounty = bounty
    print('^2[SvsO Client] Prime mise à jour: $' .. tostring(bounty) .. '^0')
end)

-- Événement pour gérer la mise à jour du rang de MainCore
RegisterNetEvent('MainCore:rankUpdated')
AddEventHandler('MainCore:rankUpdated', function(newRank, currentXp, awardedWeapons)
    LocalPlayer.rank = newRank
    print('^2[SvsO Client] Rang mis à jour: ' .. tostring(newRank) .. '^0')
    local notificationMessage = "~HUD_COLOUR_GOLD~Promotion !~s~ Nouveau rang : " .. tostring(newRank)

    if awardedWeapons and #awardedWeapons > 0 then
        local weaponNames = {}
        for _, weaponKey in ipairs(awardedWeapons) do
            -- Simple mapping for display, can be expanded
            local displayName = string.gsub(weaponKey, "WEAPON_", "")
            displayName = string.gsub(displayName, "_", " ")
            displayName = string.sub(displayName, 1, 1) .. string.lower(string.sub(displayName, 2)) -- Capitalize first letter
            table.insert(weaponNames, displayName)
        end
        notificationMessage = notificationMessage .. "~n~~INPUT_CONTEXT_A~Armes débloquées : " .. table.concat(weaponNames, ", ")
        print('^2[SvsO Client] Armes débloquées : ' .. table.concat(awardedWeapons, ", ") .. '^0')
    end
    ShowNotification(notificationMessage, 8000) -- Increased duration for more text
end)

-- Événement pour gérer le gain d'XP de MainCore
RegisterNetEvent('MainCore:xpGained')
AddEventHandler('MainCore:xpGained', function(amount, totalXp)
    print('^2[SvsO Client] XP gagné via MainCore: ' .. tostring(amount) .. '. Total XP: ' .. tostring(totalXp) .. '^0')
    local xpType = "XP"
    if LocalPlayer.faction == "sheriffs" then xpType = "Points de Mérite"
    elseif LocalPlayer.faction == "outlaws" then xpType = "Points de Déshonneur" end
    ShowNotification(string.format("~BLIP_INFO_ICON~+%s %s~s~ (Total : %s)", amount, xpType, totalXp), 5000)
end)

-- Événements liés à l'arrestation
RegisterNetEvent('sheriffs_vs_outlaws:playerJailed')
AddEventHandler('sheriffs_vs_outlaws:playerJailed', function(jailTime)
    -- Commenté temporairement pour tester
    -- LocalPlayer.isJailed = true
    -- LocalPlayer.jailReleaseTime = GetGameTimer() + (jailTime * 1000)
    -- ShowNotification("~HUD_COLOUR_REDLIGHT~Vous êtes en prison !~s~ Temps restant : " .. jailTime .. " secondes.", 7000)
end)

RegisterNetEvent('sheriffs_vs_outlaws:playerReleased')
AddEventHandler('sheriffs_vs_outlaws:playerReleased', function()
    -- Commenté temporairement pour tester
    -- LocalPlayer.isJailed = false
    -- LocalPlayer.jailReleaseTime = 0
    -- ShowNotification("~HUD_COLOUR_GREEN~Vous avez été libéré de prison.", 5000)
end)

-- Événement pour afficher les alertes de prime pour les shérifs (de MainCore)
RegisterNetEvent('MainCore:showBountyAlert')
AddEventHandler('MainCore:showBountyAlert', function(alertMessage)
    if LocalPlayer.faction == "sheriffs" then
        ShowNotification(alertMessage, 10000) -- Affiche la notification pendant 10 secondes
        print('^5[SvsO BOUNTY ALERT CLIENT] Alerte de prime reçue: ' .. alertMessage .. '^0')
    end
end)

-- Événement pour afficher les messages du journal du serveur
RegisterNetEvent('sheriffs_vs_outlaws:DisplayJournalMessage')
AddEventHandler('sheriffs_vs_outlaws:DisplayJournalMessage', function(message)
    ShowNotification("[JOURNAL] " .. message, 8000) -- Affiche le message du journal pendant 8 secondes
    print('^3[SvsO JOURNAL CLIENT] Message du journal reçu: ' .. message .. '^0')
end)

-- Boucle pour afficher les informations du joueur à l'écran et gérer les interactions
Citizen.CreateThread(function()
    local arrestKey = 0xCEFD9220 -- INPUT_CONTEXT_A (E sur clavier par défaut, Y sur manette Xbox)
    local arrestDistance = 3.0 -- Distance maximale pour initier une arrestation

    while true do
        Citizen.Wait(0) -- Exécuter à chaque frame
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        if LocalPlayer.faction then
            local factionName = LocalPlayer.faction
            if LocalPlayer.faction == "sheriffs" then factionName = "Forces de l'ordre"
            elseif LocalPlayer.faction == "outlaws" then factionName = "Hors-la-loi" end

            DrawTxt("Faction: " .. factionName, 0.05, 0.90, 0.4, 0, 255, 255, 255, 220, 1, true, true)
            DrawTxt("Rang: " .. tostring(LocalPlayer.rank or "N/A"), 0.05, 0.925, 0.4, 0, 255, 255, 255, 220, 1, true, true)

            if LocalPlayer.faction == "outlaws" then
                DrawTxt("Prime: $" .. tostring(LocalPlayer.bounty or 0), 0.05, 0.95, 0.4, 0, 255, 223, 0, 220, 1, true, true)
            end

            if LocalPlayer.isJailed then
                local timeRemaining = math.max(0, math.floor((LocalPlayer.jailReleaseTime - GetGameTimer()) / 1000))
                DrawTxt("En Prison - Temps restant: " .. timeRemaining .. "s", 0.5, 0.85, 0.5, 0, 255, 100, 100, 255, 0, true, true)
            end

        else
            DrawTxt("Aucune faction choisie. Tapez /join [sheriffs|outlaws]", 0.05, 0.90, 0.4, 0, 255, 255, 255, 220, 1, true, true)
        end

        -- Logique d'initiation d'arrestation pour les Shérifs
        if LocalPlayer.faction == "sheriffs" and not LocalPlayer.isJailed then
            local closestPlayer, closestDistance = -1, -1

            for _, otherPlayerId in ipairs(GetActivePlayers()) do
                if otherPlayerId ~= PlayerId() then -- Ne pas se cibler soi-même
                    local targetPed = GetPlayerPed(otherPlayerId)
                    if DoesEntityExist(targetPed) and not IsEntityDead(targetPed) then
                        local targetCoords = GetEntityCoords(targetPed)
                        local distance = #(playerCoords - targetCoords)

                        if distance < arrestDistance then
                            -- Pour cette version, on ne vérifie pas la faction côté client avant d'afficher le prompt
                            -- La vérification de faction se fera côté serveur
                            if closestPlayer == -1 or distance < closestDistance then
                                closestPlayer = otherPlayerId
                                closestDistance = distance
                            end
                        end
                    end
                end
            end

            if closestPlayer ~= -1 then
                -- Afficher un prompt pour arrêter
                DrawTxt("[E] Arrêter le suspect", 0.5, 0.80, 0.4, 0, 200, 200, 200, 255, 0, true, true)

                if IsControlJustReleased(0, arrestKey) then
                    ShowNotification("Tentative d'arrestation sur " .. GetPlayerName(closestPlayer) .. "...", 3000)
                    TriggerServerEvent('sheriffs_vs_outlaws:requestArrest', GetPlayerServerId(closestPlayer))
                end
            end
        end
    end
end)

-- Logique du gamemode côté client à ajouter ici
-- Par exemple : interface utilisateur, interactions joueur, affichage d'informations

-- Thread pour la détection de mort et le déclenchement de l'événement serveur
Citizen.CreateThread(function()
    local isDeadThread = false -- Variable d'état locale pour ce thread
    while true do
        Citizen.Wait(100) -- Attendre un peu pour ne pas surcharger le CPU

        local playerPed = PlayerPedId()
        if IsPedFatallyInjured(playerPed) then
            if not isDeadThread then
                isDeadThread = true
                print('^1[SvsO Client] Joueur mort. Déclenchement de l\'événement serveur.^0')
                TriggerServerEvent('sv_sheriffs_vs_outlaws:playerDied')
            end
        else
            if isDeadThread then
                isDeadThread = false -- Réinitialiser l'état quand le joueur n'est plus mort (après réapparition)
            end
        end
    end
end)

print('^2[SheriffsVsOutlaws] Logique client principale chargée.^0')
-- Thread pour l'endurance illimitée
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        -- Commenté temporairement pour tester
        -- RestorePlayerStamina(PlayerId(), 1.0)
    end
end)

-- Commande pour forcer le respawn
RegisterCommand('respawn', function(source, args, rawCommand)
    if IsEntityDead(PlayerPedId()) then
        -- Utiliser la faction stockée dans LocalPlayer
        if LocalPlayer.faction then
            TriggerEvent('sheriffs_vs_outlaws:client:spawnAtFactionLocation', LocalPlayer.faction)
            ShowNotification("Réapparition en cours...", 3000)
        else
            -- Utiliser les natives de RedM pour le respawn
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            -- Native correcte pour RedM
            Citizen.InvokeNative(0x71BC8E838B9C6035, PlayerId(), coords.x, coords.y, coords.z, GetEntityHeading(playerPed), true, true, false)
            SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))
            -- Native RedM pour nettoyer le sang
            Citizen.InvokeNative(0x8FE22675A5A45817, playerPed)
            ShowNotification("Réapparition en cours...", 3000)
        end
    else
        ShowNotification("Vous n'êtes pas mort !", 3000)
    end
end, false)