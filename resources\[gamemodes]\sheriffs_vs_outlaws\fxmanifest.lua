fx_version 'cerulean'
game 'rdr3'
rdr3_warning 'I acknowledge that this is a prerelease build of RedM, and I am aware my resources *will* become incompatible once RedM ships.'
author 'Tysor'
description 'Gamemode Shériffs vs Hors-la-loi pour RedM'
version '0.1.0'
-- Fichiers de configuration
shared_scripts {
    'config/locations.lua'
}

-- Scripts côté serveur
server_scripts {
    'server/sv_journal.lua',
    'server/sv_penalties.lua',
    'server/sv_main.lua',
    'server/sv_robbery.lua',
    'server/sv_arrest.lua',
    'server/sv_admin.lua',
    'server/sv_train_robbery.lua',
    'server/sv_manhunt.lua',
    'server/sv_theft.lua',
    'server/sv_bountycontracts.lua'
}

-- Scripts côté client
client_scripts {
    'client/cl_main.lua',
    'client/cl_robbery.lua',
    'client/cl_admin.lua',
    'client/cl_train_robbery.lua',
    'client/cl_manhunt.lua',
    'client/cl_theft.lua',
    'client/cl_bountycontracts.lua',
    'client/cl_faction_select.lua', -- Ajout du nouveau script client pour la sélection de faction
    'client/cl_sprint.lua' -- Ajout du script pour le sprint illimité
 }


server_exports {
  'AddJournalEntry',
  'ApplyArrestPenalties' -- Ajouté pour sv_arrest
  -- GetCoreObject est maintenant fourni par MainCore
}

dependencies {
    'MainCore',
    'MainCoreUI_FactionSelect'
}