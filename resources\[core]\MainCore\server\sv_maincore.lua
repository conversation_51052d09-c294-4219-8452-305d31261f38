local MainCore = {}
local ServerPlayersData = {} -- <PERSON>ur stocker les données des joueurs par source

function GetCoreObject()
    return MainCore
end

MainCore.Functions = {}

MainCore.Functions.GetPlayer = function(source)
    local src = source 
    print(("[MainCore_SHIM] MainCore.Functions.GetPlayer called for source: %s"):format(tostring(src)))

    if not ServerPlayersData[src] then
        ServerPlayersData[src] = {
            job = { name = 'unemployed', grade = 0, label = 'Unemployed' },
            charinfo = { 
                firstname = "Info",
                lastname = "Factice",
                birthdate = "01-01-1900",
                gender = 0, 
                nationality = "Unknown"
            }
        }
    elseif not ServerPlayersData[src].job then
        ServerPlayersData[src].job = { name = 'unemployed', grade = 0, label = 'Unemployed' }
    end

    if not ServerPlayersData[src].charinfo then
        ServerPlayersData[src].charinfo = {
            firstname = "Info",
            lastname = "Factice",
            birthdate = "01-01-1900",
            gender = 0,
            nationality = "Unknown"
        }
    end

    local playerDataForSource = ServerPlayersData[src]

    local Player = {
        Functions = {},
        PlayerData = playerDataForSource, 
        CitizenId = GetPlayerIdentifiers(src)[1], 
        source = src 
    }

    Player.Functions.SetJob = function(jobName, grade)
        print(("[MainCore_SHIM] Player.Functions.SetJob called for source: %s - Job: %s Grade: %s"):format(src, jobName, grade))
        
        if not ServerPlayersData[src] then 
            ServerPlayersData[src] = {} 
        end

        local gradeNumber = tonumber(grade) or 0 
        ServerPlayersData[src].job = { name = jobName, grade = gradeNumber, label = jobName }
        
        playerDataForSource.job = ServerPlayersData[src].job
        
        print(("[MainCore_SHIM] PlayerData for source %s updated: job_name=%s, job_grade=%s, job_label=%s"):format(src, playerDataForSource.job.name, playerDataForSource.job.grade, playerDataForSource.job.label))
    end

    return Player
end

exports('GetCoreObject', GetCoreObject)
print('^2[MainCore_SHIM] Shim côté serveur chargé.^0')