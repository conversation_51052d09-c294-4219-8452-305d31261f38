{"name": "@types/long", "version": "4.0.1", "description": "TypeScript definitions for long.js", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/peter<PERSON><PERSON>jmans", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/long"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5a2ae1424989c49d7303e1f5cc510288bfab1e71e0e2143cdcb9d24ff1c3dc8e", "typeScriptVersion": "2.8"}