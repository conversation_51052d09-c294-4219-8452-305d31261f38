-- Variables pour stocker les joueurs qui sont en train de braquer
local ActiveRobberies = {}

-- Fonction pour obtenir un timestamp (pour les cooldowns)
local function GetCurrentTime()
    return os.time()
end

-- Initialiser les timestamps de dernier braquage
if Config.RobbableLocations and type(Config.RobbableLocations) == "table" then
    for i in ipairs(Config.RobbableLocations) do
        if Config.LocationStatus and Config.LocationStatus[i] and Config.RobbableLocations[i] and Config.RobbableLocations[i].cooldown then
            Config.LocationStatus[i].lastRobbed = GetCurrentTime() - (Config.RobbableLocations[i].cooldown * 60)
        end
    end
end

-- Event pour vérifier si un lieu peut être braqué
RegisterNetEvent('sheriffs_vs_outlaws:checkRobbery')
AddEventHandler('sheriffs_vs_outlaws:checkRobbery', function(locationId)
    local _source = source
    -- Validate input from client
    if type(locationId) ~= 'number' then
        print(string.format("[Robbery] Invalid locationId type received from %s. Expected number, got %s.", _source, type(locationId)))
        return
    end
    local location = Config.RobbableLocations[locationId]
    local status = Config.LocationStatus[locationId]
    
    -- Vérifier si le joueur est un hors-la-loi via MainCore
    if exports.MainCore:GetPlayerFaction(_source) ~= "outlaws" then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Erreur]", "Vous devez être un hors-la-loi pour braquer."}
        })
        return
    end
    
    -- Vérifier si le lieu est déjà en train d'être braqué
    if status.beingRobbed then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Braquage]", "Ce lieu est déjà en train d'être braqué."}
        })
        return
    end
    
    -- Vérifier le cooldown
    local currentTime = GetCurrentTime()
    local timeSinceLastRob = currentTime - status.lastRobbed
    local cooldownInSeconds = location.cooldown * 60
    
    if timeSinceLastRob < cooldownInSeconds then
        local remainingTime = cooldownInSeconds - timeSinceLastRob
        local minutes = math.floor(remainingTime / 60)
        local seconds = remainingTime % 60
        
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Braquage]", "Ce lieu a déjà été braqué récemment. Réessayez dans " .. minutes .. " minute(s) et " .. seconds .. " seconde(s)."}
        })
        return
    end
    
    -- Commencer le braquage
    Config.LocationStatus[locationId].beingRobbed = true
    ActiveRobberies[_source] = locationId
    
    -- Informer le client de démarrer le braquage
    TriggerClientEvent('sheriffs_vs_outlaws:startRobbery', _source, locationId, location.robberyDuration)
    print(string.format("^3[SvsO DEBUG] Le joueur %s (ID: %s) a commencé un braquage à %s.^0", GetPlayerName(_source), _source, location.name))
    
    -- Alerter les forces de l'ordre dans la zone
    AlertLawEnforcement(_source, locationId)
end)

-- Event pour terminer un braquage avec succès
RegisterNetEvent('sheriffs_vs_outlaws:completeRobbery')
AddEventHandler('sheriffs_vs_outlaws:completeRobbery', function(locationId)
    local _source = source
    -- Validate input from client
    if type(locationId) ~= 'number' then
        print(string.format("[Robbery] Invalid locationId type received from %s. Expected number, got %s.", _source, type(locationId)))
        -- On pourrait aussi annuler le braquage côté client ici si nécessaire
        return
    end
    local location = Config.RobbableLocations[locationId]
    
    -- Vérifier si le joueur est bien en train de braquer ce lieu
    if not ActiveRobberies[_source] or ActiveRobberies[_source] ~= locationId then
        TriggerClientEvent('sheriffs_vs_outlaws:cancelRobbery', _source, "Vous n'êtes pas autorisé à terminer ce braquage.")
        return
    end
    
    -- Calculer la récompense (valeur aléatoire entre min et max)
    local reward = math.random(location.reward.min, location.reward.max)
    
    -- Mettre à jour l'argent sale du joueur
    -- Mettre à jour l'argent sale du joueur via MainCore
    exports.MainCore:UpdatePlayerDirtyMoney(_source, reward)
    
    -- Augmenter la prime du joueur via MainCore
    exports.MainCore:UpdatePlayerBounty(_source, location.bountyIncrease)
    
    -- Mettre à jour le statut du lieu
    Config.LocationStatus[locationId].beingRobbed = false
    Config.LocationStatus[locationId].lastRobbed = GetCurrentTime()
    ActiveRobberies[_source] = nil
    
    -- Ajouter de l'XP au hors-la-loi via MainCore
    exports.MainCore:AddPlayerXP(_source, Config.PointsForOutlawRobberySuccess)

    -- Informer le client
    TriggerClientEvent('sheriffs_vs_outlaws:robberySuccess', _source, reward, location.bountyIncrease, true) -- Ajout d'un paramètre pour indiquer argent sale
    print(string.format("^3[SvsO DEBUG] Le joueur %s (ID: %s) a réussi un braquage à %s. Récompense (sale): $%s, Prime +$%s, XP +%s^0",
        GetPlayerName(_source), _source, location.name, reward, location.bountyIncrease, Config.PointsForOutlawRobberySuccess))

    -- Ajouter une entrée au journal
    local journalMessage = string.format("La %s a été dévalisée par des hors-la-loi audacieux !", location.name)
    exports['sheriffs_vs_outlaws']:AddJournalEntry(journalMessage)
end)

-- Fonction pour alerter les forces de l'ordre
function AlertLawEnforcement(robberId, locationId)
    local location = Config.RobbableLocations[locationId]
    local robberCoords = GetEntityCoords(GetPlayerPed(robberId))
    
    -- Envoyer une alerte à tous les shérifs via MainCore
    local sheriffs = exports.MainCore:GetPlayersByFaction("sheriffs")
    for playerId, playerData in pairs(sheriffs) do
        local sheriffPed = GetPlayerPed(tonumber(playerId)) -- playerId est une string ici
        if DoesEntityExist(sheriffPed) then
            local sheriffCoords = GetEntityCoords(sheriffPed)
            local distance = #(sheriffCoords - robberCoords)
            
            -- Si le shérif est assez proche du braquage
            if distance <= location.alertRadius then
                TriggerClientEvent('sheriffs_vs_outlaws:robberyAlert', tonumber(playerId), location.name, location.coords)
                print(string.format("^3[SvsO DEBUG] Alerte envoyée au shérif %s (ID: %s) pour un braquage à %s.^0",
                    playerData.name, playerId, location.name))
                -- Ajouter de l'XP au shérif pour l'intervention (être alerté et proche) via MainCore
                exports.MainCore:AddPlayerXP(tonumber(playerId), Config.PointsForSheriffIntervention)
                end
            end
        end
    end

-- Événement quand un joueur quitte pour nettoyer les braquages actifs
AddEventHandler('playerDropped', function()
    local _source = source
    
    if ActiveRobberies[_source] then
        local locationId = ActiveRobberies[_source]
        Config.LocationStatus[locationId].beingRobbed = false
        ActiveRobberies[_source] = nil
        print(string.format("^3[SvsO DEBUG] Le joueur %s (ID: %s) a quitté pendant un braquage. Braquage annulé.^0", 
            GetPlayerName(_source) or "Inconnu", _source))
    end
end)

-- Commande pour les administrateurs pour réinitialiser les cooldowns (pour les tests)
RegisterCommand('resetrobberies', function(source, args, rawCommand)
    local _source = source
    
    -- Vérifier si c'est un admin (à adapter selon votre système de permissions)
    if _source > 0 and not IsPlayerAceAllowed(_source, "command.resetrobberies") then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Vous n'avez pas la permission d'utiliser cette commande."}
        })
        return
    end
    
    -- Réinitialiser tous les cooldowns
    for i in ipairs(Config.RobbableLocations) do
        Config.LocationStatus[i].beingRobbed = false
        Config.LocationStatus[i].lastRobbed = GetCurrentTime() - (Config.RobbableLocations[i].cooldown * 60)
    end
    
    if _source > 0 then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Tous les cooldowns de braquage ont été réinitialisés."}
        })
    end
    
    print("^3[SvsO DEBUG] Tous les cooldowns de braquage ont été réinitialisés.^0")
end, true) -- 'true' signifie que la commande est restreinte (admin only) 