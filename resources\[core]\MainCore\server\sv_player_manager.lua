-- Table pour stocker les données des joueurs
PlayersData = {}

-- Factions disponibles et leurs grades/rangs
Factions = {
    sheriffs = {
        name = "Forces de l'ordre",
        grades = {
            { name = "Adjoint", xpThreshold = 0, rewards = { weapons = {} } },
            { name = "Sh<PERSON><PERSON><PERSON>", xpThreshold = 100, rewards = { weapons = { "WEAPON_REVOLVER_SCHOFIELD" } } },
            { name = "Marshal Féd<PERSON>l", xpThreshold = 300, rewards = { weapons = { "WEAPON_RIFLE_SPRINGFIELD" } } },
            { name = "Commandant des Rangers", xpThreshold = 700, rewards = { weapons = { "WEAPON_SHOTGUN_PUMP", "WEAPON_REVOLVER_MAUSER" } } }
        }
    },
    outlaws = {
        name = "Hors-la-loi",
        grades = {
            { name = "Débutant", xpThreshold = 0, rewards = { weapons = {} } },
            { name = "Bandit", xpThreshold = 100, rewards = { weapons = { "WEAPON_REVOLVER_DOUBLEACTION" } } },
            { name = "Chef de gang", xpThreshold = 300, rewards = { weapons = { "WEAPON_SHOTGUN_SAWEDOFF", "WEAPON_REPEATER_WINCHESTER" } } },
            { name = "Légende du crime", xpThreshold = 700, rewards = { weapons = { "WEAPON_RIFLE_CARCANO", "WEAPON_PISTOL_VOLCANIC" } } }
        }
    }
}

Config = Config or {} -- Ensures Config table exists, preserves existing values
Config.PointsForSheriffIntervention = Config.PointsForSheriffIntervention or 10
Config.PointsForOutlawRobberySuccess = 15

Config.BountyAlertThresholds = {
    { threshold = 500, message = "Un Hors-la-loi avec une prime considérable de %s$ a été signalé : %s" },
    { threshold = 1000, message = "AVIS DE RECHERCHE : %s est activement recherché avec une prime de %s$ !" },
    { threshold = 2000, message = "DANGER PUBLIC : %s est une menace majeure avec une prime de %s$ ! Mobilisation générale !" }
}

-- Événement lorsqu'un joueur commence à se connecter
AddEventHandler('playerConnecting', function(playerName, setKickReason, deferrals)
    local src = source
    print(string.format("^3[MainCore DEBUG] playerConnecting: Name: %s, Source: %s", playerName, src))
    deferrals.defer()
    -- Citizen.Wait(50) -- Un petit délai n'est généralement pas nécessaire ici si deferrals.update suit
    deferrals.update(string.format("Bonjour %s, chargement de vos données pour MainCore...", playerName))
    -- Citizen.Wait(100) -- Un autre petit délai, peut être ajusté ou supprimé
    deferrals.done()
    print(string.format("^3[MainCore DEBUG] playerConnecting: Deferrals done for %s (Source: %s)", playerName, src))
end)

-- Événement lorsqu'un joueur a complètement rejoint et est actif (semble ne pas se déclencher)
-- AddEventHandler('playerJoined', function()
--     local src = source
--     local playerName = GetPlayerName(src)
--     print(string.format("^2[MainCore DEBUG] playerJoined: PlayerName: %s, Source: %s. Initialisation des données...", playerName, src))
--
--     PlayersData[src] = {
--         name = playerName,
--         faction = nil,
--         rank = nil,
--         xp = 0, -- Points d'expérience/mérite/déshonneur
--         bounty = 0,
--         money = 50, -- Argent de départ
--         dirtyMoney = 0, -- Argent sale de départ
--         inventory = {}, -- Inventaire de départ
--         id = src, -- Stocker l'ID pour vérification
--         lastBountyAlertLevel = 0 -- Initialiser le niveau d'alerte de prime
--     }
--     print(string.format("^2[MainCore DEBUG] playerJoined: PlayersData[%s] initialisé pour %s. Faction: %s, XP: %s, DirtyMoney: %s", src, playerName, PlayersData[src].faction, PlayersData[src].xp, PlayersData[src].dirtyMoney))
--     -- Le client déclenchera 'MainCore:server:checkFactionStatus' pour potentiellement afficher le menu
-- end)

-- Événement pour vérifier si le joueur doit voir le menu de sélection de faction
RegisterNetEvent('MainCore:server:checkFactionStatus', function()
    local src = source
    local playerName = GetPlayerName(src) -- Récupérer le nom du joueur ici
    print(string.format("^3[MainCore DEBUG] Event 'MainCore:server:checkFactionStatus' received from source: %s (%s)", src, playerName))

    -- Initialiser les données du joueur ici si elles n'existent pas encore
    if not PlayersData[src] then
        print(string.format("^2[MainCore DEBUG] Initialisation des données pour %s (ID: %s) dans checkFactionStatus...", playerName, src))
        PlayersData[src] = {
            name = playerName,
            faction = nil,
            rank = nil,
            xp = 0,
            bounty = 0,
            money = 50,
            dirtyMoney = 0,
            inventory = {},
            id = src,
            lastBountyAlertLevel = 0
        }
        print(string.format("^2[MainCore DEBUG] PlayersData[%s] initialisé pour %s. Faction: %s", src, playerName, PlayersData[src].faction))
    end

    if PlayersData[src].faction then -- Maintenant PlayersData[src] devrait exister
        print(string.format("^2[MainCore DEBUG] checkFactionStatus: Joueur %s (ID: %s) a déjà la faction %s. Pas d'affichage du menu.", PlayersData[src].name, src, PlayersData[src].faction))
    else
        -- PlayersData[src] existe mais PlayersData[src].faction est nil
        print(string.format("^2[MainCore DEBUG] checkFactionStatus: Joueur %s (ID: %s) n'a pas de faction. Préparation de l'envoi de 'MainCore:client:showFactionMenu'.", PlayersData[src].name, src))
        print(string.format("^2[MainCore DEBUG] Déclenchement de 'MainCore:client:showFactionMenu' pour la source %s", src))
        TriggerClientEvent('MainCore:client:showFactionMenu', src)
    end
end)

-- Événement lorsqu'un joueur quitte le serveur
AddEventHandler('playerDropped', function(reason)
    local src = source
    local playerName = "Joueur inconnu"
    if PlayersData[src] and PlayersData[src].name then
        playerName = PlayersData[src].name
    elseif GetPlayerName(src) then -- Tentative de récupérer le nom si pas dans PlayersData
        playerName = GetPlayerName(src)
    end

    print(string.format("^1[MainCore DEBUG] playerDropped: PlayerName: %s, Source: %s, Reason: %s", playerName, src, reason))
    if PlayersData[src] then
        PlayersData[src] = nil
        print(string.format("^1[MainCore DEBUG] playerDropped: PlayersData[%s] (pour %s) effacé.", src, playerName))
    else
        print(string.format("^1[MainCore DEBUG] playerDropped: PlayersData[%s] était déjà nil pour %s.", src, playerName))
    end
end)

-- Événement réseau pour rejoindre une faction (utilisé par NUI et potentiellement par la commande)
RegisterNetEvent('MainCore:server:joinFaction', function(factionChoiceParam)
    local src = source
    local factionChoice = string.lower(factionChoiceParam or "")

    print(string.format('^3[MainCore DEBUG] Événement MainCore:server:joinFaction reçu par ID: %s (Type: %s) pour la faction: %s^0', src, type(src), factionChoice))
    print(string.format('^3[MainCore DEBUG] Vérification de PlayersData[%s]...', src))
    if PlayersData then
        print('^3[MainCore DEBUG] Table PlayersData existe. Contenu (partiel):')
        local count = 0
        for k, v in pairs(PlayersData) do
            print(string.format('  - Clé: %s (Type: %s), Nom: %s', k, type(k), v and v.name or "N/A"))
            count = count + 1
            if count >= 5 then print('  ...'); break end -- Limiter l'affichage
        end
        if count == 0 then print('  (Table PlayersData est vide)') end
    else
        print('^1[MainCore DEBUG] La table PlayersData elle-même est nil !^0')
    end

    if not PlayersData[src] then
        print(string.format('^1[MainCore DEBUG] ÉCHEC de la vérification: PlayersData[%s] est %s.^0', src, tostring(PlayersData[src])))
        print('^1[MainCore DEBUG] Erreur: PlayersData[src] non trouvé pour ID: ' .. src .. '. Vérifiez les logs playerJoined.^0')
        TriggerClientEvent('chat:addMessage', src, { args = { "[Erreur]", "Vos données joueur n'ont pas pu être chargées correctement (Code: PJNF). Essayez de vous reconnecter ou contactez un admin." }})
        return
    end
    print('^3[MainCore DEBUG] PlayersData[src] trouvé pour ID: ' .. src .. '. Nom: '.. PlayersData[src].name ..'^0')

    if PlayersData[src].faction then
        print('^3[MainCore DEBUG] Le joueur (ID: '.. src ..') a déjà une faction: ' .. PlayersData[src].faction .. '^0')
        TriggerClientEvent('chat:addMessage', src, { args = { "[Faction]", "Vous avez déjà choisi une faction : " .. Factions[PlayersData[src].faction].name }})
        return
    end
    print('^3[MainCore DEBUG] Le joueur (ID: '.. src ..') n\'a pas encore de faction.^0')

    if Factions[factionChoice] then
        print('^3[MainCore DEBUG] factionChoice "'.. factionChoice ..'" trouvée dans la table Factions pour ID: '.. src ..'.^0')
        PlayersData[src].faction = factionChoice
        PlayersData[src].rank = Factions[factionChoice].grades[1].name -- Assign the name of the first rank

        -- S'assurer que le framework est notifié du changement de job/faction si applicable
        local CoreShim = exports.MainCore:GetCoreObject() -- On utilise l'export de MainCore lui-même maintenant
        if CoreShim then
            local Player = CoreShim.Functions.GetPlayer(src)
            if Player then
                local jobGrade = 1 -- ou le grade correspondant au premier rang de votre faction
                Player.Functions.SetJob(factionChoice, jobGrade)
                print(string.format('^2[MainCore Shim] Job pour %s (ID: %s) mis à %s grade %s.^0', Player.PlayerData.charinfo.firstname, src, factionChoice, jobGrade))
            else
                print(string.format('^1[MainCore Shim] Impossible de récupérer l\'objet Player pour src %s lors de la mise à jour du job.', src))
            end
        else
            print('^1[MainCore Shim] CoreShim (MainCore) n\'est pas chargé, impossible de mettre à jour le job du joueur.')
        end

        TriggerClientEvent('chat:addMessage', src, {
            args = { "[Faction]", "Vous avez rejoint les " .. Factions[factionChoice].name .. " en tant que " .. PlayersData[src].rank .. "." }
        })
        print('^2[MainCore] Le joueur ' .. PlayersData[src].name .. ' (ID: ' .. src .. ') a rejoint la faction: ' .. factionChoice .. '^0')
        TriggerClientEvent('MainCore:factionJoined', src, factionChoice, PlayersData[src].rank) -- Notifie le client (utile pour màj UI, etc.)

        -- Déclencher l'événement client pour téléporter le joueur au spawn de sa faction
        TriggerClientEvent('sheriffs_vs_outlaws:client:spawnAtFactionLocation', src, factionChoice)
        print(string.format('^2[MainCore] Déclenchement de "sheriffs_vs_outlaws:client:spawnAtFactionLocation" pour %s (ID: %s) faction: %s', PlayersData[src].name, src, factionChoice))

    else
        print('^1[MainCore DEBUG] factionChoice "'.. factionChoice ..'" NON trouvée dans la table Factions pour ID: '.. src ..'.^0')
        TriggerClientEvent('chat:addMessage', src, { args = { "[Faction]", "Faction invalide. Choisissez 'sheriffs' ou 'outlaws'." }})
    end
end)

-- Commande pour choisir une faction (maintenant elle appelle l'événement réseau de MainCore)
-- Cette commande pourrait rester dans le gamemode, mais pour l'instant, on la garde ici pour la cohérence du core.
-- Si elle reste ici, le gamemode devra s'assurer de ne pas la redéclarer.
RegisterCommand('joinfaction', function(sourceCmd, args, rawCommand) -- Renommée pour éviter conflit potentiel
    local src = sourceCmd
    local factionChoice = string.lower(args[1] or "")
    print('^3[MainCore DEBUG] Commande /joinfaction exécutée par ID: ' .. src .. ' avec choix: ' .. factionChoice .. '^0')

    if factionChoice == "sheriffs" or factionChoice == "outlaws" then
        TriggerServerEvent('MainCore:server:joinFaction', factionChoice)
    else
        TriggerClientEvent('chat:addMessage', src, { args = { "[Faction]", "Faction invalide. Utilisez /joinfaction sheriffs ou /joinfaction outlaws." }})
    end
end, false) -- false pour permettre l'exécution par tout le monde

-- Exemple de fonction pour mettre à jour la prime d'un joueur
function UpdatePlayerBounty(playerId, amount)
    if PlayersData[playerId] and PlayersData[playerId].faction == 'outlaws' then
        PlayersData[playerId].bounty = PlayersData[playerId].bounty + amount
        if PlayersData[playerId].bounty < 0 then PlayersData[playerId].bounty = 0 end
    end -- Fin du if PlayersData[playerId] and PlayersData[playerId].faction == 'outlaws'
    print('^2[MainCore] Prime de ' .. PlayersData[playerId].name .. ' (ID: '..playerId..') mise à jour à : $' .. PlayersData[playerId].bounty .. '^0')
    TriggerClientEvent('MainCore:bountyUpdated', playerId, PlayersData[playerId].bounty)
    CheckBountyAlerts(playerId) -- Vérifier les alertes de prime après la mise à jour
end
exports('UpdatePlayerBounty', UpdatePlayerBounty)

-- Fonction pour récupérer la prime d'un joueur
function GetPlayerBounty(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].bounty
    end -- Fin du if PlayersData[playerId]
    return 0 -- Retourne 0 si le joueur n'est pas trouvé ou n'a pas de prime
end
exports('GetPlayerBounty', GetPlayerBounty)
-- Fonction pour récupérer les données des joueurs d'une faction spécifique
function GetPlayersByFaction(factionName)
    local playersInFaction = {}
    for playerId, playerData in pairs(PlayersData) do
        if playerData.faction == factionName then
            -- On retourne une copie pour éviter la modification directe de PlayersData depuis l'extérieur
            -- et pour ne pas exposer toute la table playerData si ce n'est pas nécessaire.
            -- Pour l'instant, on retourne l'ID et les données complètes.
            -- À affiner selon les besoins réels des gamemodes.
            playersInFaction[playerId] = playerData
        end
    end -- Fin du for
    return playersInFaction
end
exports('GetPlayersByFaction', GetPlayersByFaction)

-- Fonctions pour la gestion de l'arrestation (appelées par le gamemode)
function SetPlayerJailedStatus(playerId, status, jailTimeInSeconds)
    if PlayersData[playerId] then
        PlayersData[playerId].isJailed = status
        if status and jailTimeInSeconds and jailTimeInSeconds > 0 then
            PlayersData[playerId].jailReleaseTime = GetGameTimer() + (jailTimeInSeconds * 1000)
        else
            PlayersData[playerId].jailReleaseTime = nil
        end
        print(string.format("[MainCore] Statut de prison pour %s (ID: %s) mis à %s. Temps restant: %s", GetPlayerName(playerId), playerId, tostring(status), jailTimeInSeconds or "N/A"))
    end -- Fin du if PlayersData[playerId]
end
exports('SetPlayerJailedStatus', SetPlayerJailedStatus)

function ResetPlayerBounty(playerId)
    if PlayersData[playerId] then
        PlayersData[playerId].bounty = 0
        print(string.format("[MainCore] Prime pour %s (ID: %s) réinitialisée.", GetPlayerName(playerId), playerId))
        -- L'alerte de prime est aussi réinitialisée car la prime est à 0
        if PlayersData[playerId].lastBountyAlertLevel then
             PlayersData[playerId].lastBountyAlertLevel = 0
        end
    end -- Fin du if PlayersData[playerId]
end
exports('ResetPlayerBounty', ResetPlayerBounty)

function ResetPlayerBountyAlertLevel(playerId)
    if PlayersData[playerId] then
        PlayersData[playerId].lastBountyAlertLevel = 0
        print(string.format("[MainCore] Niveau d'alerte de prime pour %s (ID: %s) réinitialisé.", GetPlayerName(playerId), playerId))
    end -- Fin du if PlayersData[playerId]
end
exports('ResetPlayerBountyAlertLevel', ResetPlayerBountyAlertLevel)

function IsPlayerJailed(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].isJailed or false
    end -- Fin du if PlayersData[playerId]
    return false
end
exports('IsPlayerJailed', IsPlayerJailed)

function GetPlayerJailReleaseTime(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].jailReleaseTime or nil
    end -- Fin du if PlayersData[playerId]
    return nil
end
exports('GetPlayerJailReleaseTime', GetPlayerJailReleaseTime)
function GetPlayerMoney(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].money or 0
    end -- Fin du if PlayersData[playerId]
    return 0
end
exports('GetPlayerMoney', GetPlayerMoney)

function GetPlayerRank(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].rank
    end -- Fin du if PlayersData[playerId]
    return nil -- Ajout du return nil manquant
end
exports('GetPlayerRank', GetPlayerRank)

function GetPlayerDirtyMoney(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].dirtyMoney or 0
    end -- Fin du if PlayersData[playerId]
    return 0
end
exports('GetPlayerDirtyMoney', GetPlayerDirtyMoney)

function GetAllPlayersData()
    -- Retourne une copie pour éviter la modification externe directe de la table PlayersData originale.
    -- C'est une copie superficielle. Si les sous-tables sont modifiées, elles le seront dans l'original.
    -- Pour une isolation complète, une copie profonde serait nécessaire, mais plus coûteuse.
    local playersDataCopy = {}
    for k, v in pairs(PlayersData) do
        playersDataCopy[k] = v -- Copie la référence à la sous-table du joueur
    end -- Fin du for
    return playersDataCopy
end
exports('GetAllPlayersData', GetAllPlayersData)

-- Note: Les exports pour GetPlayersByFaction, GetPlayerBounty, UpdatePlayerBounty ont été déplacés après leurs définitions respectives.
-- L'export pour UpdatePlayerBounty était déjà présent à la ligne 296.

-- Fonction pour vérifier et envoyer les alertes de prime
function CheckBountyAlerts(playerId)
    local playerData = PlayersData[playerId]
    if not playerData or playerData.faction ~= 'outlaws' then
        return
    end

    local currentBounty = playerData.bounty
    local lastAlertLevel = playerData.lastBountyAlertLevel or 0
    local newAlertLevel = lastAlertLevel
    local alertToSend = nil

    -- Trier les seuils par ordre décroissant pour s'assurer qu'on prend le plus pertinent
    local sortedThresholds = {}
    for _, data in ipairs(Config.BountyAlertThresholds) do
        table.insert(sortedThresholds, data)
    end
    table.sort(sortedThresholds, function(a, b) return a.threshold > b.threshold end)

    for _, alertData in ipairs(sortedThresholds) do
        if currentBounty >= alertData.threshold and alertData.threshold > lastAlertLevel then
            newAlertLevel = alertData.threshold
            alertToSend = string.format(alertData.message, playerData.name, currentBounty) -- Inversion pour correspondre à l'exemple
            print(string.format("^5[MainCore BOUNTY ALERT] Joueur %s (ID: %s) a atteint le seuil de prime %s$. Message: %s", playerData.name, playerId, alertData.threshold, alertToSend))
            break -- On envoie seulement l'alerte pour le plus haut nouveau seuil atteint
        end
    end

    if alertToSend and newAlertLevel > lastAlertLevel then
        playerData.lastBountyAlertLevel = newAlertLevel
        print(string.format("^5[MainCore BOUNTY ALERT] lastBountyAlertLevel pour %s (ID: %s) mis à jour à %s", playerData.name, playerId, newAlertLevel))

        for targetId, targetData in pairs(PlayersData) do
            if targetData.faction == 'sheriffs' then
                TriggerClientEvent('MainCore:showBountyAlert', tonumber(targetId), alertToSend)
                print(string.format("^5[MainCore BOUNTY ALERT] Alerte envoyée au Shérif %s (ID: %s)", targetData.name, targetId))
            end
        end

        -- Ajout au journal pour l'alerte de prime la plus élevée
        -- Ceci est un point de couplage avec le gamemode. Idéalement, MainCore émettrait un événement
        -- et le gamemode s'y abonnerait pour ajouter l'entrée au journal.
        -- Pour l'instant, on garde l'appel direct en attendant une refactorisation plus poussée.
        if newAlertLevel == sortedThresholds[1].threshold then -- sortedThresholds[1] est le plus élevé car trié en ordre décroissant
            local journalMessage = string.format("Un avis de recherche majeur a été émis pour %s, dont la tête est mise à prix pour %s$ ! La région entière est en alerte.", playerData.name, currentBounty)
            -- Tenter d'appeler l'export du gamemode. Cela suppose que le gamemode s'appelle 'sheriffs_vs_outlaws'
            -- et qu'il exporte 'AddJournalEntry'.
            -- Si MainCore doit être indépendant, cette partie doit être gérée par des événements.
            local gamemodeResourceName = GlobalState.GamemodeResourceName or "sheriffs_vs_outlaws" -- Exemple de configuration globale
            if exports[gamemodeResourceName] and exports[gamemodeResourceName].AddJournalEntry then
                 exports[gamemodeResourceName]:AddJournalEntry(journalMessage)
            else
                print(string.format("^1[MainCore WARNING] Impossible d'ajouter l'entrée au journal. L'export 'AddJournalEntry' de la ressource '%s' n'est pas disponible.", gamemodeResourceName))
            end
        end
    end -- Fin du if alertToSend and newAlertLevel > lastAlertLevel
end -- Fin de la fonction CheckBountyAlerts

-- Fonction pour récupérer la faction d'un joueur
function GetPlayerFaction(playerId)
    if PlayersData[playerId] then
        return PlayersData[playerId].faction
    end -- Fin du if PlayersData[playerId]
    return nil
end
exports('GetPlayerFaction', GetPlayerFaction)

-- Fonction pour mettre à jour l'argent d'un joueur
function UpdatePlayerMoney(playerId, amount)
    if PlayersData[playerId] then
        PlayersData[playerId].money = PlayersData[playerId].money + amount
        print('^2[MainCore] Argent de ' .. PlayersData[playerId].name .. ' (ID: '..playerId..') mis à jour à : $' .. PlayersData[playerId].money .. '^0')
        TriggerClientEvent('MainCore:moneyUpdated', playerId, PlayersData[playerId].money)
    end -- Fin du if PlayersData[playerId]
end
exports('UpdatePlayerMoney', UpdatePlayerMoney)

-- Fonction pour mettre à jour l'argent sale d'un joueur
function UpdatePlayerDirtyMoney(playerId, amount)
    if PlayersData[playerId] then
        PlayersData[playerId].dirtyMoney = PlayersData[playerId].dirtyMoney + amount
        if PlayersData[playerId].dirtyMoney < 0 then -- Empêcher l'argent sale négatif
            PlayersData[playerId].dirtyMoney = 0
        end
        print('^2[MainCore] Argent sale de ' .. PlayersData[playerId].name .. ' (ID: '..playerId..') mis à jour à : $' .. PlayersData[playerId].dirtyMoney .. '^0')
        TriggerClientEvent('MainCore:dirtyMoneyUpdated', playerId, PlayersData[playerId].dirtyMoney)
    end -- Fin du if PlayersData[playerId]
end
exports('UpdatePlayerDirtyMoney', UpdatePlayerDirtyMoney)

-- Fonction pour ajouter de l'XP à un joueur et vérifier la montée en grade
function AddPlayerXP(playerId, amount)
    local player = PlayersData[playerId]
    if not player then
        print(string.format("^1[MainCore DEBUG] AddPlayerXP: Joueur avec ID %s non trouvé dans PlayersData.", playerId))
        return -- Fin du if not player
    end

    player.xp = player.xp + amount
    print(string.format("^2[MainCore] Joueur %s (ID: %s) a reçu %s XP. Total XP: %s", player.name, playerId, amount, player.xp))
    TriggerClientEvent('MainCore:xpGained', playerId, amount, player.xp) -- Notifier le client du gain d'XP

    CheckRankUp(playerId)
end -- Fin de la fonction AddPlayerXP
exports('AddPlayerXP', AddPlayerXP)

-- Fonction pour vérifier et appliquer la montée en grade
function CheckRankUp(playerId)
    local player = PlayersData[playerId]
    if not player or not player.faction then
        print(string.format("^1[MainCore DEBUG] CheckRankUp: Joueur avec ID %s non trouvé ou sans faction.", playerId))
        return -- Fin du if not player or not player.faction
    end

    local factionData = Factions[player.faction]
    if not factionData or not factionData.grades then
        print(string.format("^1[MainCore DEBUG] CheckRankUp: Données de faction ou grades non trouvés pour %s.", player.faction))
        return -- Fin du if not factionData or not factionData.grades
    end

    local currentRankIndex = -1
    for i, gradeData in ipairs(factionData.grades) do
        if gradeData.name == player.rank then
            currentRankIndex = i
            break -- Fin du if gradeData.name == player.rank
        end
    end -- Fin du for i, gradeData

    if currentRankIndex == -1 then
        print(string.format("^1[MainCore DEBUG] CheckRankUp: Rang actuel '%s' du joueur %s non trouvé dans la faction %s.", player.rank, player.name, player.faction))
        return -- Fin du if currentRankIndex == -1
    end

    local newRankIndex = -1
    -- Itérer depuis le plus haut grade pour trouver le grade actuel atteignable
    for i = #factionData.grades, 1, -1 do
        if player.xp >= factionData.grades[i].xpThreshold then
            if i > currentRankIndex then -- S'il y a une promotion possible
                newRankIndex = i
            end -- Fin du if i > currentRankIndex
            break -- On a trouvé le plus haut grade atteignable avec l'XP actuel
        end -- Fin du if player.xp >= factionData.grades[i].xpThreshold
    end -- Fin du for i = #factionData.grades, 1, -1

    if newRankIndex ~= -1 and newRankIndex > currentRankIndex then
        local newGradeData = factionData.grades[newRankIndex]
        if newGradeData and newGradeData.name ~= player.rank then
            local oldRankName = player.rank
            player.rank = newGradeData.name
            local awardedWeapons = {}

            print(string.format("^2[MainCore] %s (ID: %s) est promu de %s à %s!", player.name, playerId, oldRankName, player.rank))

            -- Attribuer les récompenses
            if newGradeData.rewards and newGradeData.rewards.weapons then
                for _, weaponName in ipairs(newGradeData.rewards.weapons) do
                    local weaponHash = GetHashKey(weaponName)
                    if not HasPedGotWeapon(GetPlayerPed(playerId), weaponHash, false) then
                        GiveWeaponToPed(GetPlayerPed(playerId), weaponHash, 100, false, true) -- 100 munitions initiales
                        AddAmmoToPed(GetPlayerPed(playerId), weaponHash, 100) -- Ajoute 100 munitions supplémentaires
                        table.insert(awardedWeapons, weaponName)
                        print(string.format("^2[MainCore] %s (ID: %s) a reçu l'arme: %s", player.name, playerId, weaponName))
                    else
                        print(string.format("^3[MainCore DEBUG] %s (ID: %s) possède déjà l'arme: %s", player.name, playerId, weaponName))
                    end
                end -- Fin du if not HasPedGotWeapon / else
            end -- Fin du for _, weaponName
            -- Notifier le client de la promotion et des armes débloquées
            TriggerClientEvent('MainCore:rankUpdated', playerId, player.rank, player.xp, awardedWeapons)

            -- Ajout au journal pour promotion à un grade élevé (couplage avec gamemode)
            if newRankIndex == #factionData.grades then -- Si c'est le dernier grade (le plus élevé)
                local journalMessage = string.format("%s a été promu au rang de %s, une figure %s de la région !", player.name, player.rank, player.faction == "sheriffs" and "respectée" or "crainte")
                local gamemodeResourceName = GlobalState.GamemodeResourceName or "sheriffs_vs_outlaws"
                if exports[gamemodeResourceName] and exports[gamemodeResourceName].AddJournalEntry then
                    exports[gamemodeResourceName]:AddJournalEntry(journalMessage)
                else
                    print(string.format("^1[MainCore WARNING] Impossible d'ajouter l'entrée au journal pour promotion. L'export 'AddJournalEntry' de la ressource '%s' n'est pas disponible.", gamemodeResourceName))
                end -- Fin du if exports[gamemodeResourceName] / else
            end -- Fin du if newRankIndex == #factionData.grades
        end -- Fin du if newGradeData and newGradeData.name ~= player.rank
    end -- Fin du if newRankIndex ~= -1 and newRankIndex > currentRankIndex
end -- Fin de la fonction CheckRankUp

-- Écouteur côté serveur pour l'enregistrement du joueur (si nécessaire, peut être redondant avec playerJoined)

-- Gestion de la mort d'un joueur
AddEventHandler('playerDied', function(killerType, deathCoords)
    local playerId = source
    local killerId = GetPlayerKiller(playerId)

    print(string.format("^1[MainCore DEATH] Joueur %s (ID: %s) est mort. Tueur ID: %s, Type de tueur: %s", GetPlayerName(playerId) or "Inconnu", playerId, killerId or "N/A", killerType or "N/A"))

    -- Logique de base pour la mort (pénalités, etc.)
    if PlayersData[playerId] then
        if PlayersData[playerId].faction == "outlaws" then
            local moneyLost = math.floor(PlayersData[playerId].money * 0.1) -- Perdre 10% de son argent
            UpdatePlayerMoney(playerId, -moneyLost)
            TriggerClientEvent('chat:addMessage', playerId, { args = {"[Mort]", string.format("Vous êtes mort et avez perdu %s$.", moneyLost) }})
            print(string.format("^1[MainCore DEATH] Le Hors-la-loi %s a perdu %s$.", GetPlayerName(playerId), moneyLost))
        elseif PlayersData[playerId].faction == "sheriffs" then
            TriggerClientEvent('chat:addMessage', playerId, { args = {"[Mort]", "Vous avez été neutralisé." }})
        end -- Fin du if PlayersData[playerId].faction == "outlaws" / elseif == "sheriffs"
    end -- Fin du if PlayersData[playerId]

    -- Émettre un événement pour que les gamemodes puissent réagir à la mort
    TriggerEvent('MainCore:playerDied', playerId, killerId, killerType, deathCoords)
    -- Le gamemode 'sheriffs_vs_outlaws' devra écouter 'MainCore:playerDied' pour sa logique spécifique de chasse à l'homme.
end)

-- Fonction basique pour récupérer les armes du joueur (placeholder)
-- TODO: Implémenter une vraie logique de récupération des armes si un système d'inventaire est ajouté.
function GetPlayerWeapons(playerId)
    print(string.format("^3[MainCore DEBUG] GetPlayerWeapons appelé pour playerId %s. Retourne une table vide (placeholder).", playerId))
    -- Retourne une table vide pour éviter les erreurs dans les scripts qui l'appellent,
    -- en attendant une implémentation correcte basée sur un système d'inventaire.
    return {}
end
exports('GetPlayerWeapons', GetPlayerWeapons)

-- Fonction pour retirer une arme d'un joueur (placeholder)
function RemovePlayerWeapon(playerId, weaponHash)
    print(string.format("^3[MainCore DEBUG] RemovePlayerWeapon appelé pour playerId %s, weaponHash %s. Fonction placeholder.", playerId, weaponHash))
    -- Utiliser la native RedM pour retirer l'arme
    local playerPed = GetPlayerPed(playerId)
    if playerPed and DoesEntityExist(playerPed) then
        RemoveWeaponFromPed(playerPed, weaponHash)
        return true
    end
    return false
end
exports('RemovePlayerWeapon', RemovePlayerWeapon)
print('^2[MainCore] Logique principale du serveur chargée.^0')