-- Liste des identifiants d'administrateurs (à personnaliser)
local Admins = {
    -- Vous pouvez ajouter des identifiants Steam, License, Discord, etc.
    -- Exemple: "steam:110000112345678", "license:1234567890abcdef", "discord:123456789012345678"
    "ip:127.0.0.1" -- Ceci permettra à l'hôte local d'être admin
}

-- Variable pour stocker les IDs des joueurs définies comme admin en jeu
local AdminPlayers = {}

-- Événement pour vérifier si un joueur est administrateur
RegisterNetEvent('sheriffs_vs_outlaws:checkAdminStatus')
AddEventHandler('sheriffs_vs_outlaws:checkAdminStatus', function()
    local _source = source
    local isAdmin = IsPlayerAdmin(_source)
    TriggerClientEvent('sheriffs_vs_outlaws:setAdminStatus', _source, isAdmin)
    
    if isAdmin then
        print(string.format("^3[SvsO Admin] Le joueur %s (ID: %s) a été identifié comme administrateur.^0", 
            GetPlayerName(_source), _source))
    end
end)

-- Fonction pour vérifier si un joueur est administrateur
function IsPlayerAdmin(playerId)
    -- Si le joueur est dans la console serveur (ID 0)
    if playerId == 0 then
        return true
    end
    
    -- Vérifier si le joueur est dans la liste des AdminPlayers
    if AdminPlayers[playerId] then
        return true
    end
    
    -- Vérifier si le joueur a l'ACE "command.tpwaypoint"
    if IsPlayerAceAllowed(playerId, "command.tpwaypoint") then
        return true
    end
    
    -- Vérifier si l'identifiant du joueur est dans la liste d'administrateurs
    local identifiers = GetPlayerIdentifiers(playerId)
    for _, id in ipairs(identifiers) do
        for _, adminId in ipairs(Admins) do
            if id == adminId or id:lower() == adminId:lower() then
                return true
            end
        end
    end
    
    return false
end

-- Ajouter la commande pour donner/retirer le statut d'administrateur à un joueur
RegisterCommand('setadmin', function(source, args, rawCommand)
    local _source = source
    
    -- Si c'est la console, toujours permettre
    if _source == 0 then
        -- Vérifier les arguments
        if #args ~= 1 then
            print("^3[Admin] Usage: setadmin [ID]^0")
            return
        end
        
        local targetId = tonumber(args[1])
        if not targetId then
            print("^3[Admin] ID invalide.^0")
            return
        end
        
        -- Vérifier si le joueur existe
        if GetPlayerName(targetId) == nil then
            print("^3[Admin] Joueur introuvable.^0")
            return
        end
        
        -- Définir le joueur comme administrateur
        AdminPlayers[targetId] = true
        TriggerClientEvent('sheriffs_vs_outlaws:setAdminStatus', targetId, true)
        
        -- Notification
        print(string.format("^3[Admin] %s (ID: %s) a été défini comme administrateur.^0", GetPlayerName(targetId), targetId))
        TriggerClientEvent('chat:addMessage', targetId, {
            args = {"[Admin]", "Vous avez reçu les privilèges d'administrateur."}
        })
        return
    end
    
    -- Pour les joueurs, vérifier qu'ils sont admin
    if not IsPlayerAdmin(_source) then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Vous n'avez pas la permission d'utiliser cette commande."}
        })
        return
    end
    
    -- Vérifier les arguments
    if #args ~= 1 then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Usage: /setadmin [ID]"}
        })
        return
    end
    
    local targetId = tonumber(args[1])
    if not targetId then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "ID invalide."}
        })
        return
    end
    
    -- Vérifier si le joueur existe
    if GetPlayerName(targetId) == nil then
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Joueur introuvable."}
        })
        return
    end
    
    -- Définir le joueur comme administrateur
    AdminPlayers[targetId] = true
    TriggerClientEvent('sheriffs_vs_outlaws:setAdminStatus', targetId, true)
    
    -- Notifications
    TriggerClientEvent('chat:addMessage', _source, {
        args = {"[Admin]", GetPlayerName(targetId) .. " a été défini comme administrateur."}
    })
    
    TriggerClientEvent('chat:addMessage', targetId, {
        args = {"[Admin]", "Vous avez reçu les privilèges d'administrateur."}
    })
    
    print(string.format("^3[SvsO Admin] %s (ID: %s) a donné le statut d'administrateur à %s (ID: %s).^0", 
        GetPlayerName(_source), _source, GetPlayerName(targetId), targetId))
end, false) -- false pour permettre l'exécution par tout le monde, la vérification est faite dans la fonction

-- Commande simplifiée pour devenir admin soi-même (à utiliser pour les tests)
RegisterCommand('makeadmin', function(source, args, rawCommand)
    local _source = source
    
    -- Si la commande vient de la console, ignorer
    if _source == 0 then return end
    
    -- Vérifier si la commande est exécutée par l'ID 1 (généralement le premier joueur qui se connecte)
    -- Ceci est une mesure temporaire pour les tests
    if _source == 1 then
        AdminPlayers[_source] = true
        TriggerClientEvent('sheriffs_vs_outlaws:setAdminStatus', _source, true)
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Vous êtes maintenant administrateur."}
        })
        print(string.format("^3[SvsO Admin] %s (ID: %s) s'est défini comme administrateur via /makeadmin.^0", 
            GetPlayerName(_source), _source))
    else
        TriggerClientEvent('chat:addMessage', _source, {
            args = {"[Admin]", "Cette commande n'est disponible que pour le joueur avec l'ID 1."}
        })
    end
end, false)

print('^3[SheriffsVsOutlaws] Système d\'administration chargé.^0') 