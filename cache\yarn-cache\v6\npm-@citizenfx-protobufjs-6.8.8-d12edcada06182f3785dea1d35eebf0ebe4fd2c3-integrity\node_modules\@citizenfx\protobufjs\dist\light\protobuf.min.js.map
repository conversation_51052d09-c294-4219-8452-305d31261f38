{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light", "../src/index-minimal.js", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "char<PERSON>t", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "Float32Array", "f32", "f8b", "le", "writeFloat_f32_cpy", "val", "buf", "pos", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "writeFloat_ieee754", "writeUint", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "f64", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "moduleName", "mod", "eval", "e", "path", "isAbsolute", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "converter", "Enum", "util", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "resolvedType", "values", "repeated", "typeDefault", "fullName", "isUnsigned", "type", "genValuePartial_toObject", "fromObject", "mtype", "fields", "fieldsArray", "name", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "arrayDefault", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "ref", "id", "keyType", "types", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "create", "constructor", "className", "Namespace", "comment", "comments", "TypeError", "reserved", "fromJSON", "json", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "rule", "extend", "isObject", "toLowerCase", "message", "defaultValue", "<PERSON>", "extensionField", "declaringField", "_packed", "defineProperty", "get", "getOption", "setOption", "ifNotSet", "resolved", "defaults", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "configure", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Writer", "BufferWriter", "rpc", "roots", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "nested", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "define", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "Root_", "fieldNames", "oneof", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skip", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "parse", "common", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "process", "parsed", "imports", "weakImports", "queued", "weak", "idx", "lastIndexOf", "altname", "substring", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "extendedType", "sisterField", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "method", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "service", "inherited", "methodsArray", "rpcService", "methodName", "lcFirst", "isReserved", "m", "q", "s", "oneofs", "extensions", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "wrapper", "originalThis", "fork", "l<PERSON>im", "typeName", "target", "bake", "o", "key", "safePropBackslashRe", "safePropQuoteRe", "ucFirst", "str", "toUpperCase", "camelCaseRe", "camelCase", "a", "decorateRoot", "enumerable", "decorateEnumIndex", "zero", "zzEncode", "zeroHash", "from", "parseInt", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "newError", "CustomError", "captureStackTrace", "stack", "pool", "global", "window", "versions", "node", "Number", "isFinite", "isset", "isSet", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "invalid", "genVerifyKey", "genVerifyValue", "oneofProp", "expected", "type_url", "substr", "Op", "next", "noop", "State", "head", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeBytesBuffer", "copy", "writeStringBuffer", "byteLength", "$require", "$module", "amd", "isLong"], "mappings": ";;;;;;CAAA,SAAAA,GAAA,aAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,GChCAA,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,gCCxCA,IAAAE,EAAAf,EAOAe,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAG,OAAAF,MACAC,EACA,OAAAE,KAAAC,KAAA,EAAAL,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAI,EAAAlB,MAAA,IAGAmB,EAAAnB,MAAA,KAGAoB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAT,EAAAU,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAxB,EAAAyB,OAAA,SAAAxB,EAAAU,EAAAnB,GAIA,IAHA,IAEAsB,EAFAF,EAAApB,EACAyB,EAAA,EAEAR,EAAA,EAAAA,EAAAR,EAAAV,QAAA,CACA,IAAAmC,EAAAzB,EAAA0B,WAAAlB,KACA,GAAA,KAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAjD,EACA,MAAAmD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,KAAAsB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,GAAAsB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,EAAAsB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAAhC,EAAAoB,GAQAZ,EAAA6B,KAAA,SAAA5B,GACA,MAAA,mEAAA4B,KAAA5B,0BC/HA,SAAA6B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAtD,GAGA,IAAAwD,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAvD,MAAAoD,EAAAlD,OAAA,GACAsD,EAAAxD,MAAAoD,EAAAlD,QACAuD,EAAA,EACAA,EAAAL,EAAAlD,QACAqD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAAhD,MAAA,KAAA6C,GAAA7C,MAAA,KAAA8C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA3D,MAAAC,UAAAC,OAAA,GACA0D,EAAA,EACAA,EAAAD,EAAAzD,QACAyD,EAAAC,GAAA3D,YAAA2D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,OAAAC,EAAAjC,GACA,IAAA,IAAA,OAAAf,KAAAiD,MAAAD,GAAAjC,GACA,IAAA,IAAA,OAAAmC,KAAAC,UAAAH,GACA,IAAA,IAAA,OAAAA,EAAAjC,GAEA,MAAA,MAEA6B,IAAAD,EAAAzD,OACA,MAAAqC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAoB,GACA,MAAA,aAAAA,GAAAzB,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAC,QAAA6C,GAiGAQ,SAAA,wBCzFA,SAAAoB,IAOAC,KAAAC,EAAA,IAfA5E,EAAAC,QAAAyE,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA5C,KAAA,CACAjC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAtF,EACAkF,KAAAC,EAAA,QAEA,GAAA1E,IAAAT,EACAkF,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAtD,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,KAAAA,EACA+E,EAAAC,OAAAzD,EAAA,KAEAA,EAGA,OAAAkD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA3D,EAAA,EACAA,EAAAnB,UAAAC,QACA6E,EAAAjD,KAAA7B,UAAAmB,MACA,IAAAA,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,GAAAa,MAAAkE,EAAAxD,KAAAtB,IAAAiF,GAEA,OAAAT,4BCzEA3E,EAAAC,QAAAoF,EAEA,IAAAC,EAAAvF,EAAA,GAGAwF,EAFAxF,EAAA,EAEAyF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,MANA,mBAAAD,GACAC,EAAAD,EACAA,EAAA,IACAA,IACAA,EAAA,IAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,WAIAgC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAzG,EAKA,GAAA,IAAAmG,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CACA,IAAArE,EAAAiE,EAAAQ,SACA,IAAAzE,EAAA,CACAA,EAAA,GACA,IAAA,IAAAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA9F,SAAAkB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,IAEA,OAAAkE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA3E,GAAAA,GAEA,OAAAgE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,qCC1BA,SAAAC,EAAA1G,GAwNA,MArNA,oBAAA2G,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAE,EAAA,IAAAR,WAAAO,EAAAlF,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAE,EAAAC,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAM,EAAAH,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAO,EAAAH,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAGA,SAAAS,EAAAJ,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAjBA5G,EAAAsH,aAAAR,EAAAC,EAAAI,EAEAnH,EAAAuH,aAAAT,EAAAK,EAAAJ,EAmBA/G,EAAAwH,YAAAV,EAAAM,EAAAC,EAEArH,EAAAyH,YAAAX,EAAAO,EAAAD,EA9CA,GAiDA,WAEA,SAAAM,EAAAC,EAAAX,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,QACA,GAAAW,MAAAb,GACAW,EAAA,WAAAV,EAAAC,QACA,GAAA,qBAAAF,EACAW,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,QACA,GAAAF,EAAA,sBACAW,GAAAC,GAAA,GAAAxG,KAAA0G,MAAAd,EAAA,yBAAA,EAAAC,EAAAC,OACA,CACA,IAAAa,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KAEAL,GAAAC,GAAA,GAAAG,EAAA,KAAA,GADA,QAAA3G,KAAA0G,MAAAd,EAAA5F,KAAA6G,IAAA,GAAAF,GAAA,YACA,EAAAd,EAAAC,IAOA,SAAAgB,EAAAC,EAAAlB,EAAAC,GACA,IAAAkB,EAAAD,EAAAlB,EAAAC,GACAU,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,EAAA,QAAAD,EACA,OAAA,MAAAL,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,qBAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,MAAAM,EAAA,SAdArI,EAAAsH,aAAAI,EAAAc,KAAA,KAAAC,GACAzI,EAAAuH,aAAAG,EAAAc,KAAA,KAAAE,GAgBA1I,EAAAwH,YAAAU,EAAAM,KAAA,KAAAG,GACA3I,EAAAyH,YAAAS,EAAAM,KAAA,KAAAI,GAvCA,GA4CA,oBAAAC,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAhC,EAAA,IAAAR,WAAAyC,EAAApH,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAkC,EAAA/B,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAmC,EAAAhC,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAoC,EAAAhC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAGA,SAAAI,EAAAjC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAzBA9I,EAAAmJ,cAAArC,EAAAiC,EAAAC,EAEAhJ,EAAAoJ,cAAAtC,EAAAkC,EAAAD,EA2BA/I,EAAAqJ,aAAAvC,EAAAmC,EAAAC,EAEAlJ,EAAAsJ,aAAAxC,EAAAoC,EAAAD,EA9DA,GAiEA,WAEA,SAAAM,EAAA5B,EAAA6B,EAAAC,EAAAzC,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,EAAAuC,QACA,GAAA5B,MAAAb,GACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,WAAAV,EAAAC,EAAAuC,QACA,GAAA,sBAAAzC,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,EAAAuC,OACA,CACA,IAAApB,EACA,GAAArB,EAAA,uBAEAW,GADAU,EAAArB,EAAA,UACA,EAAAC,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAS,EAAA,cAAA,EAAApB,EAAAC,EAAAuC,OACA,CACA,IAAA1B,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KACA,OAAAD,IACAA,EAAA,MAEAJ,EAAA,kBADAU,EAAArB,EAAA5F,KAAA6G,IAAA,GAAAF,MACA,EAAAd,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAApB,EAAAC,EAAAuC,KAQA,SAAAC,EAAAvB,EAAAqB,EAAAC,EAAAxC,EAAAC,GACA,IAAAyC,EAAAxB,EAAAlB,EAAAC,EAAAsC,GACAI,EAAAzB,EAAAlB,EAAAC,EAAAuC,GACA7B,EAAA,GAAAgC,GAAA,IAAA,EACA7B,EAAA6B,IAAA,GAAA,KACAvB,EAAA,YAAA,QAAAuB,GAAAD,EACA,OAAA,OAAA5B,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,OAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBAfArI,EAAAmJ,cAAAI,EAAAf,KAAA,KAAAC,EAAA,EAAA,GACAzI,EAAAoJ,cAAAG,EAAAf,KAAA,KAAAE,EAAA,EAAA,GAiBA1I,EAAAqJ,aAAAK,EAAAlB,KAAA,KAAAG,EAAA,EAAA,GACA3I,EAAAsJ,aAAAI,EAAAlB,KAAA,KAAAI,EAAA,EAAA,GAnDA,GAuDA5I,EAKA,SAAAyI,EAAAzB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAA0B,EAAA1B,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAA2B,EAAA1B,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAA0B,EAAA3B,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAnH,EAAAC,QAAA0G,EAAAA,2BCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAmD,OAAAC,KAAAoG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAjK,EAAAC,QAAAuF,0BCMA,IAAA0E,EAAAjK,EAEAkK,EAMAD,EAAAC,WAAA,SAAAD,GACA,MAAA,eAAArH,KAAAqH,IAGAE,EAMAF,EAAAE,UAAA,SAAAF,GAGA,IAAAnI,GAFAmI,EAAAA,EAAAhG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAmG,MAAA,KACAC,EAAAH,EAAAD,GACAK,EAAA,GACAD,IACAC,EAAAxI,EAAAyI,QAAA,KACA,IAAA,IAAA/I,EAAA,EAAAA,EAAAM,EAAAxB,QACA,OAAAwB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAmD,SAAAzD,EAAA,GACA6I,EACAvI,EAAAmD,OAAAzD,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAmD,OAAAzD,EAAA,KAEAA,EAEA,OAAA8I,EAAAxI,EAAAQ,KAAA,MAUA2H,EAAAtJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,IACAP,EAAAO,GACAA,GACAC,IACAF,EAAAL,EAAAK,KACAA,EAAAA,EAAAvG,QAAA,iBAAA,KAAA3D,OAAA6J,EAAAK,EAAA,IAAAC,GAAAA,0BC9DA1K,EAAAC,QA6BA,SAAA2K,EAAAtI,EAAAuI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEA,IAAA0G,EAAA5E,EAAA2I,KAAAD,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACA0G,6BCtCA,IAAAgE,EAAAjL,EAOAiL,EAAA3K,OAAA,SAAAU,GAGA,IAFA,IAAAkK,EAAA,EACAzI,EAAA,EACAjB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAiB,EAAAzB,EAAA0B,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAzB,EAAA0B,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAApK,EAAAU,EAAAnB,GAIA,IAHA,IACA8K,EACAC,EAFA3J,EAAApB,EAGAiB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACA6J,EAAArK,EAAA0B,WAAAlB,IACA,IACAE,EAAAnB,KAAA8K,GACAA,EAAA,KACA3J,EAAAnB,KAAA8K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAtK,EAAA0B,WAAAlB,EAAA,MACA6J,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KACA9J,EACAE,EAAAnB,KAAA8K,GAAA,GAAA,IACA3J,EAAAnB,KAAA8K,GAAA,GAAA,GAAA,KAIA3J,EAAAnB,KAAA8K,GAAA,GAAA,IAHA3J,EAAAnB,KAAA8K,GAAA,EAAA,GAAA,KANA3J,EAAAnB,KAAA,GAAA8K,EAAA,KAcA,OAAA9K,EAAAoB,4BClGA,IAAA4J,EAAAvL,EAEAwL,EAAA1L,EAAA,IACA2L,EAAA3L,EAAA,IAWA,SAAA4L,EAAAC,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,eAAAG,GACA,IAAA,IAAAE,EAAAJ,EAAAG,aAAAC,OAAAtI,EAAAD,OAAAC,KAAAsI,GAAAxK,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAoK,EAAAK,UAAAD,EAAAtI,EAAAlC,MAAAoK,EAAAM,aAAAP,EACA,YACAA,EACA,UAAAjI,EAAAlC,GADAmK,CAEA,WAAAK,EAAAtI,EAAAlC,IAFAmK,CAGA,SAAAG,EAAAE,EAAAtI,EAAAlC,IAHAmK,CAIA,SACAA,EACA,UACAA,EACA,4BAAAG,EADAH,CAEA,sBAAAC,EAAAO,SAAA,oBAFAR,CAGA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAM,GAAA,EACA,OAAAR,EAAAS,MACA,IAAA,SACA,IAAA,QAAAV,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,gBADAA,CAEA,6CAAAG,EAAAA,EAAAM,EAFAT,CAGA,iCAAAG,EAHAH,CAIA,uBAAAG,EAAAA,EAJAH,CAKA,iCAAAG,EALAH,CAMA,UAAAG,EAAAA,EANAH,CAOA,iCAAAG,EAPAH,CAQA,+DAAAG,EAAAA,EAAAA,EAAAM,EAAA,OAAA,IACA,MACA,IAAA,QAAAT,EACA,4BAAAG,EADAH,CAEA,wEAAAG,EAAAA,EAAAA,EAFAH,CAGA,sBAAAG,EAHAH,CAIA,UAAAG,EAAAA,GACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,GACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,IAOA,OAAAH,EAmEA,SAAAW,EAAAX,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAG,aACAH,EAAAG,wBAAAP,EAAAG,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAM,GAAA,EACA,OAAAR,EAAAS,MACA,IAAA,SACA,IAAA,QAAAV,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,GAAAN,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EA5FAJ,EAAAgB,WAAA,SAAAC,GAEA,IAAAC,EAAAD,EAAAE,YACAf,EAAAF,EAAA5I,QAAA,CAAA,KAAA2J,EAAAG,KAAA,cAAAlB,CACA,6BADAA,CAEA,YACA,IAAAgB,EAAAnM,OAAA,OAAAqL,EACA,wBACAA,EACA,uBACA,IAAA,IAAAnK,EAAA,EAAAA,EAAAiL,EAAAnM,SAAAkB,EAAA,CACA,IAAAoK,EAAAa,EAAAjL,GAAAb,UACAmL,EAAAL,EAAAmB,SAAAhB,EAAAe,MAGAf,EAAAiB,KAAAlB,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAO,SAAA,oBAHAR,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAApK,EAAAsK,EAAA,UAAAJ,CACA,IADAA,CAEA,MAGAE,EAAAK,UAAAN,EACA,WAAAG,EADAH,CAEA,0BAAAG,EAFAH,CAGA,sBAAAC,EAAAO,SAAA,mBAHAR,CAIA,SAAAG,EAJAH,CAKA,iCAAAG,GACAJ,EAAAC,EAAAC,EAAApK,EAAAsK,EAAA,MAAAJ,CACA,IADAA,CAEA,OAIAE,EAAAG,wBAAAP,GAAAG,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAApK,EAAAsK,GACAF,EAAAG,wBAAAP,GAAAG,EACA,MAEA,OAAAA,EACA,aAwDAJ,EAAAuB,SAAA,SAAAN,GAEA,IAAAC,EAAAD,EAAAE,YAAArK,QAAA0K,KAAAtB,EAAAuB,mBACA,IAAAP,EAAAnM,OACA,OAAAmL,EAAA5I,SAAA4I,CAAA,aAUA,IATA,IAAAE,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA2J,EAAAG,KAAA,YAAAlB,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAwB,EAAA,GACAC,EAAA,GACAC,EAAA,GACA3L,EAAA,EACAA,EAAAiL,EAAAnM,SAAAkB,EACAiL,EAAAjL,GAAA4L,SACAX,EAAAjL,GAAAb,UAAAsL,SAAAgB,EACAR,EAAAjL,GAAAqL,IAAAK,EACAC,GAAAjL,KAAAuK,EAAAjL,IAEA,GAAAyL,EAAA3M,OAAA,CAEA,IAFAqL,EACA,6BACAnK,EAAA,EAAAA,EAAAyL,EAAA3M,SAAAkB,EAAAmK,EACA,SAAAF,EAAAmB,SAAAK,EAAAzL,GAAAmL,OACAhB,EACA,KAGA,GAAAuB,EAAA5M,OAAA,CAEA,IAFAqL,EACA,8BACAnK,EAAA,EAAAA,EAAA0L,EAAA5M,SAAAkB,EAAAmK,EACA,SAAAF,EAAAmB,SAAAM,EAAA1L,GAAAmL,OACAhB,EACA,KAGA,GAAAwB,EAAA7M,OAAA,CAEA,IAFAqL,EACA,mBACAnK,EAAA,EAAAA,EAAA2L,EAAA7M,SAAAkB,EAAA,CACA,IAAAoK,EAAAuB,EAAA3L,GACAsK,EAAAL,EAAAmB,SAAAhB,EAAAe,MACA,GAAAf,EAAAG,wBAAAP,EAAAG,EACA,6BAAAG,EAAAF,EAAAG,aAAAsB,WAAAzB,EAAAM,aAAAN,EAAAM,kBACA,GAAAN,EAAA0B,KAAA3B,EACA,iBADAA,CAEA,gCAAAC,EAAAM,YAAAqB,IAAA3B,EAAAM,YAAAsB,KAAA5B,EAAAM,YAAAuB,SAFA9B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAM,YAAA9I,WAAAwI,EAAAM,YAAAwB,iBACA,GAAA9B,EAAA+B,MAAA,CACA,IAAAC,EAAA,IAAAxN,MAAAwE,UAAAvC,MAAA2I,KAAAY,EAAAM,aAAA5J,KAAA,KAAA,IACAqJ,EACA,6BAAAG,EAAA3J,OAAAC,aAAAtB,MAAAqB,OAAAyJ,EAAAM,aADAP,CAEA,QAFAA,CAGA,SAAAG,EAAA8B,EAHAjC,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,UACAA,EACA,SAAAG,EAAAF,EAAAM,aACAP,EACA,KAEA,IAAAkC,GAAA,EACA,IAAArM,EAAA,EAAAA,EAAAiL,EAAAnM,SAAAkB,EAAA,CACAoK,EAAAa,EAAAjL,GAAA,IACAhB,EAAAgM,EAAAsB,EAAAC,QAAAnC,GACAE,EAAAL,EAAAmB,SAAAhB,EAAAe,MACAf,EAAAiB,KACAgB,IAAAA,GAAA,EAAAlC,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAW,EAAAX,EAAAC,EAAApL,EAAAsL,EAAA,WAAAQ,CACA,MACAV,EAAAK,UAAAN,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAQ,EAAAX,EAAAC,EAAApL,EAAAsL,EAAA,MAAAQ,CACA,OACAX,EACA,uCAAAG,EAAAF,EAAAe,MACAL,EAAAX,EAAAC,EAAApL,EAAAsL,GACAF,EAAAwB,QAAAzB,EACA,eADAA,CAEA,SAAAF,EAAAmB,SAAAhB,EAAAwB,OAAAT,MAAAf,EAAAe,OAEAhB,EACA,KAEA,OAAAA,EACA,+CCjSA5L,EAAAC,QAeA,SAAAwM,GAEA,IAAAb,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA2J,EAAAG,KAAA,UAAAlB,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAAe,EAAAE,YAAAsB,OAAA,SAAApC,GAAA,OAAAA,EAAAiB,MAAAvM,OAAA,KAAA,IAHAmL,CAIA,kBAJAA,CAKA,oBACAe,EAAAyB,OAAAtC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAnK,EAAA,EACAA,EAAAgL,EAAAE,YAAApM,SAAAkB,EAAA,CACA,IAAAoK,EAAAY,EAAAsB,EAAAtM,GAAAb,UACA0L,EAAAT,EAAAG,wBAAAP,EAAA,QAAAI,EAAAS,KACA6B,EAAA,IAAAzC,EAAAmB,SAAAhB,EAAAe,MAAAhB,EACA,WAAAC,EAAAuC,IAGAvC,EAAAiB,KAAAlB,EACA,iBADAA,CAEA,4BAAAuC,EAFAvC,CAGA,QAAAuC,EAHAvC,CAIA,WAAAC,EAAAwC,QAJAzC,CAKA,WACA0C,EAAAf,KAAA1B,EAAAwC,WAAA5O,EACA6O,EAAAC,MAAAjC,KAAA7M,EAAAmM,EACA,8EAAAuC,EAAA1M,GACAmK,EACA,sDAAAuC,EAAA7B,GAEAgC,EAAAC,MAAAjC,KAAA7M,EAAAmM,EACA,uCAAAuC,EAAA1M,GACAmK,EACA,eAAAuC,EAAA7B,IAIAT,EAAAK,UAAAN,EAEA,uBAAAuC,EAAAA,EAFAvC,CAGA,QAAAuC,GAGAG,EAAAE,OAAAlC,KAAA7M,GAAAmM,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAuC,EAAA7B,EAJAV,CAKA,SAGA0C,EAAAC,MAAAjC,KAAA7M,EAAAmM,EAAAC,EAAAG,aAAAkC,MACA,+BACA,0CAAAC,EAAA1M,GACAmK,EACA,kBAAAuC,EAAA7B,IAGAgC,EAAAC,MAAAjC,KAAA7M,EAAAmM,EAAAC,EAAAG,aAAAkC,MACA,yBACA,oCAAAC,EAAA1M,GACAmK,EACA,YAAAuC,EAAA7B,GACAV,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAnK,EAAA,EAAAA,EAAAgL,EAAAsB,EAAAxN,SAAAkB,EAAA,CACA,IAAAgN,EAAAhC,EAAAsB,EAAAtM,GACAgN,EAAAC,UAAA9C,EACA,4BAAA6C,EAAA7B,KADAhB,CAEA,4CA3FA,qBA2FA6C,EA3FA7B,KAAA,KA8FA,OAAAhB,EACA,aApGA,IAAAH,EAAA1L,EAAA,IACAuO,EAAAvO,EAAA,IACA2L,EAAA3L,EAAA,4CCJAC,EAAAC,QA0BA,SAAAwM,GAWA,IATA,IAIA0B,EAJAvC,EAAAF,EAAA5I,QAAA,CAAA,IAAA,KAAA2J,EAAAG,KAAA,UAAAlB,CACA,SADAA,CAEA,qBAKAgB,EAAAD,EAAAE,YAAArK,QAAA0K,KAAAtB,EAAAuB,mBAEAxL,EAAA,EAAAA,EAAAiL,EAAAnM,SAAAkB,EAAA,CACA,IAAAoK,EAAAa,EAAAjL,GAAAb,UACAH,EAAAgM,EAAAsB,EAAAC,QAAAnC,GACAS,EAAAT,EAAAG,wBAAAP,EAAA,QAAAI,EAAAS,KACAqC,EAAAL,EAAAC,MAAAjC,GACA6B,EAAA,IAAAzC,EAAAmB,SAAAhB,EAAAe,MAGAf,EAAAiB,KACAlB,EACA,sCAAAuC,EAAAtC,EAAAe,KADAhB,CAEA,mDAAAuC,EAFAvC,CAGA,4CAAAC,EAAAuC,IAAA,EAAA,KAAA,EAAA,EAAAE,EAAAM,OAAA/C,EAAAwC,SAAAxC,EAAAwC,SACAM,IAAAlP,EAAAmM,EACA,oEAAAnL,EAAA0N,GACAvC,EACA,qCAAA,GAAA+C,EAAArC,EAAA6B,GACAvC,EACA,IADAA,CAEA,MAGAC,EAAAK,UAAAN,EACA,2BAAAuC,EAAAA,GAGAtC,EAAA2C,QAAAF,EAAAE,OAAAlC,KAAA7M,EAAAmM,EAEA,uBAAAC,EAAAuC,IAAA,EAAA,KAAA,EAFAxC,CAGA,+BAAAuC,EAHAvC,CAIA,cAAAU,EAAA6B,EAJAvC,CAKA,eAGAA,EAEA,+BAAAuC,GACAQ,IAAAlP,EACAoP,EAAAjD,EAAAC,EAAApL,EAAA0N,EAAA,OACAvC,EACA,0BAAAC,EAAAuC,IAAA,EAAAO,KAAA,EAAArC,EAAA6B,IAEAvC,EACA,OAIAC,EAAAiD,UAAAlD,EACA,qCAAAuC,EAAAtC,EAAAe,MAEA+B,IAAAlP,EACAoP,EAAAjD,EAAAC,EAAApL,EAAA0N,GACAvC,EACA,uBAAAC,EAAAuC,IAAA,EAAAO,KAAA,EAAArC,EAAA6B,IAKA,OAAAvC,EACA,aA9FA,IAAAH,EAAA1L,EAAA,IACAuO,EAAAvO,EAAA,IACA2L,EAAA3L,EAAA,IAWA,SAAA8O,EAAAjD,EAAAC,EAAAC,EAAAqC,GACA,OAAAtC,EAAAG,aAAAkC,MACAtC,EAAA,+CAAAE,EAAAqC,GAAAtC,EAAAuC,IAAA,EAAA,KAAA,GAAAvC,EAAAuC,IAAA,EAAA,KAAA,GACAxC,EAAA,oDAAAE,EAAAqC,GAAAtC,EAAAuC,IAAA,EAAA,KAAA,4CClBApO,EAAAC,QAAAwL,EAGA,IAAAsD,EAAAhP,EAAA,MACA0L,EAAA5G,UAAAnB,OAAAsL,OAAAD,EAAAlK,YAAAoK,YAAAxD,GAAAyD,UAAA,OAEA,IAAAC,EAAApP,EAAA,IACA2L,EAAA3L,EAAA,IAaA,SAAA0L,EAAAmB,EAAAX,EAAAvG,EAAA0J,EAAAC,GAGA,GAFAN,EAAA9D,KAAAtG,KAAAiI,EAAAlH,GAEAuG,GAAA,iBAAAA,EACA,MAAAqD,UAAA,4BAoCA,GA9BA3K,KAAA2I,WAAA,GAMA3I,KAAAsH,OAAAvI,OAAAsL,OAAArK,KAAA2I,YAMA3I,KAAAyK,QAAAA,EAMAzK,KAAA0K,SAAAA,GAAA,GAMA1K,KAAA4K,SAAA9P,EAMAwM,EACA,IAAA,IAAAtI,EAAAD,OAAAC,KAAAsI,GAAAxK,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACA,iBAAAwK,EAAAtI,EAAAlC,MACAkD,KAAA2I,WAAA3I,KAAAsH,OAAAtI,EAAAlC,IAAAwK,EAAAtI,EAAAlC,KAAAkC,EAAAlC,IAiBAgK,EAAA+D,SAAA,SAAA5C,EAAA6C,GACA,IAAAC,EAAA,IAAAjE,EAAAmB,EAAA6C,EAAAxD,OAAAwD,EAAA/J,QAAA+J,EAAAL,QAAAK,EAAAJ,UAEA,OADAK,EAAAH,SAAAE,EAAAF,SACAG,GAQAjE,EAAA5G,UAAA8K,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,UAAApI,KAAAe,QACA,SAAAf,KAAAsH,OACA,WAAAtH,KAAA4K,UAAA5K,KAAA4K,SAAAhP,OAAAoE,KAAA4K,SAAA9P,EACA,UAAAoQ,EAAAlL,KAAAyK,QAAA3P,EACA,WAAAoQ,EAAAlL,KAAA0K,SAAA5P,KAaAgM,EAAA5G,UAAAiL,IAAA,SAAAlD,EAAAwB,EAAAgB,GAGA,IAAA1D,EAAAqE,SAAAnD,GACA,MAAA0C,UAAA,yBAEA,IAAA5D,EAAAsE,UAAA5B,GACA,MAAAkB,UAAA,yBAEA,GAAA3K,KAAAsH,OAAAW,KAAAnN,EACA,MAAAmD,MAAA,mBAAAgK,EAAA,QAAAjI,MAEA,GAAAA,KAAAsL,aAAA7B,GACA,MAAAxL,MAAA,MAAAwL,EAAA,mBAAAzJ,MAEA,GAAAA,KAAAuL,eAAAtD,GACA,MAAAhK,MAAA,SAAAgK,EAAA,oBAAAjI,MAEA,GAAAA,KAAA2I,WAAAc,KAAA3O,EAAA,CACA,IAAAkF,KAAAe,UAAAf,KAAAe,QAAAyK,YACA,MAAAvN,MAAA,gBAAAwL,EAAA,OAAAzJ,MACAA,KAAAsH,OAAAW,GAAAwB,OAEAzJ,KAAA2I,WAAA3I,KAAAsH,OAAAW,GAAAwB,GAAAxB,EAGA,OADAjI,KAAA0K,SAAAzC,GAAAwC,GAAA,KACAzK,MAUA8G,EAAA5G,UAAAuL,OAAA,SAAAxD,GAEA,IAAAlB,EAAAqE,SAAAnD,GACA,MAAA0C,UAAA,yBAEA,IAAArI,EAAAtC,KAAAsH,OAAAW,GACA,GAAA,MAAA3F,EACA,MAAArE,MAAA,SAAAgK,EAAA,uBAAAjI,MAMA,cAJAA,KAAA2I,WAAArG,UACAtC,KAAAsH,OAAAW,UACAjI,KAAA0K,SAAAzC,GAEAjI,MAQA8G,EAAA5G,UAAAoL,aAAA,SAAA7B,GACA,OAAAe,EAAAc,aAAAtL,KAAA4K,SAAAnB,IAQA3C,EAAA5G,UAAAqL,eAAA,SAAAtD,GACA,OAAAuC,EAAAe,eAAAvL,KAAA4K,SAAA3C,4CClLA5M,EAAAC,QAAAoQ,EAGA,IAAAtB,EAAAhP,EAAA,MACAsQ,EAAAxL,UAAAnB,OAAAsL,OAAAD,EAAAlK,YAAAoK,YAAAoB,GAAAnB,UAAA,QAEA,IAIAoB,EAJA7E,EAAA1L,EAAA,IACAuO,EAAAvO,EAAA,IACA2L,EAAA3L,EAAA,IAIAwQ,EAAA,+BAyCA,SAAAF,EAAAzD,EAAAwB,EAAA9B,EAAAkE,EAAAC,EAAA/K,EAAA0J,GAcA,GAZA1D,EAAAgF,SAAAF,IACApB,EAAAqB,EACA/K,EAAA8K,EACAA,EAAAC,EAAAhR,GACAiM,EAAAgF,SAAAD,KACArB,EAAA1J,EACAA,EAAA+K,EACAA,EAAAhR,GAGAsP,EAAA9D,KAAAtG,KAAAiI,EAAAlH,IAEAgG,EAAAsE,UAAA5B,IAAAA,EAAA,EACA,MAAAkB,UAAA,qCAEA,IAAA5D,EAAAqE,SAAAzD,GACA,MAAAgD,UAAA,yBAEA,GAAAkB,IAAA/Q,IAAA8Q,EAAA1N,KAAA2N,EAAAA,EAAAnN,WAAAsN,eACA,MAAArB,UAAA,8BAEA,GAAAmB,IAAAhR,IAAAiM,EAAAqE,SAAAU,GACA,MAAAnB,UAAA,2BAMA3K,KAAA6L,KAAAA,GAAA,aAAAA,EAAAA,EAAA/Q,EAMAkF,KAAA2H,KAAAA,EAMA3H,KAAAyJ,GAAAA,EAMAzJ,KAAA8L,OAAAA,GAAAhR,EAMAkF,KAAA+J,SAAA,aAAA8B,EAMA7L,KAAAmK,UAAAnK,KAAA+J,SAMA/J,KAAAuH,SAAA,aAAAsE,EAMA7L,KAAAmI,KAAA,EAMAnI,KAAAiM,QAAA,KAMAjM,KAAA0I,OAAA,KAMA1I,KAAAwH,YAAA,KAMAxH,KAAAkM,aAAA,KAMAlM,KAAA4I,OAAA7B,EAAAoF,MAAAxC,EAAAf,KAAAjB,KAAA7M,EAMAkF,KAAAiJ,MAAA,UAAAtB,EAMA3H,KAAAqH,aAAA,KAMArH,KAAAoM,eAAA,KAMApM,KAAAqM,eAAA,KAOArM,KAAAsM,EAAA,KAMAtM,KAAAyK,QAAAA,EA7JAiB,EAAAb,SAAA,SAAA5C,EAAA6C,GACA,OAAA,IAAAY,EAAAzD,EAAA6C,EAAArB,GAAAqB,EAAAnD,KAAAmD,EAAAe,KAAAf,EAAAgB,OAAAhB,EAAA/J,QAAA+J,EAAAL,UAqKA1L,OAAAwN,eAAAb,EAAAxL,UAAA,SAAA,CACAsM,IAAA,WAIA,OAFA,OAAAxM,KAAAsM,IACAtM,KAAAsM,GAAA,IAAAtM,KAAAyM,UAAA,WACAzM,KAAAsM,KAOAZ,EAAAxL,UAAAwM,UAAA,SAAAzE,EAAAvI,EAAAiN,GAGA,MAFA,WAAA1E,IACAjI,KAAAsM,EAAA,MACAlC,EAAAlK,UAAAwM,UAAApG,KAAAtG,KAAAiI,EAAAvI,EAAAiN,IAwBAjB,EAAAxL,UAAA8K,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,OAAA,aAAApI,KAAA6L,MAAA7L,KAAA6L,MAAA/Q,EACA,OAAAkF,KAAA2H,KACA,KAAA3H,KAAAyJ,GACA,SAAAzJ,KAAA8L,OACA,UAAA9L,KAAAe,QACA,UAAAmK,EAAAlL,KAAAyK,QAAA3P,KASA4Q,EAAAxL,UAAAjE,QAAA,WAEA,GAAA+D,KAAA4M,SACA,OAAA5M,KA0BA,IAxBAA,KAAAwH,YAAAmC,EAAAkD,SAAA7M,KAAA2H,SAAA7M,IACAkF,KAAAqH,cAAArH,KAAAqM,eAAArM,KAAAqM,eAAAS,OAAA9M,KAAA8M,QAAAC,iBAAA/M,KAAA2H,MACA3H,KAAAqH,wBAAAsE,EACA3L,KAAAwH,YAAA,KAEAxH,KAAAwH,YAAAxH,KAAAqH,aAAAC,OAAAvI,OAAAC,KAAAgB,KAAAqH,aAAAC,QAAA,KAIAtH,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAwH,YAAAxH,KAAAe,QAAA,QACAf,KAAAqH,wBAAAP,GAAA,iBAAA9G,KAAAwH,cACAxH,KAAAwH,YAAAxH,KAAAqH,aAAAC,OAAAtH,KAAAwH,eAIAxH,KAAAe,WACA,IAAAf,KAAAe,QAAA8I,SAAA7J,KAAAe,QAAA8I,SAAA/O,IAAAkF,KAAAqH,cAAArH,KAAAqH,wBAAAP,WACA9G,KAAAe,QAAA8I,OACA9K,OAAAC,KAAAgB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAAjG,IAIAkF,KAAA4I,KACA5I,KAAAwH,YAAAT,EAAAoF,KAAAa,WAAAhN,KAAAwH,YAAA,MAAAxH,KAAA2H,KAAAlL,OAAA,IAGAsC,OAAAkO,QACAlO,OAAAkO,OAAAjN,KAAAwH,kBAEA,GAAAxH,KAAAiJ,OAAA,iBAAAjJ,KAAAwH,YAAA,CACA,IAAAjF,EACAwE,EAAA1K,OAAA6B,KAAA8B,KAAAwH,aACAT,EAAA1K,OAAAyB,OAAAkC,KAAAwH,YAAAjF,EAAAwE,EAAAmG,UAAAnG,EAAA1K,OAAAT,OAAAoE,KAAAwH,cAAA,GAEAT,EAAAR,KAAAG,MAAA1G,KAAAwH,YAAAjF,EAAAwE,EAAAmG,UAAAnG,EAAAR,KAAA3K,OAAAoE,KAAAwH,cAAA,GACAxH,KAAAwH,YAAAjF,EAeA,OAXAvC,KAAAmI,IACAnI,KAAAkM,aAAAnF,EAAAoG,YACAnN,KAAAuH,SACAvH,KAAAkM,aAAAnF,EAAAqG,WAEApN,KAAAkM,aAAAlM,KAAAwH,YAGAxH,KAAA8M,kBAAAnB,IACA3L,KAAA8M,OAAAO,KAAAnN,UAAAF,KAAAiI,MAAAjI,KAAAkM,cAEA9B,EAAAlK,UAAAjE,QAAAqK,KAAAtG,OAuBA0L,EAAA4B,EAAA,SAAAC,EAAAC,EAAAC,EAAAvB,GAUA,MAPA,mBAAAsB,EACAA,EAAAzG,EAAA2G,aAAAF,GAAAvF,KAGAuF,GAAA,iBAAAA,IACAA,EAAAzG,EAAA4G,aAAAH,GAAAvF,MAEA,SAAA/H,EAAA0N,GACA7G,EAAA2G,aAAAxN,EAAAoK,aACAa,IAAA,IAAAO,EAAAkC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAA3B,OAkBAR,EAAAoC,EAAA,SAAAC,GACApC,EAAAoC,iDChXA,IAAA7S,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAA8S,MAAA,QAoDA9S,EAAA+S,KAjCA,SAAAnN,EAAAoN,EAAAlN,GAMA,MALA,mBAAAkN,GACAlN,EAAAkN,EACAA,EAAA,IAAAhT,EAAAiT,MACAD,IACAA,EAAA,IAAAhT,EAAAiT,MACAD,EAAAD,KAAAnN,EAAAE,IA2CA9F,EAAAkT,SANA,SAAAtN,EAAAoN,GAGA,OAFAA,IACAA,EAAA,IAAAhT,EAAAiT,MACAD,EAAAE,SAAAtN,IAMA5F,EAAAmT,QAAAjT,EAAA,IACAF,EAAAoT,QAAAlT,EAAA,IACAF,EAAAqT,SAAAnT,EAAA,IACAF,EAAA2L,UAAAzL,EAAA,IAGAF,EAAAkP,iBAAAhP,EAAA,IACAF,EAAAsP,UAAApP,EAAA,IACAF,EAAAiT,KAAA/S,EAAA,IACAF,EAAA4L,KAAA1L,EAAA,IACAF,EAAAyQ,KAAAvQ,EAAA,IACAF,EAAAwQ,MAAAtQ,EAAA,IACAF,EAAAsT,MAAApT,EAAA,IACAF,EAAAuT,SAAArT,EAAA,IACAF,EAAAwT,QAAAtT,EAAA,IACAF,EAAAyT,OAAAvT,EAAA,IAGAF,EAAA0T,QAAAxT,EAAA,IACAF,EAAA2T,SAAAzT,EAAA,IAGAF,EAAAyO,MAAAvO,EAAA,IACAF,EAAA6L,KAAA3L,EAAA,IAGAF,EAAAkP,iBAAA0D,EAAA5S,EAAAiT,MACAjT,EAAAsP,UAAAsD,EAAA5S,EAAAyQ,KAAAzQ,EAAAwT,QAAAxT,EAAA4L,MACA5L,EAAAiT,KAAAL,EAAA5S,EAAAyQ,MACAzQ,EAAAwQ,MAAAoC,EAAA5S,EAAAyQ,gJCtGA,IAAAzQ,EAAAI,EA2BA,SAAAwT,IACA5T,EAAA6T,OAAAjB,EAAA5S,EAAA8T,cACA9T,EAAA6L,KAAA+G,IArBA5S,EAAA8S,MAAA,UAGA9S,EAAA+T,OAAA7T,EAAA,IACAF,EAAAgU,aAAA9T,EAAA,IACAF,EAAA6T,OAAA3T,EAAA,IACAF,EAAA8T,aAAA5T,EAAA,IAGAF,EAAA6L,KAAA3L,EAAA,IACAF,EAAAiU,IAAA/T,EAAA,IACAF,EAAAkU,MAAAhU,EAAA,IACAF,EAAA4T,UAAAA,EAaA5T,EAAA+T,OAAAnB,EAAA5S,EAAAgU,cACAJ,oEClCAzT,EAAAC,QAAAmT,EAGA,IAAA/C,EAAAtQ,EAAA,MACAqT,EAAAvO,UAAAnB,OAAAsL,OAAAqB,EAAAxL,YAAAoK,YAAAmE,GAAAlE,UAAA,WAEA,IAAAZ,EAAAvO,EAAA,IACA2L,EAAA3L,EAAA,IAcA,SAAAqT,EAAAxG,EAAAwB,EAAAC,EAAA/B,EAAA5G,EAAA0J,GAIA,GAHAiB,EAAApF,KAAAtG,KAAAiI,EAAAwB,EAAA9B,EAAA7M,EAAAA,EAAAiG,EAAA0J,IAGA1D,EAAAqE,SAAA1B,GACA,MAAAiB,UAAA,4BAMA3K,KAAA0J,QAAAA,EAMA1J,KAAAqP,gBAAA,KAGArP,KAAAmI,KAAA,EAwBAsG,EAAA5D,SAAA,SAAA5C,EAAA6C,GACA,OAAA,IAAA2D,EAAAxG,EAAA6C,EAAArB,GAAAqB,EAAApB,QAAAoB,EAAAnD,KAAAmD,EAAA/J,QAAA+J,EAAAL,UAQAgE,EAAAvO,UAAA8K,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,UAAApI,KAAA0J,QACA,OAAA1J,KAAA2H,KACA,KAAA3H,KAAAyJ,GACA,SAAAzJ,KAAA8L,OACA,UAAA9L,KAAAe,QACA,UAAAmK,EAAAlL,KAAAyK,QAAA3P,KAOA2T,EAAAvO,UAAAjE,QAAA,WACA,GAAA+D,KAAA4M,SACA,OAAA5M,KAGA,GAAA2J,EAAAM,OAAAjK,KAAA0J,WAAA5O,EACA,MAAAmD,MAAA,qBAAA+B,KAAA0J,SAEA,OAAAgC,EAAAxL,UAAAjE,QAAAqK,KAAAtG,OAaAyO,EAAAnB,EAAA,SAAAC,EAAA+B,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAAxI,EAAA2G,aAAA6B,GAAAtH,KAGAsH,GAAA,iBAAAA,IACAA,EAAAxI,EAAA4G,aAAA4B,GAAAtH,MAEA,SAAA/H,EAAA0N,GACA7G,EAAA2G,aAAAxN,EAAAoK,aACAa,IAAA,IAAAsD,EAAAb,EAAAL,EAAA+B,EAAAC,8CC1HAlU,EAAAC,QAAAsT,EAEA,IAAA7H,EAAA3L,EAAA,IASA,SAAAwT,EAAAY,GAEA,GAAAA,EACA,IAAA,IAAAxQ,EAAAD,OAAAC,KAAAwQ,GAAA1S,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAhB,EAAAlC,IAAA0S,EAAAxQ,EAAAlC,IA0BA8R,EAAAvE,OAAA,SAAAmF,GACA,OAAAxP,KAAAyP,MAAApF,OAAAmF,IAWAZ,EAAA7R,OAAA,SAAAkP,EAAAyD,GACA,OAAA1P,KAAAyP,MAAA1S,OAAAkP,EAAAyD,IAWAd,EAAAe,gBAAA,SAAA1D,EAAAyD,GACA,OAAA1P,KAAAyP,MAAAE,gBAAA1D,EAAAyD,IAYAd,EAAA9Q,OAAA,SAAA8R,GACA,OAAA5P,KAAAyP,MAAA3R,OAAA8R,IAYAhB,EAAAiB,gBAAA,SAAAD,GACA,OAAA5P,KAAAyP,MAAAI,gBAAAD,IAUAhB,EAAAkB,OAAA,SAAA7D,GACA,OAAAjM,KAAAyP,MAAAK,OAAA7D,IAUA2C,EAAA/G,WAAA,SAAAkI,GACA,OAAA/P,KAAAyP,MAAA5H,WAAAkI,IAWAnB,EAAAxG,SAAA,SAAA6D,EAAAlL,GACA,OAAAf,KAAAyP,MAAArH,SAAA6D,EAAAlL,IAOA6N,EAAA1O,UAAA8K,OAAA,WACA,OAAAhL,KAAAyP,MAAArH,SAAApI,KAAA+G,EAAAkE,4CCtIA5P,EAAAC,QAAAqT,EAGA,IAAAvE,EAAAhP,EAAA,MACAuT,EAAAzO,UAAAnB,OAAAsL,OAAAD,EAAAlK,YAAAoK,YAAAqE,GAAApE,UAAA,SAEA,IAAAxD,EAAA3L,EAAA,IAgBA,SAAAuT,EAAA1G,EAAAN,EAAAqI,EAAAnO,EAAAoO,EAAAC,EAAAnP,EAAA0J,GAYA,GATA1D,EAAAgF,SAAAkE,IACAlP,EAAAkP,EACAA,EAAAC,EAAApV,GACAiM,EAAAgF,SAAAmE,KACAnP,EAAAmP,EACAA,EAAApV,GAIA6M,IAAA7M,IAAAiM,EAAAqE,SAAAzD,GACA,MAAAgD,UAAA,yBAGA,IAAA5D,EAAAqE,SAAA4E,GACA,MAAArF,UAAA,gCAGA,IAAA5D,EAAAqE,SAAAvJ,GACA,MAAA8I,UAAA,iCAEAP,EAAA9D,KAAAtG,KAAAiI,EAAAlH,GAMAf,KAAA2H,KAAAA,GAAA,MAMA3H,KAAAgQ,YAAAA,EAMAhQ,KAAAiQ,gBAAAA,GAAAnV,EAMAkF,KAAA6B,aAAAA,EAMA7B,KAAAkQ,iBAAAA,GAAApV,EAMAkF,KAAAmQ,oBAAA,KAMAnQ,KAAAoQ,qBAAA,KAMApQ,KAAAyK,QAAAA,EAqBAkE,EAAA9D,SAAA,SAAA5C,EAAA6C,GACA,OAAA,IAAA6D,EAAA1G,EAAA6C,EAAAnD,KAAAmD,EAAAkF,YAAAlF,EAAAjJ,aAAAiJ,EAAAmF,cAAAnF,EAAAoF,eAAApF,EAAA/J,QAAA+J,EAAAL,UAQAkE,EAAAzO,UAAA8K,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,OAAA,QAAApI,KAAA2H,MAAA3H,KAAA2H,MAAA7M,EACA,cAAAkF,KAAAgQ,YACA,gBAAAhQ,KAAAiQ,cACA,eAAAjQ,KAAA6B,aACA,iBAAA7B,KAAAkQ,eACA,UAAAlQ,KAAAe,QACA,UAAAmK,EAAAlL,KAAAyK,QAAA3P,KAOA6T,EAAAzO,UAAAjE,QAAA,WAGA,OAAA+D,KAAA4M,SACA5M,MAEAA,KAAAmQ,oBAAAnQ,KAAA8M,OAAAuD,WAAArQ,KAAAgQ,aACAhQ,KAAAoQ,qBAAApQ,KAAA8M,OAAAuD,WAAArQ,KAAA6B,cAEAuI,EAAAlK,UAAAjE,QAAAqK,KAAAtG,0CCpJA3E,EAAAC,QAAAkP,EAGA,IAAAJ,EAAAhP,EAAA,MACAoP,EAAAtK,UAAAnB,OAAAsL,OAAAD,EAAAlK,YAAAoK,YAAAE,GAAAD,UAAA,YAEA,IAGAoB,EACA+C,EACA5H,EALA4E,EAAAtQ,EAAA,IACA2L,EAAA3L,EAAA,IAoCA,SAAAkV,EAAAC,EAAAtF,GACA,IAAAsF,IAAAA,EAAA3U,OACA,OAAAd,EAEA,IADA,IAAA0V,EAAA,GACA1T,EAAA,EAAAA,EAAAyT,EAAA3U,SAAAkB,EACA0T,EAAAD,EAAAzT,GAAAmL,MAAAsI,EAAAzT,GAAAkO,OAAAC,GACA,OAAAuF,EA4CA,SAAAhG,EAAAvC,EAAAlH,GACAqJ,EAAA9D,KAAAtG,KAAAiI,EAAAlH,GAMAf,KAAAyQ,OAAA3V,EAOAkF,KAAA0Q,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFApG,EAAAK,SAAA,SAAA5C,EAAA6C,GACA,OAAA,IAAAN,EAAAvC,EAAA6C,EAAA/J,SAAA8P,QAAA/F,EAAA2F,SAmBAjG,EAAA8F,YAAAA,EAQA9F,EAAAc,aAAA,SAAAV,EAAAnB,GACA,GAAAmB,EACA,IAAA,IAAA9N,EAAA,EAAAA,EAAA8N,EAAAhP,SAAAkB,EACA,GAAA,iBAAA8N,EAAA9N,IAAA8N,EAAA9N,GAAA,IAAA2M,GAAAmB,EAAA9N,GAAA,IAAA2M,EACA,OAAA,EACA,OAAA,GASAe,EAAAe,eAAA,SAAAX,EAAA3C,GACA,GAAA2C,EACA,IAAA,IAAA9N,EAAA,EAAAA,EAAA8N,EAAAhP,SAAAkB,EACA,GAAA8N,EAAA9N,KAAAmL,EACA,OAAA,EACA,OAAA,GA0CAlJ,OAAAwN,eAAA/B,EAAAtK,UAAA,cAAA,CACAsM,IAAA,WACA,OAAAxM,KAAA0Q,IAAA1Q,KAAA0Q,EAAA3J,EAAA+J,QAAA9Q,KAAAyQ,YA6BAjG,EAAAtK,UAAA8K,OAAA,SAAAC,GACA,OAAAlE,EAAAqB,SAAA,CACA,UAAApI,KAAAe,QACA,SAAAuP,EAAAtQ,KAAA+Q,YAAA9F,MASAT,EAAAtK,UAAA2Q,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAP,EAAAQ,EAAAlS,OAAAC,KAAAgS,GAAAlU,EAAA,EAAAA,EAAAmU,EAAArV,SAAAkB,EACA2T,EAAAO,EAAAC,EAAAnU,IAJAkD,KAKAmL,KACAsF,EAAA1I,SAAAjN,EACA6Q,EAAAd,SACA4F,EAAAnJ,SAAAxM,EACAgM,EAAA+D,SACA4F,EAAAS,UAAApW,EACA4T,EAAA7D,SACA4F,EAAAhH,KAAA3O,EACA4Q,EAAAb,SACAL,EAAAK,UAAAoG,EAAAnU,GAAA2T,IAIA,OAAAzQ,MAQAwK,EAAAtK,UAAAsM,IAAA,SAAAvE,GACA,OAAAjI,KAAAyQ,QAAAzQ,KAAAyQ,OAAAxI,IACA,MAUAuC,EAAAtK,UAAAiR,QAAA,SAAAlJ,GACA,GAAAjI,KAAAyQ,QAAAzQ,KAAAyQ,OAAAxI,aAAAnB,EACA,OAAA9G,KAAAyQ,OAAAxI,GAAAX,OACA,MAAArJ,MAAA,iBAAAgK,IAUAuC,EAAAtK,UAAAiL,IAAA,SAAA4E,GAEA,KAAAA,aAAArE,GAAAqE,EAAAjE,SAAAhR,GAAAiV,aAAApE,GAAAoE,aAAAjJ,GAAAiJ,aAAArB,GAAAqB,aAAAvF,GACA,MAAAG,UAAA,wCAEA,GAAA3K,KAAAyQ,OAEA,CACA,IAAAW,EAAApR,KAAAwM,IAAAuD,EAAA9H,MACA,GAAAmJ,EAAA,CACA,KAAAA,aAAA5G,GAAAuF,aAAAvF,IAAA4G,aAAAzF,GAAAyF,aAAA1C,EAWA,MAAAzQ,MAAA,mBAAA8R,EAAA9H,KAAA,QAAAjI,MARA,IADA,IAAAyQ,EAAAW,EAAAL,YACAjU,EAAA,EAAAA,EAAA2T,EAAA7U,SAAAkB,EACAiT,EAAA5E,IAAAsF,EAAA3T,IACAkD,KAAAyL,OAAA2F,GACApR,KAAAyQ,SACAzQ,KAAAyQ,OAAA,IACAV,EAAAsB,WAAAD,EAAArQ,SAAA,SAZAf,KAAAyQ,OAAA,GAoBA,OAFAzQ,KAAAyQ,OAAAV,EAAA9H,MAAA8H,GACAuB,MAAAtR,MACA2Q,EAAA3Q,OAUAwK,EAAAtK,UAAAuL,OAAA,SAAAsE,GAEA,KAAAA,aAAA3F,GACA,MAAAO,UAAA,qCACA,GAAAoF,EAAAjD,SAAA9M,KACA,MAAA/B,MAAA8R,EAAA,uBAAA/P,MAOA,cALAA,KAAAyQ,OAAAV,EAAA9H,MACAlJ,OAAAC,KAAAgB,KAAAyQ,QAAA7U,SACAoE,KAAAyQ,OAAA3V,GAEAiV,EAAAwB,SAAAvR,MACA2Q,EAAA3Q,OASAwK,EAAAtK,UAAAsR,OAAA,SAAAjM,EAAAuF,GAEA,GAAA/D,EAAAqE,SAAA7F,GACAA,EAAAA,EAAAG,MAAA,UACA,IAAAhK,MAAA+V,QAAAlM,GACA,MAAAoF,UAAA,gBACA,GAAApF,GAAAA,EAAA3J,QAAA,KAAA2J,EAAA,GACA,MAAAtH,MAAA,yBAGA,IADA,IAAAyT,EAAA1R,KACA,EAAAuF,EAAA3J,QAAA,CACA,IAAA+V,EAAApM,EAAAM,QACA,GAAA6L,EAAAjB,QAAAiB,EAAAjB,OAAAkB,IAEA,MADAD,EAAAA,EAAAjB,OAAAkB,cACAnH,GACA,MAAAvM,MAAA,kDAEAyT,EAAAvG,IAAAuG,EAAA,IAAAlH,EAAAmH,IAIA,OAFA7G,GACA4G,EAAAb,QAAA/F,GACA4G,GAOAlH,EAAAtK,UAAA0R,WAAA,WAEA,IADA,IAAAnB,EAAAzQ,KAAA+Q,YAAAjU,EAAA,EACAA,EAAA2T,EAAA7U,QACA6U,EAAA3T,aAAA0N,EACAiG,EAAA3T,KAAA8U,aAEAnB,EAAA3T,KAAAb,UACA,OAAA+D,KAAA/D,WAUAuO,EAAAtK,UAAA2R,OAAA,SAAAtM,EAAAuM,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAAhX,GACAgX,IAAApW,MAAA+V,QAAAK,KACAA,EAAA,CAAAA,IAEA/K,EAAAqE,SAAA7F,IAAAA,EAAA3J,OAAA,CACA,GAAA,MAAA2J,EACA,OAAAvF,KAAAkO,KACA3I,EAAAA,EAAAG,MAAA,UACA,IAAAH,EAAA3J,OACA,OAAAoE,KAGA,GAAA,KAAAuF,EAAA,GACA,OAAAvF,KAAAkO,KAAA2D,OAAAtM,EAAA5H,MAAA,GAAAmU,GAGA,IAAAE,EAAAhS,KAAAwM,IAAAjH,EAAA,IACA,GAAAyM,GACA,GAAA,IAAAzM,EAAA3J,QACA,IAAAkW,IAAA,EAAAA,EAAAzI,QAAA2I,EAAA1H,aACA,OAAA0H,OACA,GAAAA,aAAAxH,IAAAwH,EAAAA,EAAAH,OAAAtM,EAAA5H,MAAA,GAAAmU,GAAA,IACA,OAAAE,OAIA,IAAA,IAAAlV,EAAA,EAAAA,EAAAkD,KAAA+Q,YAAAnV,SAAAkB,EACA,GAAAkD,KAAA0Q,EAAA5T,aAAA0N,IAAAwH,EAAAhS,KAAA0Q,EAAA5T,GAAA+U,OAAAtM,EAAAuM,GAAA,IACA,OAAAE,EAGA,OAAA,OAAAhS,KAAA8M,QAAAiF,EACA,KACA/R,KAAA8M,OAAA+E,OAAAtM,EAAAuM,IAqBAtH,EAAAtK,UAAAmQ,WAAA,SAAA9K,GACA,IAAAyM,EAAAhS,KAAA6R,OAAAtM,EAAA,CAAAoG,IACA,IAAAqG,EACA,MAAA/T,MAAA,iBAAAsH,GACA,OAAAyM,GAUAxH,EAAAtK,UAAA+R,WAAA,SAAA1M,GACA,IAAAyM,EAAAhS,KAAA6R,OAAAtM,EAAA,CAAAuB,IACA,IAAAkL,EACA,MAAA/T,MAAA,iBAAAsH,EAAA,QAAAvF,MACA,OAAAgS,GAUAxH,EAAAtK,UAAA6M,iBAAA,SAAAxH,GACA,IAAAyM,EAAAhS,KAAA6R,OAAAtM,EAAA,CAAAoG,EAAA7E,IACA,IAAAkL,EACA,MAAA/T,MAAA,yBAAAsH,EAAA,QAAAvF,MACA,OAAAgS,GAUAxH,EAAAtK,UAAAgS,cAAA,SAAA3M,GACA,IAAAyM,EAAAhS,KAAA6R,OAAAtM,EAAA,CAAAmJ,IACA,IAAAsD,EACA,MAAA/T,MAAA,oBAAAsH,EAAA,QAAAvF,MACA,OAAAgS,GAIAxH,EAAAsD,EAAA,SAAAC,EAAAoE,EAAAC,GACAzG,EAAAoC,EACAW,EAAAyD,EACArL,EAAAsL,4CC9aA/W,EAAAC,QAAA8O,GAEAG,UAAA,mBAEA,IAEA4D,EAFApH,EAAA3L,EAAA,IAYA,SAAAgP,EAAAnC,EAAAlH,GAEA,IAAAgG,EAAAqE,SAAAnD,GACA,MAAA0C,UAAA,yBAEA,GAAA5J,IAAAgG,EAAAgF,SAAAhL,GACA,MAAA4J,UAAA,6BAMA3K,KAAAe,QAAAA,EAMAf,KAAAiI,KAAAA,EAMAjI,KAAA8M,OAAA,KAMA9M,KAAA4M,UAAA,EAMA5M,KAAAyK,QAAA,KAMAzK,KAAAc,SAAA,KAGA/B,OAAAsT,iBAAAjI,EAAAlK,UAAA,CAQAgO,KAAA,CACA1B,IAAA,WAEA,IADA,IAAAkF,EAAA1R,KACA,OAAA0R,EAAA5E,QACA4E,EAAAA,EAAA5E,OACA,OAAA4E,IAUAjK,SAAA,CACA+E,IAAA,WAGA,IAFA,IAAAjH,EAAA,CAAAvF,KAAAiI,MACAyJ,EAAA1R,KAAA8M,OACA4E,GACAnM,EAAA+M,QAAAZ,EAAAzJ,MACAyJ,EAAAA,EAAA5E,OAEA,OAAAvH,EAAA3H,KAAA,SAUAwM,EAAAlK,UAAA8K,OAAA,WACA,MAAA/M,SAQAmM,EAAAlK,UAAAoR,MAAA,SAAAxE,GACA9M,KAAA8M,QAAA9M,KAAA8M,SAAAA,GACA9M,KAAA8M,OAAArB,OAAAzL,MACAA,KAAA8M,OAAAA,EACA9M,KAAA4M,UAAA,EACA,IAAAsB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAqE,EAAAvS,OAQAoK,EAAAlK,UAAAqR,SAAA,SAAAzE,GACA,IAAAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAsE,EAAAxS,MACAA,KAAA8M,OAAA,KACA9M,KAAA4M,UAAA,GAOAxC,EAAAlK,UAAAjE,QAAA,WACA,OAAA+D,KAAA4M,UAEA5M,KAAAkO,gBAAAC,IACAnO,KAAA4M,UAAA,GAFA5M,MAWAoK,EAAAlK,UAAAuM,UAAA,SAAAxE,GACA,OAAAjI,KAAAe,QACAf,KAAAe,QAAAkH,GACAnN,GAUAsP,EAAAlK,UAAAwM,UAAA,SAAAzE,EAAAvI,EAAAiN,GAGA,OAFAA,GAAA3M,KAAAe,SAAAf,KAAAe,QAAAkH,KAAAnN,KACAkF,KAAAe,UAAAf,KAAAe,QAAA,KAAAkH,GAAAvI,GACAM,MASAoK,EAAAlK,UAAAmR,WAAA,SAAAtQ,EAAA4L,GACA,GAAA5L,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,GAAAjE,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAA0M,UAAA1N,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAA6P,GACA,OAAA3M,MAOAoK,EAAAlK,UAAAxB,SAAA,WACA,IAAA6L,EAAAvK,KAAAsK,YAAAC,UACA9C,EAAAzH,KAAAyH,SACA,OAAAA,EAAA7L,OACA2O,EAAA,IAAA9C,EACA8C,GAIAH,EAAA0D,EAAA,SAAA2E,GACAtE,EAAAsE,+BCrMApX,EAAAC,QAAAkT,EAGA,IAAApE,EAAAhP,EAAA,MACAoT,EAAAtO,UAAAnB,OAAAsL,OAAAD,EAAAlK,YAAAoK,YAAAkE,GAAAjE,UAAA,QAEA,IAAAmB,EAAAtQ,EAAA,IACA2L,EAAA3L,EAAA,IAYA,SAAAoT,EAAAvG,EAAAyK,EAAA3R,EAAA0J,GAQA,GAPA/O,MAAA+V,QAAAiB,KACA3R,EAAA2R,EACAA,EAAA5X,GAEAsP,EAAA9D,KAAAtG,KAAAiI,EAAAlH,GAGA2R,IAAA5X,IAAAY,MAAA+V,QAAAiB,GACA,MAAA/H,UAAA,+BAMA3K,KAAA2S,MAAAD,GAAA,GAOA1S,KAAAgI,YAAA,GAMAhI,KAAAyK,QAAAA,EA0CA,SAAAmI,EAAAD,GACA,GAAAA,EAAA7F,OACA,IAAA,IAAAhQ,EAAA,EAAAA,EAAA6V,EAAA3K,YAAApM,SAAAkB,EACA6V,EAAA3K,YAAAlL,GAAAgQ,QACA6F,EAAA7F,OAAA3B,IAAAwH,EAAA3K,YAAAlL,IA7BA0R,EAAA3D,SAAA,SAAA5C,EAAA6C,GACA,OAAA,IAAA0D,EAAAvG,EAAA6C,EAAA6H,MAAA7H,EAAA/J,QAAA+J,EAAAL,UAQA+D,EAAAtO,UAAA8K,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,UAAApI,KAAAe,QACA,QAAAf,KAAA2S,MACA,UAAAzH,EAAAlL,KAAAyK,QAAA3P,KAuBA0T,EAAAtO,UAAAiL,IAAA,SAAAjE,GAGA,KAAAA,aAAAwE,GACA,MAAAf,UAAA,yBAQA,OANAzD,EAAA4F,QAAA5F,EAAA4F,SAAA9M,KAAA8M,QACA5F,EAAA4F,OAAArB,OAAAvE,GACAlH,KAAA2S,MAAAnV,KAAA0J,EAAAe,MACAjI,KAAAgI,YAAAxK,KAAA0J,GAEA0L,EADA1L,EAAAwB,OAAA1I,MAEAA,MAQAwO,EAAAtO,UAAAuL,OAAA,SAAAvE,GAGA,KAAAA,aAAAwE,GACA,MAAAf,UAAA,yBAEA,IAAA7O,EAAAkE,KAAAgI,YAAAqB,QAAAnC,GAGA,GAAApL,EAAA,EACA,MAAAmC,MAAAiJ,EAAA,uBAAAlH,MAUA,OARAA,KAAAgI,YAAAzH,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAA2S,MAAAtJ,QAAAnC,EAAAe,QAIAjI,KAAA2S,MAAApS,OAAAzE,EAAA,GAEAoL,EAAAwB,OAAA,KACA1I,MAMAwO,EAAAtO,UAAAoR,MAAA,SAAAxE,GACA1C,EAAAlK,UAAAoR,MAAAhL,KAAAtG,KAAA8M,GAGA,IAFA,IAEAhQ,EAAA,EAAAA,EAAAkD,KAAA2S,MAAA/W,SAAAkB,EAAA,CACA,IAAAoK,EAAA4F,EAAAN,IAAAxM,KAAA2S,MAAA7V,IACAoK,IAAAA,EAAAwB,SACAxB,EAAAwB,OALA1I,MAMAgI,YAAAxK,KAAA0J,GAIA0L,EAAA5S,OAMAwO,EAAAtO,UAAAqR,SAAA,SAAAzE,GACA,IAAA,IAAA5F,EAAApK,EAAA,EAAAA,EAAAkD,KAAAgI,YAAApM,SAAAkB,GACAoK,EAAAlH,KAAAgI,YAAAlL,IAAAgQ,QACA5F,EAAA4F,OAAArB,OAAAvE,GACAkD,EAAAlK,UAAAqR,SAAAjL,KAAAtG,KAAA8M,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAAoF,EAAAhX,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACA8W,EAAA5W,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAA2S,GACA9L,EAAA2G,aAAAxN,EAAAoK,aACAa,IAAA,IAAAqD,EAAAqE,EAAAH,IACA3T,OAAAwN,eAAArM,EAAA2S,EAAA,CACArG,IAAAzF,EAAA+L,YAAAJ,GACAK,IAAAhM,EAAAiM,YAAAN,+CCtMArX,EAAAC,QAAAyT,EAEA,IAEAC,EAFAjI,EAAA3L,EAAA,IAIA6X,EAAAlM,EAAAkM,SACA1M,EAAAQ,EAAAR,KAGA,SAAA2M,EAAAtD,EAAAuD,GACA,OAAAC,WAAA,uBAAAxD,EAAApN,IAAA,OAAA2Q,GAAA,GAAA,MAAAvD,EAAApJ,KASA,SAAAuI,EAAA/R,GAMAgD,KAAAuC,IAAAvF,EAMAgD,KAAAwC,IAAA,EAMAxC,KAAAwG,IAAAxJ,EAAApB,OAGA,IAwCA8D,EAxCA2T,EAAA,oBAAA1R,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAjG,MAAA+V,QAAAzU,GACA,OAAA,IAAA+R,EAAA/R,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAAtB,MAAA+V,QAAAzU,GACA,OAAA,IAAA+R,EAAA/R,GACA,MAAAiB,MAAA,mBAkEA,SAAAqV,IAEA,IAAAC,EAAA,IAAAN,EAAA,EAAA,GACAnW,EAAA,EACA,KAAA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KAaA,CACA,KAAA1F,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,MAGA,GADAuT,EAAAtO,IAAAsO,EAAAtO,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA+Q,EAIA,OADAA,EAAAtO,IAAAsO,EAAAtO,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,SAAA,EAAA1F,KAAA,EACAyW,EAxBA,KAAAzW,EAAA,IAAAA,EAGA,GADAyW,EAAAtO,IAAAsO,EAAAtO,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA+Q,EAKA,GAFAA,EAAAtO,IAAAsO,EAAAtO,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EACA+Q,EAAArO,IAAAqO,EAAArO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EACAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA+Q,EAgBA,GAfAzW,EAAA,EAeA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KACA,KAAA1F,EAAA,IAAAA,EAGA,GADAyW,EAAArO,IAAAqO,EAAArO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA+Q,OAGA,KAAAzW,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,MAGA,GADAuT,EAAArO,IAAAqO,EAAArO,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAA+Q,EAIA,MAAAtV,MAAA,2BAkCA,SAAAuV,EAAAjR,EAAArF,GACA,OAAAqF,EAAArF,EAAA,GACAqF,EAAArF,EAAA,IAAA,EACAqF,EAAArF,EAAA,IAAA,GACAqF,EAAArF,EAAA,IAAA,MAAA,EA+BA,SAAAuW,IAGA,GAAAzT,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,KAAA,GAEA,OAAA,IAAAiT,EAAAO,EAAAxT,KAAAuC,IAAAvC,KAAAwC,KAAA,GAAAgR,EAAAxT,KAAAuC,IAAAvC,KAAAwC,KAAA,IArLAuM,EAAA1E,OAAAtD,EAAA2M,OACA,SAAA1W,GACA,OAAA+R,EAAA1E,OAAA,SAAArN,GACA,OAAA+J,EAAA2M,OAAAC,SAAA3W,GACA,IAAAgS,EAAAhS,GAEAqW,EAAArW,KACAA,IAGAqW,EAEAtE,EAAA7O,UAAA0T,EAAA7M,EAAArL,MAAAwE,UAAA2T,UAAA9M,EAAArL,MAAAwE,UAAAvC,MAOAoR,EAAA7O,UAAA4T,QACApU,EAAA,WACA,WACA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,QAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,GAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EAGA,IAAAM,KAAAwC,KAAA,GAAAxC,KAAAwG,IAEA,MADAxG,KAAAwC,IAAAxC,KAAAwG,IACA0M,EAAAlT,KAAA,IAEA,OAAAN,IAQAqP,EAAA7O,UAAA6T,MAAA,WACA,OAAA,EAAA/T,KAAA8T,UAOA/E,EAAA7O,UAAA8T,OAAA,WACA,IAAAtU,EAAAM,KAAA8T,SACA,OAAApU,IAAA,IAAA,EAAAA,GAAA,GAqFAqP,EAAA7O,UAAA+T,KAAA,WACA,OAAA,IAAAjU,KAAA8T,UAcA/E,EAAA7O,UAAAgU,QAAA,WAGA,GAAAlU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,KAAA,GAEA,OAAAwT,EAAAxT,KAAAuC,IAAAvC,KAAAwC,KAAA,IAOAuM,EAAA7O,UAAAiU,SAAA,WAGA,GAAAnU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,KAAA,GAEA,OAAA,EAAAwT,EAAAxT,KAAAuC,IAAAvC,KAAAwC,KAAA,IAmCAuM,EAAA7O,UAAAkU,MAAA,WAGA,GAAApU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,KAAA,GAEA,IAAAN,EAAAqH,EAAAqN,MAAAtR,YAAA9C,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAQAqP,EAAA7O,UAAAmU,OAAA,WAGA,GAAArU,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,KAAA,GAEA,IAAAN,EAAAqH,EAAAqN,MAAAzP,aAAA3E,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAOAqP,EAAA7O,UAAA+I,MAAA,WACA,IAAArN,EAAAoE,KAAA8T,SACA7W,EAAA+C,KAAAwC,IACAtF,EAAA8C,KAAAwC,IAAA5G,EAGA,GAAAsB,EAAA8C,KAAAwG,IACA,MAAA0M,EAAAlT,KAAApE,GAGA,OADAoE,KAAAwC,KAAA5G,EACAF,MAAA+V,QAAAzR,KAAAuC,KACAvC,KAAAuC,IAAA5E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA8C,KAAAuC,IAAA+H,YAAA,GACAtK,KAAA4T,EAAAtN,KAAAtG,KAAAuC,IAAAtF,EAAAC,IAOA6R,EAAA7O,UAAA5D,OAAA,WACA,IAAA2M,EAAAjJ,KAAAiJ,QACA,OAAA1C,EAAAE,KAAAwC,EAAA,EAAAA,EAAArN,SAQAmT,EAAA7O,UAAAoU,KAAA,SAAA1Y,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAwC,IAAA5G,EAAAoE,KAAAwG,IACA,MAAA0M,EAAAlT,KAAApE,GACAoE,KAAAwC,KAAA5G,OAEA,GAEA,GAAAoE,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA0M,EAAAlT,YACA,IAAAA,KAAAuC,IAAAvC,KAAAwC,QAEA,OAAAxC,MAQA+O,EAAA7O,UAAAqU,SAAA,SAAAvK,GACA,OAAAA,GACA,KAAA,EACAhK,KAAAsU,OACA,MACA,KAAA,EACAtU,KAAAsU,KAAA,GACA,MACA,KAAA,EACAtU,KAAAsU,KAAAtU,KAAA8T,UACA,MACA,KAAA,EACA,KAAA,IAAA9J,EAAA,EAAAhK,KAAA8T,WACA9T,KAAAuU,SAAAvK,GAEA,MACA,KAAA,EACAhK,KAAAsU,KAAA,GACA,MAGA,QACA,MAAArW,MAAA,qBAAA+L,EAAA,cAAAhK,KAAAwC,KAEA,OAAAxC,MAGA+O,EAAAjB,EAAA,SAAA0G,GACAxF,EAAAwF,EAEA,IAAAjZ,EAAAwL,EAAAoF,KAAA,SAAA,WACApF,EAAA0N,MAAA1F,EAAA7O,UAAA,CAEAwU,MAAA,WACA,OAAApB,EAAAhN,KAAAtG,MAAAzE,IAAA,IAGAoZ,OAAA,WACA,OAAArB,EAAAhN,KAAAtG,MAAAzE,IAAA,IAGAqZ,OAAA,WACA,OAAAtB,EAAAhN,KAAAtG,MAAA6U,WAAAtZ,IAAA,IAGAuZ,QAAA,WACA,OAAArB,EAAAnN,KAAAtG,MAAAzE,IAAA,IAGAwZ,SAAA,WACA,OAAAtB,EAAAnN,KAAAtG,MAAAzE,IAAA,mCC/YAF,EAAAC,QAAA0T,EAGA,IAAAD,EAAA3T,EAAA,KACA4T,EAAA9O,UAAAnB,OAAAsL,OAAA0E,EAAA7O,YAAAoK,YAAA0E,EAEA,IAAAjI,EAAA3L,EAAA,IASA,SAAA4T,EAAAhS,GACA+R,EAAAzI,KAAAtG,KAAAhD,GAUA+J,EAAA2M,SACA1E,EAAA9O,UAAA0T,EAAA7M,EAAA2M,OAAAxT,UAAAvC,OAKAqR,EAAA9O,UAAA5D,OAAA,WACA,IAAAkK,EAAAxG,KAAA8T,SACA,OAAA9T,KAAAuC,IAAAyS,UAAAhV,KAAAwC,IAAAxC,KAAAwC,IAAA9F,KAAAuY,IAAAjV,KAAAwC,IAAAgE,EAAAxG,KAAAwG,yCClCAnL,EAAAC,QAAA6S,EAGA,IAAA3D,EAAApP,EAAA,MACA+S,EAAAjO,UAAAnB,OAAAsL,OAAAG,EAAAtK,YAAAoK,YAAA6D,GAAA5D,UAAA,OAEA,IAKAoB,EACAuJ,EACAC,EAPAzJ,EAAAtQ,EAAA,IACA0L,EAAA1L,EAAA,IACAoT,EAAApT,EAAA,IACA2L,EAAA3L,EAAA,IAaA,SAAA+S,EAAApN,GACAyJ,EAAAlE,KAAAtG,KAAA,GAAAe,GAMAf,KAAAoV,SAAA,GAMApV,KAAAqV,MAAA,GA6BA,SAAAC,KApBAnH,EAAAtD,SAAA,SAAAC,EAAAoD,GAKA,OAJAA,IACAA,EAAA,IAAAC,GACArD,EAAA/J,SACAmN,EAAAmD,WAAAvG,EAAA/J,SACAmN,EAAA2C,QAAA/F,EAAA2F,SAWAtC,EAAAjO,UAAAqV,YAAAxO,EAAAxB,KAAAtJ,QAaAkS,EAAAjO,UAAA+N,KAAA,SAAAA,EAAAnN,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAAjG,GAEA,IAAA0a,EAAAxV,KACA,IAAAgB,EACA,OAAA+F,EAAApG,UAAAsN,EAAAuH,EAAA1U,EAAAC,GAEA,IAAA0U,EAAAzU,IAAAsU,EAGA,SAAAI,EAAAvZ,EAAA+R,GAEA,GAAAlN,EAAA,CAEA,IAAA2U,EAAA3U,EAEA,GADAA,EAAA,KACAyU,EACA,MAAAtZ,EACAwZ,EAAAxZ,EAAA+R,IAIA,SAAA0H,EAAA9U,EAAArC,GACA,IAGA,GAFAsI,EAAAqE,SAAA3M,IAAA,MAAAA,EAAAhC,OAAA,KACAgC,EAAAmB,KAAAsV,MAAAzW,IACAsI,EAAAqE,SAAA3M,GAEA,CACAyW,EAAApU,SAAAA,EACA,IACA8L,EADAiJ,EAAAX,EAAAzW,EAAA+W,EAAAzU,GAEAjE,EAAA,EACA,GAAA+Y,EAAAC,QACA,KAAAhZ,EAAA+Y,EAAAC,QAAAla,SAAAkB,GACA8P,EAAA4I,EAAAD,YAAAzU,EAAA+U,EAAAC,QAAAhZ,MACA4D,EAAAkM,GACA,GAAAiJ,EAAAE,YACA,IAAAjZ,EAAA,EAAAA,EAAA+Y,EAAAE,YAAAna,SAAAkB,GACA8P,EAAA4I,EAAAD,YAAAzU,EAAA+U,EAAAE,YAAAjZ,MACA4D,EAAAkM,GAAA,QAbA4I,EAAAnE,WAAA5S,EAAAsC,SAAA8P,QAAApS,EAAAgS,QAeA,MAAAtU,GACAuZ,EAAAvZ,GAEAsZ,GAAAO,GACAN,EAAA,KAAAF,GAIA,SAAA9U,EAAAI,EAAAmV,GAGA,IAAAC,EAAApV,EAAAqV,YAAA,oBACA,IAAA,EAAAD,EAAA,CACA,IAAAE,EAAAtV,EAAAuV,UAAAH,GACAE,KAAAjB,IACArU,EAAAsV,GAIA,MAAA,EAAAZ,EAAAH,MAAAhM,QAAAvI,IAKA,GAHA0U,EAAAH,MAAA7X,KAAAsD,GAGAA,KAAAqU,EACAM,EACAG,EAAA9U,EAAAqU,EAAArU,OAEAkV,EACAM,WAAA,aACAN,EACAJ,EAAA9U,EAAAqU,EAAArU,YAOA,GAAA2U,EAAA,CACA,IAAAhX,EACA,IACAA,EAAAsI,EAAAnG,GAAA2V,aAAAzV,GAAApC,SAAA,QACA,MAAAvC,GAGA,YAFA8Z,GACAP,EAAAvZ,IAGAyZ,EAAA9U,EAAArC,SAEAuX,EACAjP,EAAArG,MAAAI,EAAA,SAAA3E,EAAAsC,KACAuX,EAEAhV,IAEA7E,EAEA8Z,EAEAD,GACAN,EAAA,KAAAF,GAFAE,EAAAvZ,GAKAyZ,EAAA9U,EAAArC,MAIA,IAAAuX,EAAA,EAIAjP,EAAAqE,SAAAtK,KACAA,EAAA,CAAAA,IACA,IAAA,IAAA8L,EAAA9P,EAAA,EAAAA,EAAAgE,EAAAlF,SAAAkB,GACA8P,EAAA4I,EAAAD,YAAA,GAAAzU,EAAAhE,MACA4D,EAAAkM,GAEA,OAAA6I,EACAD,GACAQ,GACAN,EAAA,KAAAF,GACA1a,IAgCAqT,EAAAjO,UAAAkO,SAAA,SAAAtN,EAAAC,GACA,IAAAgG,EAAAyP,OACA,MAAAvY,MAAA,iBACA,OAAA+B,KAAAiO,KAAAnN,EAAAC,EAAAuU,IAMAnH,EAAAjO,UAAA0R,WAAA,WACA,GAAA5R,KAAAoV,SAAAxZ,OACA,MAAAqC,MAAA,4BAAA+B,KAAAoV,SAAAjN,IAAA,SAAAjB,GACA,MAAA,WAAAA,EAAA4E,OAAA,QAAA5E,EAAA4F,OAAArF,WACA7J,KAAA,OACA,OAAA4M,EAAAtK,UAAA0R,WAAAtL,KAAAtG,OAIA,IAAAyW,EAAA,SAUA,SAAAC,EAAAxI,EAAAhH,GACA,IAAAyP,EAAAzP,EAAA4F,OAAA+E,OAAA3K,EAAA4E,QACA,GAAA6K,EAAA,CACA,IAAAC,EAAA,IAAAlL,EAAAxE,EAAAO,SAAAP,EAAAuC,GAAAvC,EAAAS,KAAAT,EAAA2E,KAAA/Q,EAAAoM,EAAAnG,SAIA,OAHA6V,EAAAvK,eAAAnF,GACAkF,eAAAwK,EACAD,EAAAxL,IAAAyL,IACA,EAEA,OAAA,EASAzI,EAAAjO,UAAAqS,EAAA,SAAAxC,GACA,GAAAA,aAAArE,EAEAqE,EAAAjE,SAAAhR,GAAAiV,EAAA3D,gBACAsK,EAAA1W,EAAA+P,IACA/P,KAAAoV,SAAA5X,KAAAuS,QAEA,GAAAA,aAAAjJ,EAEA2P,EAAAvY,KAAA6R,EAAA9H,QACA8H,EAAAjD,OAAAiD,EAAA9H,MAAA8H,EAAAzI,aAEA,KAAAyI,aAAAvB,GAAA,CAEA,GAAAuB,aAAApE,EACA,IAAA,IAAA7O,EAAA,EAAAA,EAAAkD,KAAAoV,SAAAxZ,QACA8a,EAAA1W,EAAAA,KAAAoV,SAAAtY,IACAkD,KAAAoV,SAAA7U,OAAAzD,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAyS,EAAAgB,YAAAnV,SAAA0B,EACA0C,KAAAuS,EAAAxC,EAAAW,EAAApT,IACAmZ,EAAAvY,KAAA6R,EAAA9H,QACA8H,EAAAjD,OAAAiD,EAAA9H,MAAA8H,KAcA5B,EAAAjO,UAAAsS,EAAA,SAAAzC,GACA,GAAAA,aAAArE,GAEA,GAAAqE,EAAAjE,SAAAhR,EACA,GAAAiV,EAAA3D,eACA2D,EAAA3D,eAAAU,OAAArB,OAAAsE,EAAA3D,gBACA2D,EAAA3D,eAAA,SACA,CACA,IAAAtQ,EAAAkE,KAAAoV,SAAA/L,QAAA0G,IAEA,EAAAjU,GACAkE,KAAAoV,SAAA7U,OAAAzE,EAAA,SAIA,GAAAiU,aAAAjJ,EAEA2P,EAAAvY,KAAA6R,EAAA9H,cACA8H,EAAAjD,OAAAiD,EAAA9H,WAEA,GAAA8H,aAAAvF,EAAA,CAEA,IAAA,IAAA1N,EAAA,EAAAA,EAAAiT,EAAAgB,YAAAnV,SAAAkB,EACAkD,KAAAwS,EAAAzC,EAAAW,EAAA5T,IAEA2Z,EAAAvY,KAAA6R,EAAA9H,cACA8H,EAAAjD,OAAAiD,EAAA9H,QAMAkG,EAAAL,EAAA,SAAAC,EAAA8I,EAAAC,GACAnL,EAAAoC,EACAmH,EAAA2B,EACA1B,EAAA2B,uDC5VAzb,EAAAC,QAAA,4BCKAA,EA6BAoT,QAAAtT,EAAA,gCClCAC,EAAAC,QAAAoT,EAEA,IAAA3H,EAAA3L,EAAA,IAsCA,SAAAsT,EAAAqI,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAApM,UAAA,8BAEA5D,EAAAhH,aAAAuG,KAAAtG,MAMAA,KAAA+W,QAAAA,EAMA/W,KAAAgX,mBAAAA,EAMAhX,KAAAiX,oBAAAA,IA1DAvI,EAAAxO,UAAAnB,OAAAsL,OAAAtD,EAAAhH,aAAAG,YAAAoK,YAAAoE,GAwEAxO,UAAAgX,QAAA,SAAAA,EAAAC,EAAAC,EAAAC,EAAAC,EAAAtW,GAEA,IAAAsW,EACA,MAAA3M,UAAA,6BAEA,IAAA6K,EAAAxV,KACA,IAAAgB,EACA,OAAA+F,EAAApG,UAAAuW,EAAA1B,EAAA2B,EAAAC,EAAAC,EAAAC,GAEA,IAAA9B,EAAAuB,QAEA,OADAT,WAAA,WAAAtV,EAAA/C,MAAA,mBAAA,GACAnD,EAGA,IACA,OAAA0a,EAAAuB,QACAI,EACAC,EAAA5B,EAAAwB,iBAAA,kBAAA,UAAAM,GAAA5B,SACA,SAAAvZ,EAAAsF,GAEA,GAAAtF,EAEA,OADAqZ,EAAAhV,KAAA,QAAArE,EAAAgb,GACAnW,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADA+T,EAAAtY,KAAA,GACApC,EAGA,KAAA2G,aAAA4V,GACA,IACA5V,EAAA4V,EAAA7B,EAAAyB,kBAAA,kBAAA,UAAAxV,GACA,MAAAtF,GAEA,OADAqZ,EAAAhV,KAAA,QAAArE,EAAAgb,GACAnW,EAAA7E,GAKA,OADAqZ,EAAAhV,KAAA,OAAAiB,EAAA0V,GACAnW,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAqZ,EAAAhV,KAAA,QAAArE,EAAAgb,GACAb,WAAA,WAAAtV,EAAA7E,IAAA,GACArB,IASA4T,EAAAxO,UAAAhD,IAAA,SAAAqa,GAOA,OANAvX,KAAA+W,UACAQ,GACAvX,KAAA+W,QAAA,KAAA,KAAA,MACA/W,KAAA+W,QAAA,KACA/W,KAAAQ,KAAA,OAAAH,OAEAL,kCC3IA3E,EAAAC,QAAAoT,EAGA,IAAAlE,EAAApP,EAAA,MACAsT,EAAAxO,UAAAnB,OAAAsL,OAAAG,EAAAtK,YAAAoK,YAAAoE,GAAAnE,UAAA,UAEA,IAAAoE,EAAAvT,EAAA,IACA2L,EAAA3L,EAAA,IACA+T,EAAA/T,EAAA,IAWA,SAAAsT,EAAAzG,EAAAlH,GACAyJ,EAAAlE,KAAAtG,KAAAiI,EAAAlH,GAMAf,KAAAkR,QAAA,GAOAlR,KAAAwX,EAAA,KAyDA,SAAA7G,EAAA8G,GAEA,OADAA,EAAAD,EAAA,KACAC,EA1CA/I,EAAA7D,SAAA,SAAA5C,EAAA6C,GACA,IAAA2M,EAAA,IAAA/I,EAAAzG,EAAA6C,EAAA/J,SAEA,GAAA+J,EAAAoG,QACA,IAAA,IAAAD,EAAAlS,OAAAC,KAAA8L,EAAAoG,SAAApU,EAAA,EAAAA,EAAAmU,EAAArV,SAAAkB,EACA2a,EAAAtM,IAAAwD,EAAA9D,SAAAoG,EAAAnU,GAAAgO,EAAAoG,QAAAD,EAAAnU,MAIA,OAHAgO,EAAA2F,QACAgH,EAAA5G,QAAA/F,EAAA2F,QACAgH,EAAAhN,QAAAK,EAAAL,QACAgN,GAQA/I,EAAAxO,UAAA8K,OAAA,SAAAC,GACA,IAAAyM,EAAAlN,EAAAtK,UAAA8K,OAAA1E,KAAAtG,KAAAiL,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,UAAAsP,GAAAA,EAAA3W,SAAAjG,EACA,UAAA0P,EAAA8F,YAAAtQ,KAAA2X,aAAA1M,IAAA,GACA,SAAAyM,GAAAA,EAAAjH,QAAA3V,EACA,UAAAoQ,EAAAlL,KAAAyK,QAAA3P,KAUAiE,OAAAwN,eAAAmC,EAAAxO,UAAA,eAAA,CACAsM,IAAA,WACA,OAAAxM,KAAAwX,IAAAxX,KAAAwX,EAAAzQ,EAAA+J,QAAA9Q,KAAAkR,aAYAxC,EAAAxO,UAAAsM,IAAA,SAAAvE,GACA,OAAAjI,KAAAkR,QAAAjJ,IACAuC,EAAAtK,UAAAsM,IAAAlG,KAAAtG,KAAAiI,IAMAyG,EAAAxO,UAAA0R,WAAA,WAEA,IADA,IAAAV,EAAAlR,KAAA2X,aACA7a,EAAA,EAAAA,EAAAoU,EAAAtV,SAAAkB,EACAoU,EAAApU,GAAAb,UACA,OAAAuO,EAAAtK,UAAAjE,QAAAqK,KAAAtG,OAMA0O,EAAAxO,UAAAiL,IAAA,SAAA4E,GAGA,GAAA/P,KAAAwM,IAAAuD,EAAA9H,MACA,MAAAhK,MAAA,mBAAA8R,EAAA9H,KAAA,QAAAjI,MAEA,OAAA+P,aAAApB,EAGAgC,GAFA3Q,KAAAkR,QAAAnB,EAAA9H,MAAA8H,GACAjD,OAAA9M,MAGAwK,EAAAtK,UAAAiL,IAAA7E,KAAAtG,KAAA+P,IAMArB,EAAAxO,UAAAuL,OAAA,SAAAsE,GACA,GAAAA,aAAApB,EAAA,CAGA,GAAA3O,KAAAkR,QAAAnB,EAAA9H,QAAA8H,EACA,MAAA9R,MAAA8R,EAAA,uBAAA/P,MAIA,cAFAA,KAAAkR,QAAAnB,EAAA9H,MACA8H,EAAAjD,OAAA,KACA6D,EAAA3Q,MAEA,OAAAwK,EAAAtK,UAAAuL,OAAAnF,KAAAtG,KAAA+P,IAUArB,EAAAxO,UAAAmK,OAAA,SAAA0M,EAAAC,EAAAC,GAEA,IADA,IACAE,EADAS,EAAA,IAAAzI,EAAAT,QAAAqI,EAAAC,EAAAC,GACAna,EAAA,EAAAA,EAAAkD,KAAA2X,aAAA/b,SAAAkB,EAAA,CACA,IAAA+a,EAAA9Q,EAAA+Q,SAAAX,EAAAnX,KAAAwX,EAAA1a,IAAAb,UAAAgM,MAAA1I,QAAA,WAAA,IACAqY,EAAAC,GAAA9Q,EAAA5I,QAAA,CAAA,IAAA,KAAA4I,EAAAgR,WAAAF,GAAAA,EAAA,IAAAA,EAAA9Q,CAAA,iCAAAA,CAAA,CACAiR,EAAAb,EACAc,EAAAd,EAAAhH,oBAAA9C,KACA6K,EAAAf,EAAA/G,qBAAA/C,OAGA,OAAAuK,iDCpKAvc,EAAAC,QAAAqQ,EAGA,IAAAnB,EAAApP,EAAA,MACAuQ,EAAAzL,UAAAnB,OAAAsL,OAAAG,EAAAtK,YAAAoK,YAAAqB,GAAApB,UAAA,OAEA,IAAAzD,EAAA1L,EAAA,IACAoT,EAAApT,EAAA,IACAsQ,EAAAtQ,EAAA,IACAqT,EAAArT,EAAA,IACAsT,EAAAtT,EAAA,IACAwT,EAAAxT,EAAA,IACA2T,EAAA3T,EAAA,IACA6T,EAAA7T,EAAA,IACA2L,EAAA3L,EAAA,IACAiT,EAAAjT,EAAA,IACAkT,EAAAlT,EAAA,IACAmT,EAAAnT,EAAA,IACAyL,EAAAzL,EAAA,IACAyT,EAAAzT,EAAA,IAUA,SAAAuQ,EAAA1D,EAAAlH,GACAyJ,EAAAlE,KAAAtG,KAAAiI,EAAAlH,GAMAf,KAAA+H,OAAA,GAMA/H,KAAAmY,OAAArd,EAMAkF,KAAAoY,WAAAtd,EAMAkF,KAAA4K,SAAA9P,EAMAkF,KAAAuJ,MAAAzO,EAOAkF,KAAAqY,EAAA,KAOArY,KAAAoJ,EAAA,KAOApJ,KAAAsY,EAAA,KAOAtY,KAAAuY,EAAA,KA0HA,SAAA5H,EAAAhJ,GAKA,OAJAA,EAAA0Q,EAAA1Q,EAAAyB,EAAAzB,EAAA2Q,EAAA,YACA3Q,EAAA5K,cACA4K,EAAA7J,cACA6J,EAAAmI,OACAnI,EA5HA5I,OAAAsT,iBAAA1G,EAAAzL,UAAA,CAQAsY,WAAA,CACAhM,IAAA,WAGA,GAAAxM,KAAAqY,EACA,OAAArY,KAAAqY,EAEArY,KAAAqY,EAAA,GACA,IAAA,IAAApH,EAAAlS,OAAAC,KAAAgB,KAAA+H,QAAAjL,EAAA,EAAAA,EAAAmU,EAAArV,SAAAkB,EAAA,CACA,IAAAoK,EAAAlH,KAAA+H,OAAAkJ,EAAAnU,IACA2M,EAAAvC,EAAAuC,GAGA,GAAAzJ,KAAAqY,EAAA5O,GACA,MAAAxL,MAAA,gBAAAwL,EAAA,OAAAzJ,MAEAA,KAAAqY,EAAA5O,GAAAvC,EAEA,OAAAlH,KAAAqY,IAUArQ,YAAA,CACAwE,IAAA,WACA,OAAAxM,KAAAoJ,IAAApJ,KAAAoJ,EAAArC,EAAA+J,QAAA9Q,KAAA+H,WAUA0Q,YAAA,CACAjM,IAAA,WACA,OAAAxM,KAAAsY,IAAAtY,KAAAsY,EAAAvR,EAAA+J,QAAA9Q,KAAAmY,WAUA9K,KAAA,CACAb,IAAA,WACA,OAAAxM,KAAAuY,IAAAvY,KAAAqN,KAAA1B,EAAA+M,oBAAA1Y,KAAA2L,KAEAoH,IAAA,SAAA1F,GAGA,IAAAnN,EAAAmN,EAAAnN,UACAA,aAAA0O,KACAvB,EAAAnN,UAAA,IAAA0O,GAAAtE,YAAA+C,EACAtG,EAAA0N,MAAApH,EAAAnN,UAAAA,IAIAmN,EAAAoC,MAAApC,EAAAnN,UAAAuP,MAAAzP,KAGA+G,EAAA0N,MAAApH,EAAAuB,GAAA,GAEA5O,KAAAuY,EAAAlL,EAIA,IADA,IAAAvQ,EAAA,EACAA,EAAAkD,KAAAgI,YAAApM,SAAAkB,EACAkD,KAAAoJ,EAAAtM,GAAAb,UAGA,IAAA0c,EAAA,GACA,IAAA7b,EAAA,EAAAA,EAAAkD,KAAAyY,YAAA7c,SAAAkB,EACA6b,EAAA3Y,KAAAsY,EAAAxb,GAAAb,UAAAgM,MAAA,CACAuE,IAAAzF,EAAA+L,YAAA9S,KAAAsY,EAAAxb,GAAA6V,OACAI,IAAAhM,EAAAiM,YAAAhT,KAAAsY,EAAAxb,GAAA6V,QAEA7V,GACAiC,OAAAsT,iBAAAhF,EAAAnN,UAAAyY,OAUAhN,EAAA+M,oBAAA,SAAA5Q,GAIA,IAFA,IAEAZ,EAFAD,EAAAF,EAAA5I,QAAA,CAAA,KAAA2J,EAAAG,MAEAnL,EAAA,EAAAA,EAAAgL,EAAAE,YAAApM,SAAAkB,GACAoK,EAAAY,EAAAsB,EAAAtM,IAAAqL,IAAAlB,EACA,YAAAF,EAAAmB,SAAAhB,EAAAe,OACAf,EAAAK,UAAAN,EACA,YAAAF,EAAAmB,SAAAhB,EAAAe,OACA,OAAAhB,EACA,wEADAA,CAEA,yBA6BA0E,EAAAd,SAAA,SAAA5C,EAAA6C,GACA,IAAAnD,EAAA,IAAAgE,EAAA1D,EAAA6C,EAAA/J,SACA4G,EAAAyQ,WAAAtN,EAAAsN,WACAzQ,EAAAiD,SAAAE,EAAAF,SAGA,IAFA,IAAAqG,EAAAlS,OAAAC,KAAA8L,EAAA/C,QACAjL,EAAA,EACAA,EAAAmU,EAAArV,SAAAkB,EACA6K,EAAAwD,UACA,IAAAL,EAAA/C,OAAAkJ,EAAAnU,IAAA4M,QACA+E,EAAA5D,SACAa,EAAAb,UAAAoG,EAAAnU,GAAAgO,EAAA/C,OAAAkJ,EAAAnU,MAEA,GAAAgO,EAAAqN,OACA,IAAAlH,EAAAlS,OAAAC,KAAA8L,EAAAqN,QAAArb,EAAA,EAAAA,EAAAmU,EAAArV,SAAAkB,EACA6K,EAAAwD,IAAAqD,EAAA3D,SAAAoG,EAAAnU,GAAAgO,EAAAqN,OAAAlH,EAAAnU,MACA,GAAAgO,EAAA2F,OACA,IAAAQ,EAAAlS,OAAAC,KAAA8L,EAAA2F,QAAA3T,EAAA,EAAAA,EAAAmU,EAAArV,SAAAkB,EAAA,CACA,IAAA2T,EAAA3F,EAAA2F,OAAAQ,EAAAnU,IACA6K,EAAAwD,KACAsF,EAAAhH,KAAA3O,EACA4Q,EAAAb,SACA4F,EAAA1I,SAAAjN,EACA6Q,EAAAd,SACA4F,EAAAnJ,SAAAxM,EACAgM,EAAA+D,SACA4F,EAAAS,UAAApW,EACA4T,EAAA7D,SACAL,EAAAK,UAAAoG,EAAAnU,GAAA2T,IAWA,OARA3F,EAAAsN,YAAAtN,EAAAsN,WAAAxc,SACA+L,EAAAyQ,WAAAtN,EAAAsN,YACAtN,EAAAF,UAAAE,EAAAF,SAAAhP,SACA+L,EAAAiD,SAAAE,EAAAF,UACAE,EAAAvB,QACA5B,EAAA4B,OAAA,GACAuB,EAAAL,UACA9C,EAAA8C,QAAAK,EAAAL,SACA9C,GAQAgE,EAAAzL,UAAA8K,OAAA,SAAAC,GACA,IAAAyM,EAAAlN,EAAAtK,UAAA8K,OAAA1E,KAAAtG,KAAAiL,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAnE,EAAAqB,SAAA,CACA,UAAAsP,GAAAA,EAAA3W,SAAAjG,EACA,SAAA0P,EAAA8F,YAAAtQ,KAAAyY,YAAAxN,GACA,SAAAT,EAAA8F,YAAAtQ,KAAAgI,YAAAsB,OAAA,SAAAkH,GAAA,OAAAA,EAAAnE,iBAAApB,IAAA,GACA,aAAAjL,KAAAoY,YAAApY,KAAAoY,WAAAxc,OAAAoE,KAAAoY,WAAAtd,EACA,WAAAkF,KAAA4K,UAAA5K,KAAA4K,SAAAhP,OAAAoE,KAAA4K,SAAA9P,EACA,QAAAkF,KAAAuJ,OAAAzO,EACA,SAAA4c,GAAAA,EAAAjH,QAAA3V,EACA,UAAAoQ,EAAAlL,KAAAyK,QAAA3P,KAOA6Q,EAAAzL,UAAA0R,WAAA,WAEA,IADA,IAAA7J,EAAA/H,KAAAgI,YAAAlL,EAAA,EACAA,EAAAiL,EAAAnM,QACAmM,EAAAjL,KAAAb,UACA,IAAAkc,EAAAnY,KAAAyY,YACA,IADA3b,EAAA,EACAA,EAAAqb,EAAAvc,QACAuc,EAAArb,KAAAb,UACA,OAAAuO,EAAAtK,UAAA0R,WAAAtL,KAAAtG,OAMA2L,EAAAzL,UAAAsM,IAAA,SAAAvE,GACA,OAAAjI,KAAA+H,OAAAE,IACAjI,KAAAmY,QAAAnY,KAAAmY,OAAAlQ,IACAjI,KAAAyQ,QAAAzQ,KAAAyQ,OAAAxI,IACA,MAUA0D,EAAAzL,UAAAiL,IAAA,SAAA4E,GAEA,GAAA/P,KAAAwM,IAAAuD,EAAA9H,MACA,MAAAhK,MAAA,mBAAA8R,EAAA9H,KAAA,QAAAjI,MAEA,GAAA+P,aAAArE,GAAAqE,EAAAjE,SAAAhR,EAAA,CAMA,GAAAkF,KAAAqY,EAAArY,KAAAqY,EAAAtI,EAAAtG,IAAAzJ,KAAAwY,WAAAzI,EAAAtG,IACA,MAAAxL,MAAA,gBAAA8R,EAAAtG,GAAA,OAAAzJ,MACA,GAAAA,KAAAsL,aAAAyE,EAAAtG,IACA,MAAAxL,MAAA,MAAA8R,EAAAtG,GAAA,mBAAAzJ,MACA,GAAAA,KAAAuL,eAAAwE,EAAA9H,MACA,MAAAhK,MAAA,SAAA8R,EAAA9H,KAAA,oBAAAjI,MAOA,OALA+P,EAAAjD,QACAiD,EAAAjD,OAAArB,OAAAsE,IACA/P,KAAA+H,OAAAgI,EAAA9H,MAAA8H,GACA9D,QAAAjM,KACA+P,EAAAuB,MAAAtR,MACA2Q,EAAA3Q,MAEA,OAAA+P,aAAAvB,GACAxO,KAAAmY,SACAnY,KAAAmY,OAAA,KACAnY,KAAAmY,OAAApI,EAAA9H,MAAA8H,GACAuB,MAAAtR,MACA2Q,EAAA3Q,OAEAwK,EAAAtK,UAAAiL,IAAA7E,KAAAtG,KAAA+P,IAUApE,EAAAzL,UAAAuL,OAAA,SAAAsE,GACA,GAAAA,aAAArE,GAAAqE,EAAAjE,SAAAhR,EAAA,CAIA,IAAAkF,KAAA+H,QAAA/H,KAAA+H,OAAAgI,EAAA9H,QAAA8H,EACA,MAAA9R,MAAA8R,EAAA,uBAAA/P,MAKA,cAHAA,KAAA+H,OAAAgI,EAAA9H,MACA8H,EAAAjD,OAAA,KACAiD,EAAAwB,SAAAvR,MACA2Q,EAAA3Q,MAEA,GAAA+P,aAAAvB,EAAA,CAGA,IAAAxO,KAAAmY,QAAAnY,KAAAmY,OAAApI,EAAA9H,QAAA8H,EACA,MAAA9R,MAAA8R,EAAA,uBAAA/P,MAKA,cAHAA,KAAAmY,OAAApI,EAAA9H,MACA8H,EAAAjD,OAAA,KACAiD,EAAAwB,SAAAvR,MACA2Q,EAAA3Q,MAEA,OAAAwK,EAAAtK,UAAAuL,OAAAnF,KAAAtG,KAAA+P,IAQApE,EAAAzL,UAAAoL,aAAA,SAAA7B,GACA,OAAAe,EAAAc,aAAAtL,KAAA4K,SAAAnB,IAQAkC,EAAAzL,UAAAqL,eAAA,SAAAtD,GACA,OAAAuC,EAAAe,eAAAvL,KAAA4K,SAAA3C,IAQA0D,EAAAzL,UAAAmK,OAAA,SAAAmF,GACA,OAAA,IAAAxP,KAAAqN,KAAAmC,IAOA7D,EAAAzL,UAAA0Y,MAAA,WAMA,IAFA,IAAAnR,EAAAzH,KAAAyH,SACAkC,EAAA,GACA7M,EAAA,EAAAA,EAAAkD,KAAAgI,YAAApM,SAAAkB,EACA6M,EAAAnM,KAAAwC,KAAAoJ,EAAAtM,GAAAb,UAAAoL,cAGArH,KAAAjD,OAAAsR,EAAArO,KAAAqO,CAAA,CACAY,OAAAA,EACAtF,MAAAA,EACA5C,KAAAA,IAEA/G,KAAAlC,OAAAwQ,EAAAtO,KAAAsO,CAAA,CACAS,OAAAA,EACApF,MAAAA,EACA5C,KAAAA,IAEA/G,KAAA8P,OAAAvB,EAAAvO,KAAAuO,CAAA,CACA5E,MAAAA,EACA5C,KAAAA,IAEA/G,KAAA6H,WAAAhB,EAAAgB,WAAA7H,KAAA6G,CAAA,CACA8C,MAAAA,EACA5C,KAAAA,IAEA/G,KAAAoI,SAAAvB,EAAAuB,SAAApI,KAAA6G,CAAA,CACA8C,MAAAA,EACA5C,KAAAA,IAIA,IAAA8R,EAAAhK,EAAApH,GACA,GAAAoR,EAAA,CACA,IAAAC,EAAA/Z,OAAAsL,OAAArK,MAEA8Y,EAAAjR,WAAA7H,KAAA6H,WACA7H,KAAA6H,WAAAgR,EAAAhR,WAAA/D,KAAAgV,GAGAA,EAAA1Q,SAAApI,KAAAoI,SACApI,KAAAoI,SAAAyQ,EAAAzQ,SAAAtE,KAAAgV,GAIA,OAAA9Y,MASA2L,EAAAzL,UAAAnD,OAAA,SAAAkP,EAAAyD,GACA,OAAA1P,KAAA4Y,QAAA7b,OAAAkP,EAAAyD,IASA/D,EAAAzL,UAAAyP,gBAAA,SAAA1D,EAAAyD,GACA,OAAA1P,KAAAjD,OAAAkP,EAAAyD,GAAAA,EAAAlJ,IAAAkJ,EAAAqJ,OAAArJ,GAAAsJ,UAWArN,EAAAzL,UAAApC,OAAA,SAAA8R,EAAAhU,GACA,OAAAoE,KAAA4Y,QAAA9a,OAAA8R,EAAAhU,IAUA+P,EAAAzL,UAAA2P,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAA1E,OAAAuF,IACA5P,KAAAlC,OAAA8R,EAAAA,EAAAkE,WAQAnI,EAAAzL,UAAA4P,OAAA,SAAA7D,GACA,OAAAjM,KAAA4Y,QAAA9I,OAAA7D,IAQAN,EAAAzL,UAAA2H,WAAA,SAAAkI,GACA,OAAA/P,KAAA4Y,QAAA/Q,WAAAkI,IA4BApE,EAAAzL,UAAAkI,SAAA,SAAA6D,EAAAlL,GACA,OAAAf,KAAA4Y,QAAAxQ,SAAA6D,EAAAlL,IAkBA4K,EAAA2B,EAAA,SAAA2L,GACA,OAAA,SAAAC,GACAnS,EAAA2G,aAAAwL,EAAAD,uHCpkBA,IAAAtP,EAAArO,EAEAyL,EAAA3L,EAAA,IAEA8c,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAiB,EAAA7R,EAAAzL,GACA,IAAAiB,EAAA,EAAAsc,EAAA,GAEA,IADAvd,GAAA,EACAiB,EAAAwK,EAAA1L,QAAAwd,EAAAlB,EAAApb,EAAAjB,IAAAyL,EAAAxK,KACA,OAAAsc,EAuBAzP,EAAAC,MAAAuP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBAxP,EAAAkD,SAAAsM,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACApS,EAAAqG,WACA,OAaAzD,EAAAf,KAAAuQ,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBAxP,EAAAM,OAAAkP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBAxP,EAAAE,OAAAsP,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,gCC5LA,IAIAxN,EACA7E,EALAC,EAAA1L,EAAAC,QAAAF,EAAA,IAEAgU,EAAAhU,EAAA,IAKA2L,EAAA5I,QAAA/C,EAAA,GACA2L,EAAArG,MAAAtF,EAAA,GACA2L,EAAAxB,KAAAnK,EAAA,GAMA2L,EAAAnG,GAAAmG,EAAAlG,QAAA,MAOAkG,EAAA+J,QAAA,SAAAf,GACA,GAAAA,EAAA,CAIA,IAHA,IAAA/Q,EAAAD,OAAAC,KAAA+Q,GACAQ,EAAA7U,MAAAsD,EAAApD,QACAE,EAAA,EACAA,EAAAkD,EAAApD,QACA2U,EAAAzU,GAAAiU,EAAA/Q,EAAAlD,MACA,OAAAyU,EAEA,MAAA,IAQAxJ,EAAAqB,SAAA,SAAAmI,GAGA,IAFA,IAAAR,EAAA,GACAjU,EAAA,EACAA,EAAAyU,EAAA3U,QAAA,CACA,IAAAyd,EAAA9I,EAAAzU,KACAwG,EAAAiO,EAAAzU,KACAwG,IAAAxH,IACAiV,EAAAsJ,GAAA/W,GAEA,OAAAyN,GAGA,IAAAuJ,EAAA,MACAC,EAAA,KAOAxS,EAAAgR,WAAA,SAAA9P,GACA,MAAA,uTAAA/J,KAAA+J,IAQAlB,EAAAmB,SAAA,SAAAd,GACA,OAAA,YAAAlJ,KAAAkJ,IAAAL,EAAAgR,WAAA3Q,GACA,KAAAA,EAAA7H,QAAA+Z,EAAA,QAAA/Z,QAAAga,EAAA,OAAA,KACA,IAAAnS,GAQAL,EAAAyS,QAAA,SAAAC,GACA,OAAAA,EAAAhd,OAAA,GAAAid,cAAAD,EAAApD,UAAA,IAGA,IAAAsD,EAAA,YAOA5S,EAAA6S,UAAA,SAAAH,GACA,OAAAA,EAAApD,UAAA,EAAA,GACAoD,EAAApD,UAAA,GACA9W,QAAAoa,EAAA,SAAAna,EAAAC,GAAA,OAAAA,EAAAia,iBASA3S,EAAAuB,kBAAA,SAAAuR,EAAAtc,GACA,OAAAsc,EAAApQ,GAAAlM,EAAAkM,IAWA1C,EAAA2G,aAAA,SAAAL,EAAA4L,GAGA,GAAA5L,EAAAoC,MAMA,OALAwJ,GAAA5L,EAAAoC,MAAAxH,OAAAgR,IACAlS,EAAA+S,aAAArO,OAAA4B,EAAAoC,OACApC,EAAAoC,MAAAxH,KAAAgR,EACAlS,EAAA+S,aAAA3O,IAAAkC,EAAAoC,QAEApC,EAAAoC,MAIA9D,IACAA,EAAAvQ,EAAA,KAEA,IAAAuM,EAAA,IAAAgE,EAAAsN,GAAA5L,EAAApF,MAKA,OAJAlB,EAAA+S,aAAA3O,IAAAxD,GACAA,EAAA0F,KAAAA,EACAtO,OAAAwN,eAAAc,EAAA,QAAA,CAAA3N,MAAAiI,EAAAoS,YAAA,IACAhb,OAAAwN,eAAAc,EAAAnN,UAAA,QAAA,CAAAR,MAAAiI,EAAAoS,YAAA,IACApS,GAGA,IAAAqS,EAAA,EAOAjT,EAAA4G,aAAA,SAAAoC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAGA3I,IACAA,EAAA1L,EAAA,KAEA,IAAA2P,EAAA,IAAAjE,EAAA,OAAAkT,IAAAjK,GAGA,OAFAhJ,EAAA+S,aAAA3O,IAAAJ,GACAhM,OAAAwN,eAAAwD,EAAA,QAAA,CAAArQ,MAAAqL,EAAAgP,YAAA,IACAhP,GASAhM,OAAAwN,eAAAxF,EAAA,eAAA,CACAyF,IAAA,WACA,OAAA4C,EAAA,YAAAA,EAAA,UAAA,IAAAhU,EAAA,yEC9KAC,EAAAC,QAAA2X,EAEA,IAAAlM,EAAA3L,EAAA,IAUA,SAAA6X,EAAAhO,EAAAC,GASAlF,KAAAiF,GAAAA,IAAA,EAMAjF,KAAAkF,GAAAA,IAAA,EAQA,IAAA+U,EAAAhH,EAAAgH,KAAA,IAAAhH,EAAA,EAAA,GAEAgH,EAAAjR,SAAA,WAAA,OAAA,GACAiR,EAAAC,SAAAD,EAAApF,SAAA,WAAA,OAAA7U,MACAia,EAAAre,OAAA,WAAA,OAAA,GAOA,IAAAue,EAAAlH,EAAAkH,SAAA,mBAOAlH,EAAAjG,WAAA,SAAAtN,GACA,GAAA,IAAAA,EACA,OAAAua,EACA,IAAA/W,EAAAxD,EAAA,EACAwD,IACAxD,GAAAA,GACA,IAAAuF,EAAAvF,IAAA,EACAwF,GAAAxF,EAAAuF,GAAA,aAAA,EAUA,OATA/B,IACAgC,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAA+N,EAAAhO,EAAAC,IAQA+N,EAAAmH,KAAA,SAAA1a,GACA,GAAA,iBAAAA,EACA,OAAAuT,EAAAjG,WAAAtN,GACA,GAAAqH,EAAAqE,SAAA1L,GAAA,CAEA,IAAAqH,EAAAoF,KAGA,OAAA8G,EAAAjG,WAAAqN,SAAA3a,EAAA,KAFAA,EAAAqH,EAAAoF,KAAAmO,WAAA5a,GAIA,OAAAA,EAAAmJ,KAAAnJ,EAAAoJ,KAAA,IAAAmK,EAAAvT,EAAAmJ,MAAA,EAAAnJ,EAAAoJ,OAAA,GAAAmR,GAQAhH,EAAA/S,UAAA8I,SAAA,SAAAD,GACA,IAAAA,GAAA/I,KAAAkF,KAAA,GAAA,CACA,IAAAD,EAAA,GAAAjF,KAAAiF,KAAA,EACAC,GAAAlF,KAAAkF,KAAA,EAGA,OAFAD,IACAC,EAAAA,EAAA,IAAA,KACAD,EAAA,WAAAC,GAEA,OAAAlF,KAAAiF,GAAA,WAAAjF,KAAAkF,IAQA+N,EAAA/S,UAAAqa,OAAA,SAAAxR,GACA,OAAAhC,EAAAoF,KACA,IAAApF,EAAAoF,KAAA,EAAAnM,KAAAiF,GAAA,EAAAjF,KAAAkF,KAAA6D,GAEA,CAAAF,IAAA,EAAA7I,KAAAiF,GAAA6D,KAAA,EAAA9I,KAAAkF,GAAA6D,WAAAA,IAGA,IAAA/K,EAAAP,OAAAyC,UAAAlC,WAOAiV,EAAAuH,SAAA,SAAAC,GACA,OAAAA,IAAAN,EACAF,EACA,IAAAhH,GACAjV,EAAAsI,KAAAmU,EAAA,GACAzc,EAAAsI,KAAAmU,EAAA,IAAA,EACAzc,EAAAsI,KAAAmU,EAAA,IAAA,GACAzc,EAAAsI,KAAAmU,EAAA,IAAA,MAAA,GAEAzc,EAAAsI,KAAAmU,EAAA,GACAzc,EAAAsI,KAAAmU,EAAA,IAAA,EACAzc,EAAAsI,KAAAmU,EAAA,IAAA,GACAzc,EAAAsI,KAAAmU,EAAA,IAAA,MAAA,IAQAxH,EAAA/S,UAAAwa,OAAA,WACA,OAAAjd,OAAAC,aACA,IAAAsC,KAAAiF,GACAjF,KAAAiF,KAAA,EAAA,IACAjF,KAAAiF,KAAA,GAAA,IACAjF,KAAAiF,KAAA,GACA,IAAAjF,KAAAkF,GACAlF,KAAAkF,KAAA,EAAA,IACAlF,KAAAkF,KAAA,GAAA,IACAlF,KAAAkF,KAAA,KAQA+N,EAAA/S,UAAAga,SAAA,WACA,IAAAS,EAAA3a,KAAAkF,IAAA,GAGA,OAFAlF,KAAAkF,KAAAlF,KAAAkF,IAAA,EAAAlF,KAAAiF,KAAA,IAAA0V,KAAA,EACA3a,KAAAiF,IAAAjF,KAAAiF,IAAA,EAAA0V,KAAA,EACA3a,MAOAiT,EAAA/S,UAAA2U,SAAA,WACA,IAAA8F,IAAA,EAAA3a,KAAAiF,IAGA,OAFAjF,KAAAiF,KAAAjF,KAAAiF,KAAA,EAAAjF,KAAAkF,IAAA,IAAAyV,KAAA,EACA3a,KAAAkF,IAAAlF,KAAAkF,KAAA,EAAAyV,KAAA,EACA3a,MAOAiT,EAAA/S,UAAAtE,OAAA,WACA,IAAAgf,EAAA5a,KAAAiF,GACA4V,GAAA7a,KAAAiF,KAAA,GAAAjF,KAAAkF,IAAA,KAAA,EACA4V,EAAA9a,KAAAkF,KAAA,GACA,OAAA,IAAA4V,EACA,IAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,kCCrMA,IAAA/T,EAAAzL,EAoOA,SAAAmZ,EAAAsG,EAAAC,EAAArO,GACA,IAAA,IAAA3N,EAAAD,OAAAC,KAAAgc,GAAAle,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAie,EAAA/b,EAAAlC,MAAAhC,GAAA6R,IACAoO,EAAA/b,EAAAlC,IAAAke,EAAAhc,EAAAlC,KACA,OAAAie,EAoBA,SAAAE,EAAAhT,GAEA,SAAAiT,EAAAjP,EAAAuD,GAEA,KAAAxP,gBAAAkb,GACA,OAAA,IAAAA,EAAAjP,EAAAuD,GAKAzQ,OAAAwN,eAAAvM,KAAA,UAAA,CAAAwM,IAAA,WAAA,OAAAP,KAGAhO,MAAAkd,kBACAld,MAAAkd,kBAAAnb,KAAAkb,GAEAnc,OAAAwN,eAAAvM,KAAA,QAAA,CAAAN,MAAAzB,QAAAmd,OAAA,KAEA5L,GACAiF,EAAAzU,KAAAwP,GAWA,OARA0L,EAAAhb,UAAAnB,OAAAsL,OAAApM,MAAAiC,YAAAoK,YAAA4Q,EAEAnc,OAAAwN,eAAA2O,EAAAhb,UAAA,OAAA,CAAAsM,IAAA,WAAA,OAAAvE,KAEAiT,EAAAhb,UAAAxB,SAAA,WACA,OAAAsB,KAAAiI,KAAA,KAAAjI,KAAAiM,SAGAiP,EAvRAnU,EAAApG,UAAAvF,EAAA,GAGA2L,EAAA1K,OAAAjB,EAAA,GAGA2L,EAAAhH,aAAA3E,EAAA,GAGA2L,EAAAqN,MAAAhZ,EAAA,GAGA2L,EAAAlG,QAAAzF,EAAA,GAGA2L,EAAAR,KAAAnL,EAAA,IAGA2L,EAAAsU,KAAAjgB,EAAA,GAGA2L,EAAAkM,SAAA7X,EAAA,IAGA2L,EAAAuU,OAAA,oBAAAC,QAAAA,QACA,oBAAAD,QAAAA,QACA,oBAAA9F,MAAAA,MACAxV,KAQA+G,EAAAqG,WAAArO,OAAAkO,OAAAlO,OAAAkO,OAAA,IAAA,GAOAlG,EAAAoG,YAAApO,OAAAkO,OAAAlO,OAAAkO,OAAA,IAAA,GAQAlG,EAAAyP,UAAAzP,EAAAuU,OAAA1F,SAAA7O,EAAAuU,OAAA1F,QAAA4F,UAAAzU,EAAAuU,OAAA1F,QAAA4F,SAAAC,MAQA1U,EAAAsE,UAAAqQ,OAAArQ,WAAA,SAAA3L,GACA,MAAA,iBAAAA,GAAAic,SAAAjc,IAAAhD,KAAAiD,MAAAD,KAAAA,GAQAqH,EAAAqE,SAAA,SAAA1L,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQAsJ,EAAAgF,SAAA,SAAArM,GACA,OAAAA,GAAA,iBAAAA,GAWAqH,EAAA6U,MAQA7U,EAAA8U,MAAA,SAAArL,EAAApJ,GACA,IAAA1H,EAAA8Q,EAAApJ,GACA,QAAA,MAAA1H,IAAA8Q,EAAAsL,eAAA1U,MACA,iBAAA1H,GAAA,GAAAhE,MAAA+V,QAAA/R,GAAAA,EAAA9D,OAAAmD,OAAAC,KAAAU,GAAA9D,UAeAmL,EAAA2M,OAAA,WACA,IACA,IAAAA,EAAA3M,EAAAlG,QAAA,UAAA6S,OAEA,OAAAA,EAAAxT,UAAA6b,UAAArI,EAAA,KACA,MAAApO,GAEA,OAAA,MAPA,GAYAyB,EAAAiV,EAAA,KAGAjV,EAAAkV,EAAA,KAOAlV,EAAAmG,UAAA,SAAAgP,GAEA,MAAA,iBAAAA,EACAnV,EAAA2M,OACA3M,EAAAkV,EAAAC,GACA,IAAAnV,EAAArL,MAAAwgB,GACAnV,EAAA2M,OACA3M,EAAAiV,EAAAE,GACA,oBAAAva,WACAua,EACA,IAAAva,WAAAua,IAOAnV,EAAArL,MAAA,oBAAAiG,WAAAA,WAAAjG,MAeAqL,EAAAoF,KAAApF,EAAAuU,OAAAa,SAAApV,EAAAuU,OAAAa,QAAAhQ,MACApF,EAAAuU,OAAAnP,MACApF,EAAAlG,QAAA,QAOAkG,EAAAqV,OAAA,mBAOArV,EAAAsV,QAAA,wBAOAtV,EAAAuV,QAAA,6CAOAvV,EAAAwV,WAAA,SAAA7c,GACA,OAAAA,EACAqH,EAAAkM,SAAAmH,KAAA1a,GAAAgb,SACA3T,EAAAkM,SAAAkH,UASApT,EAAAyV,aAAA,SAAA/B,EAAA1R,GACA,IAAAwK,EAAAxM,EAAAkM,SAAAuH,SAAAC,GACA,OAAA1T,EAAAoF,KACApF,EAAAoF,KAAAsQ,SAAAlJ,EAAAtO,GAAAsO,EAAArO,GAAA6D,GACAwK,EAAAvK,WAAAD,IAkBAhC,EAAA0N,MAAAA,EAOA1N,EAAA+Q,QAAA,SAAA2B,GACA,OAAAA,EAAAhd,OAAA,GAAAuP,cAAAyN,EAAApD,UAAA,IA0CAtP,EAAAkU,SAAAA,EAmBAlU,EAAA2V,cAAAzB,EAAA,iBAoBAlU,EAAA+L,YAAA,SAAAJ,GAEA,IADA,IAAAiK,EAAA,GACA7f,EAAA,EAAAA,EAAA4V,EAAA9W,SAAAkB,EACA6f,EAAAjK,EAAA5V,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAgB,MAAAlD,EAAAkC,EAAApD,OAAA,GAAA,EAAAkB,IAAAA,EACA,GAAA,IAAA6f,EAAA3d,EAAAlC,KAAAkD,KAAAhB,EAAAlC,MAAAhC,GAAA,OAAAkF,KAAAhB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAiK,EAAAiM,YAAA,SAAAN,GAQA,OAAA,SAAAzK,GACA,IAAA,IAAAnL,EAAA,EAAAA,EAAA4V,EAAA9W,SAAAkB,EACA4V,EAAA5V,KAAAmL,UACAjI,KAAA0S,EAAA5V,MAoBAiK,EAAAkE,cAAA,CACA2R,MAAAnf,OACAof,MAAApf,OACAwL,MAAAxL,OACAqN,MAAA,GAIA/D,EAAA+G,EAAA,WACA,IAAA4F,EAAA3M,EAAA2M,OAEAA,GAMA3M,EAAAiV,EAAAtI,EAAA0G,OAAAzY,WAAAyY,MAAA1G,EAAA0G,MAEA,SAAA1a,EAAAod,GACA,OAAA,IAAApJ,EAAAhU,EAAAod,IAEA/V,EAAAkV,EAAAvI,EAAAqJ,aAEA,SAAA7W,GACA,OAAA,IAAAwN,EAAAxN,KAbAa,EAAAiV,EAAAjV,EAAAkV,EAAA,gEC7YA5gB,EAAAC,QAwHA,SAAAwM,GAGA,IAAAb,EAAAF,EAAA5I,QAAA,CAAA,KAAA2J,EAAAG,KAAA,UAAAlB,CACA,oCADAA,CAEA,WAAA,mBACAoR,EAAArQ,EAAA2Q,YACAuE,EAAA,GACA7E,EAAAvc,QAAAqL,EACA,YAEA,IAAA,IAAAnK,EAAA,EAAAA,EAAAgL,EAAAE,YAAApM,SAAAkB,EAAA,CACA,IAAAoK,EAAAY,EAAAsB,EAAAtM,GAAAb,UACAuN,EAAA,IAAAzC,EAAAmB,SAAAhB,EAAAe,MAMA,GAJAf,EAAAiD,UAAAlD,EACA,sCAAAuC,EAAAtC,EAAAe,MAGAf,EAAAiB,IAAAlB,EACA,yBAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,UAFAD,CAGA,wBAAAuC,EAHAvC,CAIA,gCACAiW,EAAAjW,EAAAC,EAAA,QACAiW,EAAAlW,EAAAC,EAAApK,EAAA0M,EAAA,SAAA2T,CACA,UAGA,GAAAjW,EAAAK,SAAAN,EACA,yBAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,SAFAD,CAGA,gCAAAuC,GACA2T,EAAAlW,EAAAC,EAAApK,EAAA0M,EAAA,MAAA2T,CACA,SAGA,CACA,GAAAjW,EAAAwB,OAAA,CACA,IAAA0U,EAAArW,EAAAmB,SAAAhB,EAAAwB,OAAAT,MACA,IAAA+U,EAAA9V,EAAAwB,OAAAT,OAAAhB,EACA,cAAAmW,EADAnW,CAEA,WAAAC,EAAAwB,OAAAT,KAAA,qBACA+U,EAAA9V,EAAAwB,OAAAT,MAAA,EACAhB,EACA,QAAAmW,GAEAD,EAAAlW,EAAAC,EAAApK,EAAA0M,GAEAtC,EAAAiD,UAAAlD,EACA,KAEA,OAAAA,EACA,gBA3KA,IAAAH,EAAA1L,EAAA,IACA2L,EAAA3L,EAAA,IAEA,SAAA6hB,EAAA/V,EAAAmW,GACA,OAAAnW,EAAAe,KAAA,KAAAoV,GAAAnW,EAAAK,UAAA,UAAA8V,EAAA,KAAAnW,EAAAiB,KAAA,WAAAkV,EAAA,MAAAnW,EAAAwC,QAAA,IAAA,IAAA,YAYA,SAAAyT,EAAAlW,EAAAC,EAAAC,EAAAqC,GAEA,GAAAtC,EAAAG,aACA,GAAAH,EAAAG,wBAAAP,EAAA,CAAAG,EACA,cAAAuC,EADAvC,CAEA,WAFAA,CAGA,WAAAgW,EAAA/V,EAAA,eACA,IAAA,IAAAlI,EAAAD,OAAAC,KAAAkI,EAAAG,aAAAC,QAAAhK,EAAA,EAAAA,EAAA0B,EAAApD,SAAA0B,EAAA2J,EACA,WAAAC,EAAAG,aAAAC,OAAAtI,EAAA1B,KACA2J,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAqC,EAFAvC,CAGA,QAHAA,CAIA,aAAAC,EAAAe,KAAA,IAJAhB,CAKA,UAGA,OAAAC,EAAAS,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAV,EACA,0BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAuC,EAAAA,EAAAA,EAAAA,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAuC,EAAAA,EAAAA,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,WAIA,OAAAD,EAYA,SAAAiW,EAAAjW,EAAAC,EAAAsC,GAEA,OAAAtC,EAAAwC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAzC,EACA,6BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAuC,EADAvC,CAEA,WAAAgW,EAAA/V,EAAA,gBAGA,OAAAD,uCCzGA,IAAA4H,EAAAvT,EAEAsT,EAAAxT,EAAA,IA6BAyT,EAAA,wBAAA,CAEAhH,WAAA,SAAAkI,GAGA,GAAAA,GAAAA,EAAA,SAAA,CACA,IAAApI,EAAA3H,KAAA6R,OAAA9B,EAAA,UAEA,GAAApI,EAAA,CAEA,IAAA2V,EAAA,MAAAvN,EAAA,SAAAtT,OAAA,GACAsT,EAAA,SAAAwN,OAAA,GAAAxN,EAAA,SAEA,OAAA/P,KAAAqK,OAAA,CACAiT,SAAA,IAAAA,EACA5d,MAAAiI,EAAA5K,OAAA4K,EAAAE,WAAAkI,IAAA2F,YAKA,OAAA1V,KAAA6H,WAAAkI,IAGA3H,SAAA,SAAA6D,EAAAlL,GAGA,GAAAA,GAAAA,EAAA+J,MAAAmB,EAAAqR,UAAArR,EAAAvM,MAAA,CAEA,IAAAuI,EAAAgE,EAAAqR,SAAAjH,UAAApK,EAAAqR,SAAAnH,YAAA,KAAA,GACAxO,EAAA3H,KAAA6R,OAAA5J,GAEAN,IACAsE,EAAAtE,EAAA7J,OAAAmO,EAAAvM,QAIA,KAAAuM,aAAAjM,KAAAqN,OAAApB,aAAA2C,EAAA,CACA,IAAAmB,EAAA9D,EAAAwD,MAAArH,SAAA6D,EAAAlL,GAEA,OADAgP,EAAA,SAAA9D,EAAAwD,MAAAhI,SACAsI,EAGA,OAAA/P,KAAAoI,SAAA6D,EAAAlL,iCC/EA1F,EAAAC,QAAA2T,EAEA,IAEAC,EAFAnI,EAAA3L,EAAA,IAIA6X,EAAAlM,EAAAkM,SACA5W,EAAA0K,EAAA1K,OACAkK,EAAAQ,EAAAR,KAWA,SAAAiX,EAAAjiB,EAAAiL,EAAAlE,GAMAtC,KAAAzE,GAAAA,EAMAyE,KAAAwG,IAAAA,EAMAxG,KAAAyd,KAAA3iB,EAMAkF,KAAAsC,IAAAA,EAIA,SAAAob,KAUA,SAAAC,EAAAjO,GAMA1P,KAAA4d,KAAAlO,EAAAkO,KAMA5d,KAAA6d,KAAAnO,EAAAmO,KAMA7d,KAAAwG,IAAAkJ,EAAAlJ,IAMAxG,KAAAyd,KAAA/N,EAAAoO,OAQA,SAAA7O,IAMAjP,KAAAwG,IAAA,EAMAxG,KAAA4d,KAAA,IAAAJ,EAAAE,EAAA,EAAA,GAMA1d,KAAA6d,KAAA7d,KAAA4d,KAMA5d,KAAA8d,OAAA,KAqDA,SAAAC,EAAAzb,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAA0b,EAAAxX,EAAAlE,GACAtC,KAAAwG,IAAAA,EACAxG,KAAAyd,KAAA3iB,EACAkF,KAAAsC,IAAAA,EA8CA,SAAA2b,EAAA3b,EAAAC,EAAAC,GACA,KAAAF,EAAA4C,IACA3C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,IAAA3C,EAAA2C,KAAA,EAAA3C,EAAA4C,IAAA,MAAA,EACA5C,EAAA4C,MAAA,EAEA,KAAA,IAAA5C,EAAA2C,IACA1C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,GAAA3C,EAAA2C,KAAA,EAEA1C,EAAAC,KAAAF,EAAA2C,GA2CA,SAAAiZ,EAAA5b,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAtKA2M,EAAA5E,OAAAtD,EAAA2M,OACA,WACA,OAAAzE,EAAA5E,OAAA,WACA,OAAA,IAAA6E,OAIA,WACA,OAAA,IAAAD,GAQAA,EAAAhJ,MAAA,SAAAC,GACA,OAAA,IAAAa,EAAArL,MAAAwK,IAKAa,EAAArL,QAAAA,QACAuT,EAAAhJ,MAAAc,EAAAsU,KAAApM,EAAAhJ,MAAAc,EAAArL,MAAAwE,UAAA2T,WAUA5E,EAAA/O,UAAAie,EAAA,SAAA5iB,EAAAiL,EAAAlE,GAGA,OAFAtC,KAAA6d,KAAA7d,KAAA6d,KAAAJ,KAAA,IAAAD,EAAAjiB,EAAAiL,EAAAlE,GACAtC,KAAAwG,KAAAA,EACAxG,OA8BAge,EAAA9d,UAAAnB,OAAAsL,OAAAmT,EAAAtd,YACA3E,GAxBA,SAAA+G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BA2M,EAAA/O,UAAA4T,OAAA,SAAApU,GAWA,OARAM,KAAAwG,MAAAxG,KAAA6d,KAAA7d,KAAA6d,KAAAJ,KAAA,IAAAO,GACAte,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAxG,MASAiP,EAAA/O,UAAA6T,MAAA,SAAArU,GACA,OAAAA,EAAA,EACAM,KAAAme,EAAAF,EAAA,GAAAhL,EAAAjG,WAAAtN,IACAM,KAAA8T,OAAApU,IAQAuP,EAAA/O,UAAA8T,OAAA,SAAAtU,GACA,OAAAM,KAAA8T,QAAApU,GAAA,EAAAA,GAAA,MAAA,IAkCAuP,EAAA/O,UAAAwU,MAZAzF,EAAA/O,UAAAyU,OAAA,SAAAjV,GACA,IAAA6T,EAAAN,EAAAmH,KAAA1a,GACA,OAAAM,KAAAme,EAAAF,EAAA1K,EAAA3X,SAAA2X,IAkBAtE,EAAA/O,UAAA0U,OAAA,SAAAlV,GACA,IAAA6T,EAAAN,EAAAmH,KAAA1a,GAAAwa,WACA,OAAAla,KAAAme,EAAAF,EAAA1K,EAAA3X,SAAA2X,IAQAtE,EAAA/O,UAAA+T,KAAA,SAAAvU,GACA,OAAAM,KAAAme,EAAAJ,EAAA,EAAAre,EAAA,EAAA,IAyBAuP,EAAA/O,UAAAiU,SAVAlF,EAAA/O,UAAAgU,QAAA,SAAAxU,GACA,OAAAM,KAAAme,EAAAD,EAAA,EAAAxe,IAAA,IA6BAuP,EAAA/O,UAAA6U,SAZA9F,EAAA/O,UAAA4U,QAAA,SAAApV,GACA,IAAA6T,EAAAN,EAAAmH,KAAA1a,GACA,OAAAM,KAAAme,EAAAD,EAAA,EAAA3K,EAAAtO,IAAAkZ,EAAAD,EAAA,EAAA3K,EAAArO,KAkBA+J,EAAA/O,UAAAkU,MAAA,SAAA1U,GACA,OAAAM,KAAAme,EAAApX,EAAAqN,MAAAxR,aAAA,EAAAlD,IASAuP,EAAA/O,UAAAmU,OAAA,SAAA3U,GACA,OAAAM,KAAAme,EAAApX,EAAAqN,MAAA3P,cAAA,EAAA/E,IAGA,IAAA0e,EAAArX,EAAArL,MAAAwE,UAAA6S,IACA,SAAAzQ,EAAAC,EAAAC,GACAD,EAAAwQ,IAAAzQ,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAA1F,EAAA,EAAAA,EAAAwF,EAAA1G,SAAAkB,EACAyF,EAAAC,EAAA1F,GAAAwF,EAAAxF,IAQAmS,EAAA/O,UAAA+I,MAAA,SAAAvJ,GACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EACA,IAAA4K,EACA,OAAAxG,KAAAme,EAAAJ,EAAA,EAAA,GACA,GAAAhX,EAAAqE,SAAA1L,GAAA,CACA,IAAA6C,EAAA0M,EAAAhJ,MAAAO,EAAAnK,EAAAT,OAAA8D,IACArD,EAAAyB,OAAA4B,EAAA6C,EAAA,GACA7C,EAAA6C,EAEA,OAAAvC,KAAA8T,OAAAtN,GAAA2X,EAAAC,EAAA5X,EAAA9G,IAQAuP,EAAA/O,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAD,EAAA3K,OAAA8D,GACA,OAAA8G,EACAxG,KAAA8T,OAAAtN,GAAA2X,EAAA5X,EAAAG,MAAAF,EAAA9G,GACAM,KAAAme,EAAAJ,EAAA,EAAA,IAQA9O,EAAA/O,UAAA6Y,KAAA,WAIA,OAHA/Y,KAAA8d,OAAA,IAAAH,EAAA3d,MACAA,KAAA4d,KAAA5d,KAAA6d,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACA1d,KAAAwG,IAAA,EACAxG,MAOAiP,EAAA/O,UAAAme,MAAA,WAUA,OATAre,KAAA8d,QACA9d,KAAA4d,KAAA5d,KAAA8d,OAAAF,KACA5d,KAAA6d,KAAA7d,KAAA8d,OAAAD,KACA7d,KAAAwG,IAAAxG,KAAA8d,OAAAtX,IACAxG,KAAA8d,OAAA9d,KAAA8d,OAAAL,OAEAzd,KAAA4d,KAAA5d,KAAA6d,KAAA,IAAAL,EAAAE,EAAA,EAAA,GACA1d,KAAAwG,IAAA,GAEAxG,MAOAiP,EAAA/O,UAAA8Y,OAAA,WACA,IAAA4E,EAAA5d,KAAA4d,KACAC,EAAA7d,KAAA6d,KACArX,EAAAxG,KAAAwG,IAOA,OANAxG,KAAAqe,QAAAvK,OAAAtN,GACAA,IACAxG,KAAA6d,KAAAJ,KAAAG,EAAAH,KACAzd,KAAA6d,KAAAA,EACA7d,KAAAwG,KAAAA,GAEAxG,MAOAiP,EAAA/O,UAAAwV,OAAA,WAIA,IAHA,IAAAkI,EAAA5d,KAAA4d,KAAAH,KACAlb,EAAAvC,KAAAsK,YAAArE,MAAAjG,KAAAwG,KACAhE,EAAA,EACAob,GACAA,EAAAriB,GAAAqiB,EAAAtb,IAAAC,EAAAC,GACAA,GAAAob,EAAApX,IACAoX,EAAAA,EAAAH,KAGA,OAAAlb,GAGA0M,EAAAnB,EAAA,SAAAwQ,GACApP,EAAAoP,+BCxcAjjB,EAAAC,QAAA4T,EAGA,IAAAD,EAAA7T,EAAA,KACA8T,EAAAhP,UAAAnB,OAAAsL,OAAA4E,EAAA/O,YAAAoK,YAAA4E,EAEA,IAAAnI,EAAA3L,EAAA,IAEAsY,EAAA3M,EAAA2M,OAQA,SAAAxE,IACAD,EAAA3I,KAAAtG,MAQAkP,EAAAjJ,MAAA,SAAAC,GACA,OAAAgJ,EAAAjJ,MAAAc,EAAAkV,GAAA/V,IAGA,IAAAqY,EAAA7K,GAAAA,EAAAxT,qBAAAyB,YAAA,QAAA+R,EAAAxT,UAAA6S,IAAA9K,KACA,SAAA3F,EAAAC,EAAAC,GACAD,EAAAwQ,IAAAzQ,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAkc,KACAlc,EAAAkc,KAAAjc,EAAAC,EAAA,EAAAF,EAAA1G,aACA,IAAA,IAAAkB,EAAA,EAAAA,EAAAwF,EAAA1G,QACA2G,EAAAC,KAAAF,EAAAxF,MAgBA,SAAA2hB,EAAAnc,EAAAC,EAAAC,GACAF,EAAA1G,OAAA,GACAmL,EAAAR,KAAAG,MAAApE,EAAAC,EAAAC,GAEAD,EAAAwZ,UAAAzZ,EAAAE,GAdA0M,EAAAhP,UAAA+I,MAAA,SAAAvJ,GACAqH,EAAAqE,SAAA1L,KACAA,EAAAqH,EAAAiV,EAAAtc,EAAA,WACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EAIA,OAHAoE,KAAA8T,OAAAtN,GACAA,GACAxG,KAAAme,EAAAI,EAAA/X,EAAA9G,GACAM,MAaAkP,EAAAhP,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAkN,EAAAgL,WAAAhf,GAIA,OAHAM,KAAA8T,OAAAtN,GACAA,GACAxG,KAAAme,EAAAM,EAAAjY,EAAA9G,GACAM,uBvCvEAhF,KAAAC,OAcAC,EAPA,SAAAyjB,EAAA1W,GACA,IAAA2W,EAAA5jB,EAAAiN,GAGA,OAFA2W,GACA7jB,EAAAkN,GAAA,GAAA3B,KAAAsY,EAAA5jB,EAAAiN,GAAA,CAAA3M,QAAA,IAAAqjB,EAAAC,EAAAA,EAAAtjB,SACAsjB,EAAAtjB,QAGAqjB,CAAA1jB,EAAA,IAGAC,EAAA6L,KAAAuU,OAAApgB,SAAAA,EAGA,mBAAAsW,QAAAA,OAAAqN,KACArN,OAAA,CAAA,QAAA,SAAArF,GAKA,OAJAA,GAAAA,EAAA2S,SACA5jB,EAAA6L,KAAAoF,KAAAA,EACAjR,EAAA4T,aAEA5T,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\r\n\r\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\r\n    // sources through a conflict-free require shim and is again wrapped within an iife that\r\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\r\n    // so that minification can remove the directives of each module.\r\n\r\n    function $require(name) {\r\n        var $module = cache[name];\r\n        if (!$module)\r\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\r\n        return $module.exports;\r\n    }\r\n\r\n    var protobuf = $require(entries[0]);\r\n\r\n    // Expose globally\r\n    protobuf.util.global.protobuf = protobuf;\r\n\r\n    // Be nice to AMD\r\n    if (typeof define === \"function\" && define.amd)\r\n        define([\"long\"], function(Long) {\r\n            if (Long && Long.isLong) {\r\n                protobuf.util.Long = Long;\r\n                protobuf.configure();\r\n            }\r\n            return protobuf;\r\n        });\r\n\r\n    // Be nice to CommonJS\r\n    if (typeof module === \"object\" && module && module.exports)\r\n        module.exports = protobuf;\r\n\r\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\r\n/**\r\n * Runtime message from/to plain object converters.\r\n * @namespace\r\n */\r\nvar converter = exports;\r\n\r\nvar Enum = require(14),\r\n    util = require(33);\r\n\r\n/**\r\n * Generates a partial value fromObject conveter.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} prop Property reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) { gen\r\n            (\"switch(d%s){\", prop);\r\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\r\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\r\n                (\"default:\");\r\n                gen\r\n                (\"case%j:\", keys[i])\r\n                (\"case %i:\", values[keys[i]])\r\n                    (\"m%s=%j\", prop, values[keys[i]])\r\n                    (\"break\");\r\n            } gen\r\n            (\"}\");\r\n        } else gen\r\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\r\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\r\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\r\n    } else {\r\n        var isUnsigned = false;\r\n        switch (field.type) {\r\n            case \"double\":\r\n            case \"float\": gen\r\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\r\n                break;\r\n            case \"uint32\":\r\n            case \"fixed32\": gen\r\n                (\"m%s=d%s>>>0\", prop, prop);\r\n                break;\r\n            case \"int32\":\r\n            case \"sint32\":\r\n            case \"sfixed32\": gen\r\n                (\"m%s=d%s|0\", prop, prop);\r\n                break;\r\n            case \"uint64\":\r\n                isUnsigned = true;\r\n                // eslint-disable-line no-fallthrough\r\n            case \"int64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n                (\"if(util.Long)\")\r\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\r\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\r\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\r\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\r\n                    (\"m%s=d%s\", prop, prop)\r\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\r\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\r\n                break;\r\n            case \"bytes\": gen\r\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\r\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\r\n                (\"else if(d%s.length)\", prop)\r\n                    (\"m%s=d%s\", prop, prop);\r\n                break;\r\n            case \"string\": gen\r\n                (\"m%s=String(d%s)\", prop, prop);\r\n                break;\r\n            case \"bool\": gen\r\n                (\"m%s=Boolean(d%s)\", prop, prop);\r\n                break;\r\n            /* default: gen\r\n                (\"m%s=d%s\", prop, prop);\r\n                break; */\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}\r\n\r\n/**\r\n * Generates a plain object to runtime message converter specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nconverter.fromObject = function fromObject(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var fields = mtype.fieldsArray;\r\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\r\n    (\"if(d instanceof this.ctor)\")\r\n        (\"return d\");\r\n    if (!fields.length) return gen\r\n    (\"return new this.ctor\");\r\n    gen\r\n    (\"var m=new this.ctor\");\r\n    for (var i = 0; i < fields.length; ++i) {\r\n        var field  = fields[i].resolve(),\r\n            prop   = util.safeProp(field.name);\r\n\r\n        // Map fields\r\n        if (field.map) { gen\r\n    (\"if(d%s){\", prop)\r\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\r\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\r\n        (\"m%s={}\", prop)\r\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\r\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n        // Repeated fields\r\n        } else if (field.repeated) { gen\r\n    (\"if(d%s){\", prop)\r\n        (\"if(!Array.isArray(d%s))\", prop)\r\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\r\n        (\"m%s=[]\", prop)\r\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\r\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n        // Non-repeated fields\r\n        } else {\r\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\r\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\r\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\r\n            if (!(field.resolvedType instanceof Enum)) gen\r\n    (\"}\");\r\n        }\r\n    } return gen\r\n    (\"return m\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n};\r\n\r\n/**\r\n * Generates a partial value toObject converter.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} prop Property reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) gen\r\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\r\n        else gen\r\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\r\n    } else {\r\n        var isUnsigned = false;\r\n        switch (field.type) {\r\n            case \"double\":\r\n            case \"float\": gen\r\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\r\n                break;\r\n            case \"uint64\":\r\n                isUnsigned = true;\r\n                // eslint-disable-line no-fallthrough\r\n            case \"int64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\r\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\r\n            (\"else\") // Long-like\r\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\r\n                break;\r\n            case \"bytes\": gen\r\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\r\n                break;\r\n            default: gen\r\n            (\"d%s=m%s\", prop, prop);\r\n                break;\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}\r\n\r\n/**\r\n * Generates a runtime message to plain object converter specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nconverter.toObject = function toObject(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\r\n    if (!fields.length)\r\n        return util.codegen()(\"return {}\");\r\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\r\n    (\"if(!o)\")\r\n        (\"o={}\")\r\n    (\"var d={}\");\r\n\r\n    var repeatedFields = [],\r\n        mapFields = [],\r\n        normalFields = [],\r\n        i = 0;\r\n    for (; i < fields.length; ++i)\r\n        if (!fields[i].partOf)\r\n            ( fields[i].resolve().repeated ? repeatedFields\r\n            : fields[i].map ? mapFields\r\n            : normalFields).push(fields[i]);\r\n\r\n    if (repeatedFields.length) { gen\r\n    (\"if(o.arrays||o.defaults){\");\r\n        for (i = 0; i < repeatedFields.length; ++i) gen\r\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\r\n        gen\r\n    (\"}\");\r\n    }\r\n\r\n    if (mapFields.length) { gen\r\n    (\"if(o.objects||o.defaults){\");\r\n        for (i = 0; i < mapFields.length; ++i) gen\r\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\r\n        gen\r\n    (\"}\");\r\n    }\r\n\r\n    if (normalFields.length) { gen\r\n    (\"if(o.defaults){\");\r\n        for (i = 0; i < normalFields.length; ++i) {\r\n            var field = normalFields[i],\r\n                prop  = util.safeProp(field.name);\r\n            if (field.resolvedType instanceof Enum) gen\r\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\r\n            else if (field.long) gen\r\n        (\"if(util.Long){\")\r\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\r\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\r\n        (\"}else\")\r\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\r\n            else if (field.bytes) {\r\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\r\n                gen\r\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\r\n        (\"else{\")\r\n            (\"d%s=%s\", prop, arrayDefault)\r\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\r\n        (\"}\");\r\n            } else gen\r\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\r\n        } gen\r\n    (\"}\");\r\n    }\r\n    var hasKs2 = false;\r\n    for (i = 0; i < fields.length; ++i) {\r\n        var field = fields[i],\r\n            index = mtype._fieldsArray.indexOf(field),\r\n            prop  = util.safeProp(field.name);\r\n        if (field.map) {\r\n            if (!hasKs2) { hasKs2 = true; gen\r\n    (\"var ks2\");\r\n            } gen\r\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\r\n        (\"d%s={}\", prop)\r\n        (\"for(var j=0;j<ks2.length;++j){\");\r\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\r\n        (\"}\");\r\n        } else if (field.repeated) { gen\r\n    (\"if(m%s&&m%s.length){\", prop, prop)\r\n        (\"d%s=[]\", prop)\r\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\r\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\r\n        (\"}\");\r\n        } else { gen\r\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\r\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\r\n        if (field.partOf) gen\r\n        (\"if(o.oneofs)\")\r\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\r\n        }\r\n        gen\r\n    (\"}\");\r\n    }\r\n    return gen\r\n    (\"return d\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n};\r\n", "\"use strict\";\r\nmodule.exports = decoder;\r\n\r\nvar Enum    = require(14),\r\n    types   = require(32),\r\n    util    = require(33);\r\n\r\nfunction missing(field) {\r\n    return \"missing required '\" + field.name + \"'\";\r\n}\r\n\r\n/**\r\n * Generates a decoder specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction decoder(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\r\n    (\"if(!(r instanceof Reader))\")\r\n        (\"r=Reader.create(r)\")\r\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k\" : \"\"))\r\n    (\"while(r.pos<c){\")\r\n        (\"var t=r.uint32()\");\r\n    if (mtype.group) gen\r\n        (\"if((t&7)===4)\")\r\n            (\"break\");\r\n    gen\r\n        (\"switch(t>>>3){\");\r\n\r\n    var i = 0;\r\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\r\n        var field = mtype._fieldsArray[i].resolve(),\r\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\r\n            ref   = \"m\" + util.safeProp(field.name); gen\r\n            (\"case %i:\", field.id);\r\n\r\n        // Map fields\r\n        if (field.map) { gen\r\n                (\"r.skip().pos++\") // assumes id 1 + key wireType\r\n                (\"if(%s===util.emptyObject)\", ref)\r\n                    (\"%s={}\", ref)\r\n                (\"k=r.%s()\", field.keyType)\r\n                (\"r.pos++\"); // assumes id 2 + value wireType\r\n            if (types.long[field.keyType] !== undefined) {\r\n                if (types.basic[type] === undefined) gen\r\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\r\n                else gen\r\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=r.%s()\", ref, type);\r\n            } else {\r\n                if (types.basic[type] === undefined) gen\r\n                (\"%s[k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\r\n                else gen\r\n                (\"%s[k]=r.%s()\", ref, type);\r\n            }\r\n\r\n        // Repeated fields\r\n        } else if (field.repeated) { gen\r\n\r\n                (\"if(!(%s&&%s.length))\", ref, ref)\r\n                    (\"%s=[]\", ref);\r\n\r\n            // Packable (always check for forward and backward compatiblity)\r\n            if (types.packed[type] !== undefined) gen\r\n                (\"if((t&7)===2){\")\r\n                    (\"var c2=r.uint32()+r.pos\")\r\n                    (\"while(r.pos<c2)\")\r\n                        (\"%s.push(r.%s())\", ref, type)\r\n                (\"}else\");\r\n\r\n            // Non-packed\r\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\r\n                    ? \"%s.push(types[%i].decode(r))\"\r\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\r\n            else gen\r\n                    (\"%s.push(r.%s())\", ref, type);\r\n\r\n        // Non-repeated\r\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\r\n                ? \"%s=types[%i].decode(r)\"\r\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\r\n        else gen\r\n                (\"%s=r.%s()\", ref, type);\r\n        gen\r\n                (\"break\");\r\n    // Unknown fields\r\n    } gen\r\n            (\"default:\")\r\n                (\"r.skipType(t&7)\")\r\n                (\"break\")\r\n\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n    // Field presence\r\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\r\n        var rfield = mtype._fieldsArray[i];\r\n        if (rfield.required) gen\r\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\r\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\r\n    }\r\n\r\n    return gen\r\n    (\"return m\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n", "\"use strict\";\r\nmodule.exports = encoder;\r\n\r\nvar Enum     = require(14),\r\n    types    = require(32),\r\n    util     = require(33);\r\n\r\n/**\r\n * Generates a partial message type encoder.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genTypePartial(gen, field, fieldIndex, ref) {\r\n    return field.resolvedType.group\r\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\r\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\r\n}\r\n\r\n/**\r\n * Generates an encoder specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction encoder(mtype) {\r\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\r\n    (\"if(!w)\")\r\n        (\"w=Writer.create()\");\r\n\r\n    var i, ref;\r\n\r\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\r\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\r\n\r\n    for (var i = 0; i < fields.length; ++i) {\r\n        var field    = fields[i].resolve(),\r\n            index    = mtype._fieldsArray.indexOf(field),\r\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\r\n            wireType = types.basic[type];\r\n            ref      = \"m\" + util.safeProp(field.name);\r\n\r\n        // Map fields\r\n        if (field.map) {\r\n            gen\r\n    (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name) // !== undefined && !== null\r\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\r\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\r\n            if (wireType === undefined) gen\r\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\r\n            else gen\r\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\r\n            gen\r\n        (\"}\")\r\n    (\"}\");\r\n\r\n            // Repeated fields\r\n        } else if (field.repeated) { gen\r\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\r\n\r\n            // Packed repeated\r\n            if (field.packed && types.packed[type] !== undefined) { gen\r\n\r\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\r\n        (\"for(var i=0;i<%s.length;++i)\", ref)\r\n            (\"w.%s(%s[i])\", type, ref)\r\n        (\"w.ldelim()\");\r\n\r\n            // Non-packed\r\n            } else { gen\r\n\r\n        (\"for(var i=0;i<%s.length;++i)\", ref);\r\n                if (wireType === undefined)\r\n            genTypePartial(gen, field, index, ref + \"[i]\");\r\n                else gen\r\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\r\n\r\n            } gen\r\n    (\"}\");\r\n\r\n        // Non-repeated\r\n        } else {\r\n            if (field.optional) gen\r\n    (\"if(%s!=null&&m.hasOwnProperty(%j))\", ref, field.name); // !== undefined && !== null\r\n\r\n            if (wireType === undefined)\r\n        genTypePartial(gen, field, index, ref);\r\n            else gen\r\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\r\n\r\n        }\r\n    }\r\n\r\n    return gen\r\n    (\"return w\");\r\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\r\n}", "\"use strict\";\r\nmodule.exports = Enum;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(22);\r\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\r\n\r\nvar Namespace = require(21),\r\n    util = require(33);\r\n\r\n/**\r\n * Constructs a new enum instance.\r\n * @classdesc Reflected enum.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {Object.<string,number>} [values] Enum values as an object, by name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] The comment for this enum\r\n * @param {Object.<string,string>} [comments] The value comments for this enum\r\n */\r\nfunction Enum(name, values, options, comment, comments) {\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    if (values && typeof values !== \"object\")\r\n        throw TypeError(\"values must be an object\");\r\n\r\n    /**\r\n     * Enum values by id.\r\n     * @type {Object.<number,string>}\r\n     */\r\n    this.valuesById = {};\r\n\r\n    /**\r\n     * Enum values by name.\r\n     * @type {Object.<string,number>}\r\n     */\r\n    this.values = Object.create(this.valuesById); // toJSON, marker\r\n\r\n    /**\r\n     * Enum comment text.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n\r\n    /**\r\n     * Value comment texts, if any.\r\n     * @type {Object.<string,string>}\r\n     */\r\n    this.comments = comments || {};\r\n\r\n    /**\r\n     * Reserved ranges, if any.\r\n     * @type {Array.<number[]|string>}\r\n     */\r\n    this.reserved = undefined; // toJSON\r\n\r\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\r\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\r\n    // static and reflection code alike instead of emitting generic object definitions.\r\n\r\n    if (values)\r\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\r\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\r\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\r\n}\r\n\r\n/**\r\n * Enum descriptor.\r\n * @interface IEnum\r\n * @property {Object.<string,number>} values Enum values\r\n * @property {Object.<string,*>} [options] Enum options\r\n */\r\n\r\n/**\r\n * Constructs an enum from an enum descriptor.\r\n * @param {string} name Enum name\r\n * @param {IEnum} json Enum descriptor\r\n * @returns {Enum} Created enum\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nEnum.fromJSON = function fromJSON(name, json) {\r\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\r\n    enm.reserved = json.reserved;\r\n    return enm;\r\n};\r\n\r\n/**\r\n * Converts this enum to an enum descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IEnum} Enum descriptor\r\n */\r\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\"  , this.options,\r\n        \"values\"   , this.values,\r\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\r\n        \"comment\"  , keepComments ? this.comment : undefined,\r\n        \"comments\" , keepComments ? this.comments : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds a value to this enum.\r\n * @param {string} name Value name\r\n * @param {number} id Value id\r\n * @param {string} [comment] Comment, if any\r\n * @returns {Enum} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a value with this name or id\r\n */\r\nEnum.prototype.add = function add(name, id, comment) {\r\n    // utilized by the parser but not by .fromJSON\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    if (!util.isInteger(id))\r\n        throw TypeError(\"id must be an integer\");\r\n\r\n    if (this.values[name] !== undefined)\r\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\r\n\r\n    if (this.isReservedId(id))\r\n        throw Error(\"id \" + id + \" is reserved in \" + this);\r\n\r\n    if (this.isReservedName(name))\r\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\r\n\r\n    if (this.valuesById[id] !== undefined) {\r\n        if (!(this.options && this.options.allow_alias))\r\n            throw Error(\"duplicate id \" + id + \" in \" + this);\r\n        this.values[name] = id;\r\n    } else\r\n        this.valuesById[this.values[name] = id] = name;\r\n\r\n    this.comments[name] = comment || null;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes a value from this enum\r\n * @param {string} name Value name\r\n * @returns {Enum} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `name` is not a name of this enum\r\n */\r\nEnum.prototype.remove = function remove(name) {\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    var val = this.values[name];\r\n    if (val == null)\r\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\r\n\r\n    delete this.valuesById[val];\r\n    delete this.values[name];\r\n    delete this.comments[name];\r\n\r\n    return this;\r\n};\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nEnum.prototype.isReservedId = function isReservedId(id) {\r\n    return Namespace.isReservedId(this.reserved, id);\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nEnum.prototype.isReservedName = function isReservedName(name) {\r\n    return Namespace.isReservedName(this.reserved, name);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Field;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(22);\r\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\r\n\r\nvar Enum  = require(14),\r\n    types = require(32),\r\n    util  = require(33);\r\n\r\nvar Type; // cyclic\r\n\r\nvar ruleRe = /^required|optional|repeated$/;\r\n\r\n/**\r\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\r\n * @name Field\r\n * @classdesc Reflected message field.\r\n * @extends FieldBase\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} type Value type\r\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\r\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\n\r\n/**\r\n * Constructs a field from a field descriptor.\r\n * @param {string} name Field name\r\n * @param {IField} json Field descriptor\r\n * @returns {Field} Created field\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nField.fromJSON = function fromJSON(name, json) {\r\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Not an actual constructor. Use {@link Field} instead.\r\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\r\n * @exports FieldBase\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} type Value type\r\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\r\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction Field(name, id, type, rule, extend, options, comment) {\r\n\r\n    if (util.isObject(rule)) {\r\n        comment = extend;\r\n        options = rule;\r\n        rule = extend = undefined;\r\n    } else if (util.isObject(extend)) {\r\n        comment = options;\r\n        options = extend;\r\n        extend = undefined;\r\n    }\r\n\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    if (!util.isInteger(id) || id < 0)\r\n        throw TypeError(\"id must be a non-negative integer\");\r\n\r\n    if (!util.isString(type))\r\n        throw TypeError(\"type must be a string\");\r\n\r\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\r\n        throw TypeError(\"rule must be a string rule\");\r\n\r\n    if (extend !== undefined && !util.isString(extend))\r\n        throw TypeError(\"extend must be a string\");\r\n\r\n    /**\r\n     * Field rule, if any.\r\n     * @type {string|undefined}\r\n     */\r\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\r\n\r\n    /**\r\n     * Field type.\r\n     * @type {string}\r\n     */\r\n    this.type = type; // toJSON\r\n\r\n    /**\r\n     * Unique field id.\r\n     * @type {number}\r\n     */\r\n    this.id = id; // toJSON, marker\r\n\r\n    /**\r\n     * Extended type if different from parent.\r\n     * @type {string|undefined}\r\n     */\r\n    this.extend = extend || undefined; // toJSON\r\n\r\n    /**\r\n     * Whether this field is required.\r\n     * @type {boolean}\r\n     */\r\n    this.required = rule === \"required\";\r\n\r\n    /**\r\n     * Whether this field is optional.\r\n     * @type {boolean}\r\n     */\r\n    this.optional = !this.required;\r\n\r\n    /**\r\n     * Whether this field is repeated.\r\n     * @type {boolean}\r\n     */\r\n    this.repeated = rule === \"repeated\";\r\n\r\n    /**\r\n     * Whether this field is a map or not.\r\n     * @type {boolean}\r\n     */\r\n    this.map = false;\r\n\r\n    /**\r\n     * Message this field belongs to.\r\n     * @type {Type|null}\r\n     */\r\n    this.message = null;\r\n\r\n    /**\r\n     * OneOf this field belongs to, if any,\r\n     * @type {OneOf|null}\r\n     */\r\n    this.partOf = null;\r\n\r\n    /**\r\n     * The field type's default value.\r\n     * @type {*}\r\n     */\r\n    this.typeDefault = null;\r\n\r\n    /**\r\n     * The field's default value on prototypes.\r\n     * @type {*}\r\n     */\r\n    this.defaultValue = null;\r\n\r\n    /**\r\n     * Whether this field's value should be treated as a long.\r\n     * @type {boolean}\r\n     */\r\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\r\n\r\n    /**\r\n     * Whether this field's value is a buffer.\r\n     * @type {boolean}\r\n     */\r\n    this.bytes = type === \"bytes\";\r\n\r\n    /**\r\n     * Resolved type if not a basic type.\r\n     * @type {Type|Enum|null}\r\n     */\r\n    this.resolvedType = null;\r\n\r\n    /**\r\n     * Sister-field within the extended type if a declaring extension field.\r\n     * @type {Field|null}\r\n     */\r\n    this.extensionField = null;\r\n\r\n    /**\r\n     * Sister-field within the declaring namespace if an extended field.\r\n     * @type {Field|null}\r\n     */\r\n    this.declaringField = null;\r\n\r\n    /**\r\n     * Internally remembers whether this field is packed.\r\n     * @type {boolean|null}\r\n     * @private\r\n     */\r\n    this._packed = null;\r\n\r\n    /**\r\n     * Comment for this field.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\r\n * @name Field#packed\r\n * @type {boolean}\r\n * @readonly\r\n */\r\nObject.defineProperty(Field.prototype, \"packed\", {\r\n    get: function() {\r\n        // defaults to packed=true if not explicity set to false\r\n        if (this._packed === null)\r\n            this._packed = this.getOption(\"packed\") !== false;\r\n        return this._packed;\r\n    }\r\n});\r\n\r\n/**\r\n * @override\r\n */\r\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\r\n    if (name === \"packed\") // clear cached before setting\r\n        this._packed = null;\r\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\r\n};\r\n\r\n/**\r\n * Field descriptor.\r\n * @interface IField\r\n * @property {string} [rule=\"optional\"] Field rule\r\n * @property {string} type Field type\r\n * @property {number} id Field id\r\n * @property {Object.<string,*>} [options] Field options\r\n */\r\n\r\n/**\r\n * Extension field descriptor.\r\n * @interface IExtensionField\r\n * @extends IField\r\n * @property {string} extend Extended type\r\n */\r\n\r\n/**\r\n * Converts this field to a field descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IField} Field descriptor\r\n */\r\nField.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\r\n        \"type\"    , this.type,\r\n        \"id\"      , this.id,\r\n        \"extend\"  , this.extend,\r\n        \"options\" , this.options,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Resolves this field's type references.\r\n * @returns {Field} `this`\r\n * @throws {Error} If any reference cannot be resolved\r\n */\r\nField.prototype.resolve = function resolve() {\r\n\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\r\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\r\n        if (this.resolvedType instanceof Type)\r\n            this.typeDefault = null;\r\n        else // instanceof Enum\r\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\r\n    }\r\n\r\n    // use explicitly set default value if present\r\n    if (this.options && this.options[\"default\"] != null) {\r\n        this.typeDefault = this.options[\"default\"];\r\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\r\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\r\n    }\r\n\r\n    // remove unnecessary options\r\n    if (this.options) {\r\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\r\n            delete this.options.packed;\r\n        if (!Object.keys(this.options).length)\r\n            this.options = undefined;\r\n    }\r\n\r\n    // convert to internal data type if necesssary\r\n    if (this.long) {\r\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\r\n\r\n        /* istanbul ignore else */\r\n        if (Object.freeze)\r\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\r\n\r\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\r\n        var buf;\r\n        if (util.base64.test(this.typeDefault))\r\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\r\n        else\r\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\r\n        this.typeDefault = buf;\r\n    }\r\n\r\n    // take special care of maps and repeated fields\r\n    if (this.map)\r\n        this.defaultValue = util.emptyObject;\r\n    else if (this.repeated)\r\n        this.defaultValue = util.emptyArray;\r\n    else\r\n        this.defaultValue = this.typeDefault;\r\n\r\n    // ensure proper value on prototype\r\n    if (this.parent instanceof Type)\r\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\r\n\r\n    return ReflectionObject.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\r\n * @typedef FieldDecorator\r\n * @type {function}\r\n * @param {Object} prototype Target prototype\r\n * @param {string} fieldName Field name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Field decorator (TypeScript).\r\n * @name Field.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\r\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\r\n * @param {T} [defaultValue] Default value\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\r\n */\r\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\r\n\r\n    // submessage: decorate the submessage and use its name as the type\r\n    if (typeof fieldType === \"function\")\r\n        fieldType = util.decorateType(fieldType).name;\r\n\r\n    // enum reference: create a reflected copy of the enum and keep reuseing it\r\n    else if (fieldType && typeof fieldType === \"object\")\r\n        fieldType = util.decorateEnum(fieldType).name;\r\n\r\n    return function fieldDecorator(prototype, fieldName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\r\n    };\r\n};\r\n\r\n/**\r\n * Field decorator (TypeScript).\r\n * @name Field.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {Constructor<T>|string} fieldType Field type\r\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends Message<T>\r\n * @variation 2\r\n */\r\n// like Field.d but without a default value\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nField._configure = function configure(Type_) {\r\n    Type = Type_;\r\n};\r\n", "\"use strict\";\r\nvar protobuf = module.exports = require(17);\r\n\r\nprotobuf.build = \"light\";\r\n\r\n/**\r\n * A node-style callback as used by {@link load} and {@link Root#load}.\r\n * @typedef LoadCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any, otherwise `null`\r\n * @param {Root} [root] Root, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @see {@link Root#load}\r\n */\r\nfunction load(filename, root, callback) {\r\n    if (typeof root === \"function\") {\r\n        callback = root;\r\n        root = new protobuf.Root();\r\n    } else if (!root)\r\n        root = new protobuf.Root();\r\n    return root.load(filename, callback);\r\n}\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\r\n * @name load\r\n * @function\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @see {@link Root#load}\r\n * @variation 2\r\n */\r\n// function load(filename:string, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\r\n * @name load\r\n * @function\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\r\n * @returns {Promise<Root>} Promise\r\n * @see {@link Root#load}\r\n * @variation 3\r\n */\r\n// function load(filename:string, [root:Root]):Promise<Root>\r\n\r\nprotobuf.load = load;\r\n\r\n/**\r\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\r\n * @param {string|string[]} filename One or multiple files to load\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\r\n * @returns {Root} Root namespace\r\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\r\n * @see {@link Root#loadSync}\r\n */\r\nfunction loadSync(filename, root) {\r\n    if (!root)\r\n        root = new protobuf.Root();\r\n    return root.loadSync(filename);\r\n}\r\n\r\nprotobuf.loadSync = loadSync;\r\n\r\n// Serialization\r\nprotobuf.encoder          = require(13);\r\nprotobuf.decoder          = require(12);\r\nprotobuf.verifier         = require(36);\r\nprotobuf.converter        = require(11);\r\n\r\n// Reflection\r\nprotobuf.ReflectionObject = require(22);\r\nprotobuf.Namespace        = require(21);\r\nprotobuf.Root             = require(26);\r\nprotobuf.Enum             = require(14);\r\nprotobuf.Type             = require(31);\r\nprotobuf.Field            = require(15);\r\nprotobuf.OneOf            = require(23);\r\nprotobuf.MapField         = require(18);\r\nprotobuf.Service          = require(30);\r\nprotobuf.Method           = require(20);\r\n\r\n// Runtime\r\nprotobuf.Message          = require(19);\r\nprotobuf.wrappers         = require(37);\r\n\r\n// Utility\r\nprotobuf.types            = require(32);\r\nprotobuf.util             = require(33);\r\n\r\n// Set up possibly cyclic reflection dependencies\r\nprotobuf.ReflectionObject._configure(protobuf.Root);\r\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\r\nprotobuf.Root._configure(protobuf.Type);\r\nprotobuf.Field._configure(protobuf.Type);\r\n", "\"use strict\";\r\nvar protobuf = exports;\r\n\r\n/**\r\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\r\n * @name build\r\n * @type {string}\r\n * @const\r\n */\r\nprotobuf.build = \"minimal\";\r\n\r\n// Serialization\r\nprotobuf.Writer       = require(38);\r\nprotobuf.BufferWriter = require(39);\r\nprotobuf.Reader       = require(24);\r\nprotobuf.BufferReader = require(25);\r\n\r\n// Utility\r\nprotobuf.util         = require(35);\r\nprotobuf.rpc          = require(28);\r\nprotobuf.roots        = require(27);\r\nprotobuf.configure    = configure;\r\n\r\n/* istanbul ignore next */\r\n/**\r\n * Reconfigures the library according to the environment.\r\n * @returns {undefined}\r\n */\r\nfunction configure() {\r\n    protobuf.Reader._configure(protobuf.BufferReader);\r\n    protobuf.util._configure();\r\n}\r\n\r\n// Set up buffer utility according to the environment\r\nprotobuf.Writer._configure(protobuf.BufferWriter);\r\nconfigure();\r\n", "\"use strict\";\r\nmodule.exports = MapField;\r\n\r\n// extends Field\r\nvar Field = require(15);\r\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\r\n\r\nvar types   = require(32),\r\n    util    = require(33);\r\n\r\n/**\r\n * Constructs a new map field instance.\r\n * @classdesc Reflected map field.\r\n * @extends FieldBase\r\n * @constructor\r\n * @param {string} name Unique name within its namespace\r\n * @param {number} id Unique id within its namespace\r\n * @param {string} keyType Key type\r\n * @param {string} type Value type\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction MapField(name, id, keyType, type, options, comment) {\r\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(keyType))\r\n        throw TypeError(\"keyType must be a string\");\r\n\r\n    /**\r\n     * Key type.\r\n     * @type {string}\r\n     */\r\n    this.keyType = keyType; // toJSON, marker\r\n\r\n    /**\r\n     * Resolved key type if not a basic type.\r\n     * @type {ReflectionObject|null}\r\n     */\r\n    this.resolvedKeyType = null;\r\n\r\n    // Overrides Field#map\r\n    this.map = true;\r\n}\r\n\r\n/**\r\n * Map field descriptor.\r\n * @interface IMapField\r\n * @extends {IField}\r\n * @property {string} keyType Key type\r\n */\r\n\r\n/**\r\n * Extension map field descriptor.\r\n * @interface IExtensionMapField\r\n * @extends IMapField\r\n * @property {string} extend Extended type\r\n */\r\n\r\n/**\r\n * Constructs a map field from a map field descriptor.\r\n * @param {string} name Field name\r\n * @param {IMapField} json Map field descriptor\r\n * @returns {MapField} Created map field\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nMapField.fromJSON = function fromJSON(name, json) {\r\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this map field to a map field descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IMapField} Map field descriptor\r\n */\r\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"keyType\" , this.keyType,\r\n        \"type\"    , this.type,\r\n        \"id\"      , this.id,\r\n        \"extend\"  , this.extend,\r\n        \"options\" , this.options,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nMapField.prototype.resolve = function resolve() {\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\r\n    if (types.mapKey[this.keyType] === undefined)\r\n        throw Error(\"invalid key type: \" + this.keyType);\r\n\r\n    return Field.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * Map field decorator (TypeScript).\r\n * @name MapField.d\r\n * @function\r\n * @param {number} fieldId Field id\r\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\r\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\r\n * @returns {FieldDecorator} Decorator function\r\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\r\n */\r\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\r\n\r\n    // submessage value: decorate the submessage and use its name as the type\r\n    if (typeof fieldValueType === \"function\")\r\n        fieldValueType = util.decorateType(fieldValueType).name;\r\n\r\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\r\n    else if (fieldValueType && typeof fieldValueType === \"object\")\r\n        fieldValueType = util.decorateEnum(fieldValueType).name;\r\n\r\n    return function mapFieldDecorator(prototype, fieldName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\r\n    };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Message;\r\n\r\nvar util = require(35);\r\n\r\n/**\r\n * Constructs a new message instance.\r\n * @classdesc Abstract runtime message.\r\n * @constructor\r\n * @param {Properties<T>} [properties] Properties to set\r\n * @template T extends object = object\r\n */\r\nfunction Message(properties) {\r\n    // not used internally\r\n    if (properties)\r\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\r\n            this[keys[i]] = properties[keys[i]];\r\n}\r\n\r\n/**\r\n * Reference to the reflected type.\r\n * @name Message.$type\r\n * @type {Type}\r\n * @readonly\r\n */\r\n\r\n/**\r\n * Reference to the reflected type.\r\n * @name Message#$type\r\n * @type {Type}\r\n * @readonly\r\n */\r\n\r\n/*eslint-disable valid-jsdoc*/\r\n\r\n/**\r\n * Creates a new message of this type using the specified properties.\r\n * @param {Object.<string,*>} [properties] Properties to set\r\n * @returns {Message<T>} Message instance\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.create = function create(properties) {\r\n    return this.$type.create(properties);\r\n};\r\n\r\n/**\r\n * Encodes a message of this type.\r\n * @param {T|Object.<string,*>} message Message to encode\r\n * @param {Writer} [writer] Writer to use\r\n * @returns {Writer} Writer\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.encode = function encode(message, writer) {\r\n    return this.$type.encode(message, writer);\r\n};\r\n\r\n/**\r\n * Encodes a message of this type preceeded by its length as a varint.\r\n * @param {T|Object.<string,*>} message Message to encode\r\n * @param {Writer} [writer] Writer to use\r\n * @returns {Writer} Writer\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\r\n    return this.$type.encodeDelimited(message, writer);\r\n};\r\n\r\n/**\r\n * Decodes a message of this type.\r\n * @name Message.decode\r\n * @function\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\r\n * @returns {T} Decoded message\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.decode = function decode(reader) {\r\n    return this.$type.decode(reader);\r\n};\r\n\r\n/**\r\n * Decodes a message of this type preceeded by its length as a varint.\r\n * @name Message.decodeDelimited\r\n * @function\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\r\n * @returns {T} Decoded message\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.decodeDelimited = function decodeDelimited(reader) {\r\n    return this.$type.decodeDelimited(reader);\r\n};\r\n\r\n/**\r\n * Verifies a message of this type.\r\n * @name Message.verify\r\n * @function\r\n * @param {Object.<string,*>} message Plain object to verify\r\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\r\n */\r\nMessage.verify = function verify(message) {\r\n    return this.$type.verify(message);\r\n};\r\n\r\n/**\r\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\r\n * @param {Object.<string,*>} object Plain object\r\n * @returns {T} Message instance\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.fromObject = function fromObject(object) {\r\n    return this.$type.fromObject(object);\r\n};\r\n\r\n/**\r\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\r\n * @param {T} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n * @template T extends Message<T>\r\n * @this Constructor<T>\r\n */\r\nMessage.toObject = function toObject(message, options) {\r\n    return this.$type.toObject(message, options);\r\n};\r\n\r\n/**\r\n * Converts this message to JSON.\r\n * @returns {Object.<string,*>} JSON object\r\n */\r\nMessage.prototype.toJSON = function toJSON() {\r\n    return this.$type.toObject(this, util.toJSONOptions);\r\n};\r\n\r\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\r\nmodule.exports = Method;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(22);\r\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\r\n\r\nvar util = require(33);\r\n\r\n/**\r\n * Constructs a new service method instance.\r\n * @classdesc Reflected service method.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Method name\r\n * @param {string|undefined} type Method type, usually `\"rpc\"`\r\n * @param {string} requestType Request message type\r\n * @param {string} responseType Response message type\r\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\r\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] The comment for this method\r\n */\r\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment) {\r\n\r\n    /* istanbul ignore next */\r\n    if (util.isObject(requestStream)) {\r\n        options = requestStream;\r\n        requestStream = responseStream = undefined;\r\n    } else if (util.isObject(responseStream)) {\r\n        options = responseStream;\r\n        responseStream = undefined;\r\n    }\r\n\r\n    /* istanbul ignore if */\r\n    if (!(type === undefined || util.isString(type)))\r\n        throw TypeError(\"type must be a string\");\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(requestType))\r\n        throw TypeError(\"requestType must be a string\");\r\n\r\n    /* istanbul ignore if */\r\n    if (!util.isString(responseType))\r\n        throw TypeError(\"responseType must be a string\");\r\n\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /**\r\n     * Method type.\r\n     * @type {string}\r\n     */\r\n    this.type = type || \"rpc\"; // toJSON\r\n\r\n    /**\r\n     * Request type.\r\n     * @type {string}\r\n     */\r\n    this.requestType = requestType; // toJSON, marker\r\n\r\n    /**\r\n     * Whether requests are streamed or not.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.requestStream = requestStream ? true : undefined; // toJSON\r\n\r\n    /**\r\n     * Response type.\r\n     * @type {string}\r\n     */\r\n    this.responseType = responseType; // toJSON\r\n\r\n    /**\r\n     * Whether responses are streamed or not.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.responseStream = responseStream ? true : undefined; // toJSON\r\n\r\n    /**\r\n     * Resolved request type.\r\n     * @type {Type|null}\r\n     */\r\n    this.resolvedRequestType = null;\r\n\r\n    /**\r\n     * Resolved response type.\r\n     * @type {Type|null}\r\n     */\r\n    this.resolvedResponseType = null;\r\n\r\n    /**\r\n     * Comment for this method\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Method descriptor.\r\n * @interface IMethod\r\n * @property {string} [type=\"rpc\"] Method type\r\n * @property {string} requestType Request type\r\n * @property {string} responseType Response type\r\n * @property {boolean} [requestStream=false] Whether requests are streamed\r\n * @property {boolean} [responseStream=false] Whether responses are streamed\r\n * @property {Object.<string,*>} [options] Method options\r\n */\r\n\r\n/**\r\n * Constructs a method from a method descriptor.\r\n * @param {string} name Method name\r\n * @param {IMethod} json Method descriptor\r\n * @returns {Method} Created method\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nMethod.fromJSON = function fromJSON(name, json) {\r\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this method to a method descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IMethod} Method descriptor\r\n */\r\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\r\n        \"requestType\"    , this.requestType,\r\n        \"requestStream\"  , this.requestStream,\r\n        \"responseType\"   , this.responseType,\r\n        \"responseStream\" , this.responseStream,\r\n        \"options\"        , this.options,\r\n        \"comment\"        , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nMethod.prototype.resolve = function resolve() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.resolved)\r\n        return this;\r\n\r\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\r\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\r\n\r\n    return ReflectionObject.prototype.resolve.call(this);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Namespace;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(22);\r\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\r\n\r\nvar Field    = require(15),\r\n    util     = require(33);\r\n\r\nvar Type,    // cyclic\r\n    Service,\r\n    Enum;\r\n\r\n/**\r\n * Constructs a new namespace instance.\r\n * @name Namespace\r\n * @classdesc Reflected namespace.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\n\r\n/**\r\n * Constructs a namespace from JSON.\r\n * @memberof Namespace\r\n * @function\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} json JSON object\r\n * @returns {Namespace} Created namespace\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nNamespace.fromJSON = function fromJSON(name, json) {\r\n    return new Namespace(name, json.options).addJSON(json.nested);\r\n};\r\n\r\n/**\r\n * Converts an array of reflection objects to JSON.\r\n * @memberof Namespace\r\n * @param {ReflectionObject[]} array Object array\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\r\n */\r\nfunction arrayToJSON(array, toJSONOptions) {\r\n    if (!(array && array.length))\r\n        return undefined;\r\n    var obj = {};\r\n    for (var i = 0; i < array.length; ++i)\r\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\r\n    return obj;\r\n}\r\n\r\nNamespace.arrayToJSON = arrayToJSON;\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nNamespace.isReservedId = function isReservedId(reserved, id) {\r\n    if (reserved)\r\n        for (var i = 0; i < reserved.length; ++i)\r\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] >= id)\r\n                return true;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nNamespace.isReservedName = function isReservedName(reserved, name) {\r\n    if (reserved)\r\n        for (var i = 0; i < reserved.length; ++i)\r\n            if (reserved[i] === name)\r\n                return true;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Not an actual constructor. Use {@link Namespace} instead.\r\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\r\n * @exports NamespaceBase\r\n * @extends ReflectionObject\r\n * @abstract\r\n * @constructor\r\n * @param {string} name Namespace name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @see {@link Namespace}\r\n */\r\nfunction Namespace(name, options) {\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /**\r\n     * Nested objects by name.\r\n     * @type {Object.<string,ReflectionObject>|undefined}\r\n     */\r\n    this.nested = undefined; // toJSON\r\n\r\n    /**\r\n     * Cached nested objects as an array.\r\n     * @type {ReflectionObject[]|null}\r\n     * @private\r\n     */\r\n    this._nestedArray = null;\r\n}\r\n\r\nfunction clearCache(namespace) {\r\n    namespace._nestedArray = null;\r\n    return namespace;\r\n}\r\n\r\n/**\r\n * Nested objects of this namespace as an array for iteration.\r\n * @name NamespaceBase#nestedArray\r\n * @type {ReflectionObject[]}\r\n * @readonly\r\n */\r\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\r\n    get: function() {\r\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\r\n    }\r\n});\r\n\r\n/**\r\n * Namespace descriptor.\r\n * @interface INamespace\r\n * @property {Object.<string,*>} [options] Namespace options\r\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\r\n */\r\n\r\n/**\r\n * Any extension field descriptor.\r\n * @typedef AnyExtensionField\r\n * @type {IExtensionField|IExtensionMapField}\r\n */\r\n\r\n/**\r\n * Any nested object descriptor.\r\n * @typedef AnyNestedObject\r\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\r\n */\r\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\r\n\r\n/**\r\n * Converts this namespace to a namespace descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {INamespace} Namespace descriptor\r\n */\r\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    return util.toObject([\r\n        \"options\" , this.options,\r\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds nested objects to this namespace from nested object descriptors.\r\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\r\n * @returns {Namespace} `this`\r\n */\r\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\r\n    var ns = this;\r\n    /* istanbul ignore else */\r\n    if (nestedJson) {\r\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\r\n            nested = nestedJson[names[i]];\r\n            ns.add( // most to least likely\r\n                ( nested.fields !== undefined\r\n                ? Type.fromJSON\r\n                : nested.values !== undefined\r\n                ? Enum.fromJSON\r\n                : nested.methods !== undefined\r\n                ? Service.fromJSON\r\n                : nested.id !== undefined\r\n                ? Field.fromJSON\r\n                : Namespace.fromJSON )(names[i], nested)\r\n            );\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Gets the nested object of the specified name.\r\n * @param {string} name Nested object name\r\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\r\n */\r\nNamespace.prototype.get = function get(name) {\r\n    return this.nested && this.nested[name]\r\n        || null;\r\n};\r\n\r\n/**\r\n * Gets the values of the nested {@link Enum|enum} of the specified name.\r\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\r\n * @param {string} name Nested enum name\r\n * @returns {Object.<string,number>} Enum values\r\n * @throws {Error} If there is no such enum\r\n */\r\nNamespace.prototype.getEnum = function getEnum(name) {\r\n    if (this.nested && this.nested[name] instanceof Enum)\r\n        return this.nested[name].values;\r\n    throw Error(\"no such enum: \" + name);\r\n};\r\n\r\n/**\r\n * Adds a nested object to this namespace.\r\n * @param {ReflectionObject} object Nested object to add\r\n * @returns {Namespace} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a nested object with this name\r\n */\r\nNamespace.prototype.add = function add(object) {\r\n\r\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace))\r\n        throw TypeError(\"object must be a valid nested object\");\r\n\r\n    if (!this.nested)\r\n        this.nested = {};\r\n    else {\r\n        var prev = this.get(object.name);\r\n        if (prev) {\r\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\r\n                // replace plain namespace but keep existing nested elements and options\r\n                var nested = prev.nestedArray;\r\n                for (var i = 0; i < nested.length; ++i)\r\n                    object.add(nested[i]);\r\n                this.remove(prev);\r\n                if (!this.nested)\r\n                    this.nested = {};\r\n                object.setOptions(prev.options, true);\r\n\r\n            } else\r\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n        }\r\n    }\r\n    this.nested[object.name] = object;\r\n    object.onAdd(this);\r\n    return clearCache(this);\r\n};\r\n\r\n/**\r\n * Removes a nested object from this namespace.\r\n * @param {ReflectionObject} object Nested object to remove\r\n * @returns {Namespace} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `object` is not a member of this namespace\r\n */\r\nNamespace.prototype.remove = function remove(object) {\r\n\r\n    if (!(object instanceof ReflectionObject))\r\n        throw TypeError(\"object must be a ReflectionObject\");\r\n    if (object.parent !== this)\r\n        throw Error(object + \" is not a member of \" + this);\r\n\r\n    delete this.nested[object.name];\r\n    if (!Object.keys(this.nested).length)\r\n        this.nested = undefined;\r\n\r\n    object.onRemove(this);\r\n    return clearCache(this);\r\n};\r\n\r\n/**\r\n * Defines additial namespaces within this one if not yet existing.\r\n * @param {string|string[]} path Path to create\r\n * @param {*} [json] Nested types to create from JSON\r\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\r\n */\r\nNamespace.prototype.define = function define(path, json) {\r\n\r\n    if (util.isString(path))\r\n        path = path.split(\".\");\r\n    else if (!Array.isArray(path))\r\n        throw TypeError(\"illegal path\");\r\n    if (path && path.length && path[0] === \"\")\r\n        throw Error(\"path must be relative\");\r\n\r\n    var ptr = this;\r\n    while (path.length > 0) {\r\n        var part = path.shift();\r\n        if (ptr.nested && ptr.nested[part]) {\r\n            ptr = ptr.nested[part];\r\n            if (!(ptr instanceof Namespace))\r\n                throw Error(\"path conflicts with non-namespace objects\");\r\n        } else\r\n            ptr.add(ptr = new Namespace(part));\r\n    }\r\n    if (json)\r\n        ptr.addJSON(json);\r\n    return ptr;\r\n};\r\n\r\n/**\r\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\r\n * @returns {Namespace} `this`\r\n */\r\nNamespace.prototype.resolveAll = function resolveAll() {\r\n    var nested = this.nestedArray, i = 0;\r\n    while (i < nested.length)\r\n        if (nested[i] instanceof Namespace)\r\n            nested[i++].resolveAll();\r\n        else\r\n            nested[i++].resolve();\r\n    return this.resolve();\r\n};\r\n\r\n/**\r\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\r\n * @param {string|string[]} path Path to look up\r\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\r\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\r\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\r\n */\r\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\r\n\r\n    /* istanbul ignore next */\r\n    if (typeof filterTypes === \"boolean\") {\r\n        parentAlreadyChecked = filterTypes;\r\n        filterTypes = undefined;\r\n    } else if (filterTypes && !Array.isArray(filterTypes))\r\n        filterTypes = [ filterTypes ];\r\n\r\n    if (util.isString(path) && path.length) {\r\n        if (path === \".\")\r\n            return this.root;\r\n        path = path.split(\".\");\r\n    } else if (!path.length)\r\n        return this;\r\n\r\n    // Start at root if path is absolute\r\n    if (path[0] === \"\")\r\n        return this.root.lookup(path.slice(1), filterTypes);\r\n\r\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\r\n    var found = this.get(path[0]);\r\n    if (found) {\r\n        if (path.length === 1) {\r\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\r\n                return found;\r\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\r\n            return found;\r\n\r\n    // Otherwise try each nested namespace\r\n    } else\r\n        for (var i = 0; i < this.nestedArray.length; ++i)\r\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\r\n                return found;\r\n\r\n    // If there hasn't been a match, try again at the parent\r\n    if (this.parent === null || parentAlreadyChecked)\r\n        return null;\r\n    return this.parent.lookup(path, filterTypes);\r\n};\r\n\r\n/**\r\n * Looks up the reflection object at the specified path, relative to this namespace.\r\n * @name NamespaceBase#lookup\r\n * @function\r\n * @param {string|string[]} path Path to look up\r\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\r\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\r\n * @variation 2\r\n */\r\n// lookup(path: string, [parentAlreadyChecked: boolean])\r\n\r\n/**\r\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Type} Looked up type\r\n * @throws {Error} If `path` does not point to a type\r\n */\r\nNamespace.prototype.lookupType = function lookupType(path) {\r\n    var found = this.lookup(path, [ Type ]);\r\n    if (!found)\r\n        throw Error(\"no such type: \" + path);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Enum} Looked up enum\r\n * @throws {Error} If `path` does not point to an enum\r\n */\r\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\r\n    var found = this.lookup(path, [ Enum ]);\r\n    if (!found)\r\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Type} Looked up type or enum\r\n * @throws {Error} If `path` does not point to a type or enum\r\n */\r\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\r\n    var found = this.lookup(path, [ Type, Enum ]);\r\n    if (!found)\r\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n/**\r\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\r\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\r\n * @param {string|string[]} path Path to look up\r\n * @returns {Service} Looked up service\r\n * @throws {Error} If `path` does not point to a service\r\n */\r\nNamespace.prototype.lookupService = function lookupService(path) {\r\n    var found = this.lookup(path, [ Service ]);\r\n    if (!found)\r\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\r\n    return found;\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nNamespace._configure = function(Type_, Service_, Enum_) {\r\n    Type    = Type_;\r\n    Service = Service_;\r\n    Enum    = Enum_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = ReflectionObject;\r\n\r\nReflectionObject.className = \"ReflectionObject\";\r\n\r\nvar util = require(33);\r\n\r\nvar Root; // cyclic\r\n\r\n/**\r\n * Constructs a new reflection object instance.\r\n * @classdesc Base class of all reflection objects.\r\n * @constructor\r\n * @param {string} name Object name\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @abstract\r\n */\r\nfunction ReflectionObject(name, options) {\r\n\r\n    if (!util.isString(name))\r\n        throw TypeError(\"name must be a string\");\r\n\r\n    if (options && !util.isObject(options))\r\n        throw TypeError(\"options must be an object\");\r\n\r\n    /**\r\n     * Options.\r\n     * @type {Object.<string,*>|undefined}\r\n     */\r\n    this.options = options; // toJSON\r\n\r\n    /**\r\n     * Unique name within its namespace.\r\n     * @type {string}\r\n     */\r\n    this.name = name;\r\n\r\n    /**\r\n     * Parent namespace.\r\n     * @type {Namespace|null}\r\n     */\r\n    this.parent = null;\r\n\r\n    /**\r\n     * Whether already resolved or not.\r\n     * @type {boolean}\r\n     */\r\n    this.resolved = false;\r\n\r\n    /**\r\n     * Comment text, if any.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = null;\r\n\r\n    /**\r\n     * Defining file name.\r\n     * @type {string|null}\r\n     */\r\n    this.filename = null;\r\n}\r\n\r\nObject.defineProperties(ReflectionObject.prototype, {\r\n\r\n    /**\r\n     * Reference to the root namespace.\r\n     * @name ReflectionObject#root\r\n     * @type {Root}\r\n     * @readonly\r\n     */\r\n    root: {\r\n        get: function() {\r\n            var ptr = this;\r\n            while (ptr.parent !== null)\r\n                ptr = ptr.parent;\r\n            return ptr;\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Full name including leading dot.\r\n     * @name ReflectionObject#fullName\r\n     * @type {string}\r\n     * @readonly\r\n     */\r\n    fullName: {\r\n        get: function() {\r\n            var path = [ this.name ],\r\n                ptr = this.parent;\r\n            while (ptr) {\r\n                path.unshift(ptr.name);\r\n                ptr = ptr.parent;\r\n            }\r\n            return path.join(\".\");\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Converts this reflection object to its descriptor representation.\r\n * @returns {Object.<string,*>} Descriptor\r\n * @abstract\r\n */\r\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\r\n    throw Error(); // not implemented, shouldn't happen\r\n};\r\n\r\n/**\r\n * Called when this object is added to a parent.\r\n * @param {ReflectionObject} parent Parent added to\r\n * @returns {undefined}\r\n */\r\nReflectionObject.prototype.onAdd = function onAdd(parent) {\r\n    if (this.parent && this.parent !== parent)\r\n        this.parent.remove(this);\r\n    this.parent = parent;\r\n    this.resolved = false;\r\n    var root = parent.root;\r\n    if (root instanceof Root)\r\n        root._handleAdd(this);\r\n};\r\n\r\n/**\r\n * Called when this object is removed from a parent.\r\n * @param {ReflectionObject} parent Parent removed from\r\n * @returns {undefined}\r\n */\r\nReflectionObject.prototype.onRemove = function onRemove(parent) {\r\n    var root = parent.root;\r\n    if (root instanceof Root)\r\n        root._handleRemove(this);\r\n    this.parent = null;\r\n    this.resolved = false;\r\n};\r\n\r\n/**\r\n * Resolves this objects type references.\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.resolve = function resolve() {\r\n    if (this.resolved)\r\n        return this;\r\n    if (this.root instanceof Root)\r\n        this.resolved = true; // only if part of a root\r\n    return this;\r\n};\r\n\r\n/**\r\n * Gets an option value.\r\n * @param {string} name Option name\r\n * @returns {*} Option value or `undefined` if not set\r\n */\r\nReflectionObject.prototype.getOption = function getOption(name) {\r\n    if (this.options)\r\n        return this.options[name];\r\n    return undefined;\r\n};\r\n\r\n/**\r\n * Sets an option.\r\n * @param {string} name Option name\r\n * @param {*} value Option value\r\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\r\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\r\n        (this.options || (this.options = {}))[name] = value;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Sets multiple options.\r\n * @param {Object.<string,*>} options Options to set\r\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\r\n * @returns {ReflectionObject} `this`\r\n */\r\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\r\n    if (options)\r\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\r\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\r\n    return this;\r\n};\r\n\r\n/**\r\n * Converts this instance to its string representation.\r\n * @returns {string} Class name[, space, full name]\r\n */\r\nReflectionObject.prototype.toString = function toString() {\r\n    var className = this.constructor.className,\r\n        fullName  = this.fullName;\r\n    if (fullName.length)\r\n        return className + \" \" + fullName;\r\n    return className;\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nReflectionObject._configure = function(Root_) {\r\n    Root = Root_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = OneOf;\r\n\r\n// extends ReflectionObject\r\nvar ReflectionObject = require(22);\r\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\r\n\r\nvar Field = require(15),\r\n    util  = require(33);\r\n\r\n/**\r\n * Constructs a new oneof instance.\r\n * @classdesc Reflected oneof.\r\n * @extends ReflectionObject\r\n * @constructor\r\n * @param {string} name Oneof name\r\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\r\n * @param {Object.<string,*>} [options] Declared options\r\n * @param {string} [comment] Comment associated with this field\r\n */\r\nfunction OneOf(name, fieldNames, options, comment) {\r\n    if (!Array.isArray(fieldNames)) {\r\n        options = fieldNames;\r\n        fieldNames = undefined;\r\n    }\r\n    ReflectionObject.call(this, name, options);\r\n\r\n    /* istanbul ignore if */\r\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\r\n        throw TypeError(\"fieldNames must be an Array\");\r\n\r\n    /**\r\n     * Field names that belong to this oneof.\r\n     * @type {string[]}\r\n     */\r\n    this.oneof = fieldNames || []; // toJSON, marker\r\n\r\n    /**\r\n     * Fields that belong to this oneof as an array for iteration.\r\n     * @type {Field[]}\r\n     * @readonly\r\n     */\r\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\r\n\r\n    /**\r\n     * Comment for this field.\r\n     * @type {string|null}\r\n     */\r\n    this.comment = comment;\r\n}\r\n\r\n/**\r\n * Oneof descriptor.\r\n * @interface IOneOf\r\n * @property {Array.<string>} oneof Oneof field names\r\n * @property {Object.<string,*>} [options] Oneof options\r\n */\r\n\r\n/**\r\n * Constructs a oneof from a oneof descriptor.\r\n * @param {string} name Oneof name\r\n * @param {IOneOf} json Oneof descriptor\r\n * @returns {OneOf} Created oneof\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nOneOf.fromJSON = function fromJSON(name, json) {\r\n    return new OneOf(name, json.oneof, json.options, json.comment);\r\n};\r\n\r\n/**\r\n * Converts this oneof to a oneof descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IOneOf} Oneof descriptor\r\n */\r\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\" , this.options,\r\n        \"oneof\"   , this.oneof,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Adds the fields of the specified oneof to the parent if not already done so.\r\n * @param {OneOf} oneof The oneof\r\n * @returns {undefined}\r\n * @inner\r\n * @ignore\r\n */\r\nfunction addFieldsToParent(oneof) {\r\n    if (oneof.parent)\r\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\r\n            if (!oneof.fieldsArray[i].parent)\r\n                oneof.parent.add(oneof.fieldsArray[i]);\r\n}\r\n\r\n/**\r\n * Adds a field to this oneof and removes it from its current parent, if any.\r\n * @param {Field} field Field to add\r\n * @returns {OneOf} `this`\r\n */\r\nOneOf.prototype.add = function add(field) {\r\n\r\n    /* istanbul ignore if */\r\n    if (!(field instanceof Field))\r\n        throw TypeError(\"field must be a Field\");\r\n\r\n    if (field.parent && field.parent !== this.parent)\r\n        field.parent.remove(field);\r\n    this.oneof.push(field.name);\r\n    this.fieldsArray.push(field);\r\n    field.partOf = this; // field.parent remains null\r\n    addFieldsToParent(this);\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes a field from this oneof and puts it back to the oneof's parent.\r\n * @param {Field} field Field to remove\r\n * @returns {OneOf} `this`\r\n */\r\nOneOf.prototype.remove = function remove(field) {\r\n\r\n    /* istanbul ignore if */\r\n    if (!(field instanceof Field))\r\n        throw TypeError(\"field must be a Field\");\r\n\r\n    var index = this.fieldsArray.indexOf(field);\r\n\r\n    /* istanbul ignore if */\r\n    if (index < 0)\r\n        throw Error(field + \" is not a member of \" + this);\r\n\r\n    this.fieldsArray.splice(index, 1);\r\n    index = this.oneof.indexOf(field.name);\r\n\r\n    /* istanbul ignore else */\r\n    if (index > -1) // theoretical\r\n        this.oneof.splice(index, 1);\r\n\r\n    field.partOf = null;\r\n    return this;\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nOneOf.prototype.onAdd = function onAdd(parent) {\r\n    ReflectionObject.prototype.onAdd.call(this, parent);\r\n    var self = this;\r\n    // Collect present fields\r\n    for (var i = 0; i < this.oneof.length; ++i) {\r\n        var field = parent.get(this.oneof[i]);\r\n        if (field && !field.partOf) {\r\n            field.partOf = self;\r\n            self.fieldsArray.push(field);\r\n        }\r\n    }\r\n    // Add not yet present fields\r\n    addFieldsToParent(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nOneOf.prototype.onRemove = function onRemove(parent) {\r\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\r\n        if ((field = this.fieldsArray[i]).parent)\r\n            field.parent.remove(field);\r\n    ReflectionObject.prototype.onRemove.call(this, parent);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\r\n * @typedef OneOfDecorator\r\n * @type {function}\r\n * @param {Object} prototype Target prototype\r\n * @param {string} oneofName OneOf name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * OneOf decorator (TypeScript).\r\n * @function\r\n * @param {...string} fieldNames Field names\r\n * @returns {OneOfDecorator} Decorator function\r\n * @template T extends string\r\n */\r\nOneOf.d = function decorateOneOf() {\r\n    var fieldNames = new Array(arguments.length),\r\n        index = 0;\r\n    while (index < arguments.length)\r\n        fieldNames[index] = arguments[index++];\r\n    return function oneOfDecorator(prototype, oneofName) {\r\n        util.decorateType(prototype.constructor)\r\n            .add(new OneOf(oneofName, fieldNames));\r\n        Object.defineProperty(prototype, oneofName, {\r\n            get: util.oneOfGetter(fieldNames),\r\n            set: util.oneOfSetter(fieldNames)\r\n        });\r\n    };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Reader;\r\n\r\nvar util      = require(35);\r\n\r\nvar BufferReader; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    utf8      = util.utf8;\r\n\r\n/* istanbul ignore next */\r\nfunction indexOutOfRange(reader, writeLength) {\r\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\r\n}\r\n\r\n/**\r\n * Constructs a new reader instance using the specified buffer.\r\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n * @param {Uint8Array} buffer Buffer to read from\r\n */\r\nfunction Reader(buffer) {\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @type {Uint8Array}\r\n     */\r\n    this.buf = buffer;\r\n\r\n    /**\r\n     * Read buffer position.\r\n     * @type {number}\r\n     */\r\n    this.pos = 0;\r\n\r\n    /**\r\n     * Read buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = buffer.length;\r\n}\r\n\r\nvar create_array = typeof Uint8Array !== \"undefined\"\r\n    ? function create_typed_array(buffer) {\r\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array(buffer) {\r\n        if (Array.isArray(buffer))\r\n            return new Reader(buffer);\r\n        throw Error(\"illegal buffer\");\r\n    };\r\n\r\n/**\r\n * Creates a new reader using the specified buffer.\r\n * @function\r\n * @param {Uint8Array|Buffer} buffer Buffer to read from\r\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\r\n * @throws {Error} If `buffer` is not a valid buffer\r\n */\r\nReader.create = util.Buffer\r\n    ? function create_buffer_setup(buffer) {\r\n        return (Reader.create = function create_buffer(buffer) {\r\n            return util.Buffer.isBuffer(buffer)\r\n                ? new BufferReader(buffer)\r\n                /* istanbul ignore next */\r\n                : create_array(buffer);\r\n        })(buffer);\r\n    }\r\n    /* istanbul ignore next */\r\n    : create_array;\r\n\r\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\r\n\r\n/**\r\n * Reads a varint as an unsigned 32 bit value.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.uint32 = (function read_uint32_setup() {\r\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\r\n    return function read_uint32() {\r\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\r\n\r\n        /* istanbul ignore if */\r\n        if ((this.pos += 5) > this.len) {\r\n            this.pos = this.len;\r\n            throw indexOutOfRange(this, 10);\r\n        }\r\n        return value;\r\n    };\r\n})();\r\n\r\n/**\r\n * Reads a varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.int32 = function read_int32() {\r\n    return this.uint32() | 0;\r\n};\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 32 bit value.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sint32 = function read_sint32() {\r\n    var value = this.uint32();\r\n    return value >>> 1 ^ -(value & 1) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readLongVarint() {\r\n    // tends to deopt with local vars for octet etc.\r\n    var bits = new LongBits(0, 0);\r\n    var i = 0;\r\n    if (this.len - this.pos > 4) { // fast route (lo)\r\n        for (; i < 4; ++i) {\r\n            // 1st..4th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 5th\r\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\r\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\r\n        if (this.buf[this.pos++] < 128)\r\n            return bits;\r\n        i = 0;\r\n    } else {\r\n        for (; i < 3; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 1st..3th\r\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n        // 4th\r\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\r\n        return bits;\r\n    }\r\n    if (this.len - this.pos > 4) { // fast route (hi)\r\n        for (; i < 5; ++i) {\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    } else {\r\n        for (; i < 5; ++i) {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n            // 6th..10th\r\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\r\n            if (this.buf[this.pos++] < 128)\r\n                return bits;\r\n        }\r\n    }\r\n    /* istanbul ignore next */\r\n    throw Error(\"invalid varint encoding\");\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads a varint as a signed 64 bit value.\r\n * @name Reader#int64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as an unsigned 64 bit value.\r\n * @name Reader#uint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a zig-zag encoded varint as a signed 64 bit value.\r\n * @name Reader#sint64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a varint as a boolean.\r\n * @returns {boolean} Value read\r\n */\r\nReader.prototype.bool = function read_bool() {\r\n    return this.uint32() !== 0;\r\n};\r\n\r\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\r\n    return (buf[end - 4]\r\n          | buf[end - 3] << 8\r\n          | buf[end - 2] << 16\r\n          | buf[end - 1] << 24) >>> 0;\r\n}\r\n\r\n/**\r\n * Reads fixed 32 bits as an unsigned 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.fixed32 = function read_fixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4);\r\n};\r\n\r\n/**\r\n * Reads fixed 32 bits as a signed 32 bit integer.\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.sfixed32 = function read_sfixed32() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\r\n};\r\n\r\n/* eslint-disable no-invalid-this */\r\n\r\nfunction readFixed64(/* this: Reader */) {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 8);\r\n\r\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\r\n}\r\n\r\n/* eslint-enable no-invalid-this */\r\n\r\n/**\r\n * Reads fixed 64 bits.\r\n * @name Reader#fixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads zig-zag encoded fixed 64 bits.\r\n * @name Reader#sfixed64\r\n * @function\r\n * @returns {Long} Value read\r\n */\r\n\r\n/**\r\n * Reads a float (32 bit) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.float = function read_float() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 4 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readFloatLE(this.buf, this.pos);\r\n    this.pos += 4;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a double (64 bit float) as a number.\r\n * @function\r\n * @returns {number} Value read\r\n */\r\nReader.prototype.double = function read_double() {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.pos + 8 > this.len)\r\n        throw indexOutOfRange(this, 4);\r\n\r\n    var value = util.float.readDoubleLE(this.buf, this.pos);\r\n    this.pos += 8;\r\n    return value;\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @returns {Uint8Array} Value read\r\n */\r\nReader.prototype.bytes = function read_bytes() {\r\n    var length = this.uint32(),\r\n        start  = this.pos,\r\n        end    = this.pos + length;\r\n\r\n    /* istanbul ignore if */\r\n    if (end > this.len)\r\n        throw indexOutOfRange(this, length);\r\n\r\n    this.pos += length;\r\n    if (Array.isArray(this.buf)) // plain array\r\n        return this.buf.slice(start, end);\r\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\r\n        ? new this.buf.constructor(0)\r\n        : this._slice.call(this.buf, start, end);\r\n};\r\n\r\n/**\r\n * Reads a string preceeded by its byte length as a varint.\r\n * @returns {string} Value read\r\n */\r\nReader.prototype.string = function read_string() {\r\n    var bytes = this.bytes();\r\n    return utf8.read(bytes, 0, bytes.length);\r\n};\r\n\r\n/**\r\n * Skips the specified number of bytes if specified, otherwise skips a varint.\r\n * @param {number} [length] Length if known, otherwise a varint is assumed\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skip = function skip(length) {\r\n    if (typeof length === \"number\") {\r\n        /* istanbul ignore if */\r\n        if (this.pos + length > this.len)\r\n            throw indexOutOfRange(this, length);\r\n        this.pos += length;\r\n    } else {\r\n        do {\r\n            /* istanbul ignore if */\r\n            if (this.pos >= this.len)\r\n                throw indexOutOfRange(this);\r\n        } while (this.buf[this.pos++] & 128);\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Skips the next element of the specified wire type.\r\n * @param {number} wireType Wire type received\r\n * @returns {Reader} `this`\r\n */\r\nReader.prototype.skipType = function(wireType) {\r\n    switch (wireType) {\r\n        case 0:\r\n            this.skip();\r\n            break;\r\n        case 1:\r\n            this.skip(8);\r\n            break;\r\n        case 2:\r\n            this.skip(this.uint32());\r\n            break;\r\n        case 3:\r\n            while ((wireType = this.uint32() & 7) !== 4) {\r\n                this.skipType(wireType);\r\n            }\r\n            break;\r\n        case 5:\r\n            this.skip(4);\r\n            break;\r\n\r\n        /* istanbul ignore next */\r\n        default:\r\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\r\n    }\r\n    return this;\r\n};\r\n\r\nReader._configure = function(BufferReader_) {\r\n    BufferReader = BufferReader_;\r\n\r\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\r\n    util.merge(Reader.prototype, {\r\n\r\n        int64: function read_int64() {\r\n            return readLongVarint.call(this)[fn](false);\r\n        },\r\n\r\n        uint64: function read_uint64() {\r\n            return readLongVarint.call(this)[fn](true);\r\n        },\r\n\r\n        sint64: function read_sint64() {\r\n            return readLongVarint.call(this).zzDecode()[fn](false);\r\n        },\r\n\r\n        fixed64: function read_fixed64() {\r\n            return readFixed64.call(this)[fn](true);\r\n        },\r\n\r\n        sfixed64: function read_sfixed64() {\r\n            return readFixed64.call(this)[fn](false);\r\n        }\r\n\r\n    });\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erReader;\r\n\r\n// extends Reader\r\nvar Reader = require(24);\r\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\r\n\r\nvar util = require(35);\r\n\r\n/**\r\n * Constructs a new buffer reader instance.\r\n * @classdesc Wire format reader using node buffers.\r\n * @extends Reader\r\n * @constructor\r\n * @param {Buffer} buffer Buffer to read from\r\n */\r\nfunction BufferReader(buffer) {\r\n    Reader.call(this, buffer);\r\n\r\n    /**\r\n     * Read buffer.\r\n     * @name BufferReader#buf\r\n     * @type {Buffer}\r\n     */\r\n}\r\n\r\n/* istanbul ignore else */\r\nif (util.Buffer)\r\n    BufferReader.prototype._slice = util.Buffer.prototype.slice;\r\n\r\n/**\r\n * @override\r\n */\r\nBufferReader.prototype.string = function read_string_buffer() {\r\n    var len = this.uint32(); // modifies pos\r\n    return this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len));\r\n};\r\n\r\n/**\r\n * Reads a sequence of bytes preceeded by its length as a varint.\r\n * @name BufferReader#bytes\r\n * @function\r\n * @returns {<PERSON><PERSON>er} Value read\r\n */\r\n", "\"use strict\";\r\nmodule.exports = Root;\r\n\r\n// extends Namespace\r\nvar Namespace = require(21);\r\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\r\n\r\nvar Field   = require(15),\r\n    Enum    = require(14),\r\n    OneOf   = require(23),\r\n    util    = require(33);\r\n\r\nvar Type,   // cyclic\r\n    parse,  // might be excluded\r\n    common; // \"\r\n\r\n/**\r\n * Constructs a new root namespace instance.\r\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {Object.<string,*>} [options] Top level options\r\n */\r\nfunction Root(options) {\r\n    Namespace.call(this, \"\", options);\r\n\r\n    /**\r\n     * Deferred extension fields.\r\n     * @type {Field[]}\r\n     */\r\n    this.deferred = [];\r\n\r\n    /**\r\n     * Resolved file names of loaded files.\r\n     * @type {string[]}\r\n     */\r\n    this.files = [];\r\n}\r\n\r\n/**\r\n * Loads a namespace descriptor into a root namespace.\r\n * @param {INamespace} json Nameespace descriptor\r\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\r\n * @returns {Root} Root namespace\r\n */\r\nRoot.fromJSON = function fromJSON(json, root) {\r\n    if (!root)\r\n        root = new Root();\r\n    if (json.options)\r\n        root.setOptions(json.options);\r\n    return root.addJSON(json.nested);\r\n};\r\n\r\n/**\r\n * Resolves the path of an imported file, relative to the importing origin.\r\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\r\n * @function\r\n * @param {string} origin The file name of the importing file\r\n * @param {string} target The file name being imported\r\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\r\n */\r\nRoot.prototype.resolvePath = util.path.resolve;\r\n\r\n// A symbol-like function to safely signal synchronous loading\r\n/* istanbul ignore next */\r\nfunction SYNC() {} // eslint-disable-line no-empty-function\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} options Parse options\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nRoot.prototype.load = function load(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = undefined;\r\n    }\r\n    var self = this;\r\n    if (!callback)\r\n        return util.asPromise(load, self, filename, options);\r\n\r\n    var sync = callback === SYNC; // undocumented\r\n\r\n    // Finishes loading by calling the callback (exactly once)\r\n    function finish(err, root) {\r\n        /* istanbul ignore if */\r\n        if (!callback)\r\n            return;\r\n        var cb = callback;\r\n        callback = null;\r\n        if (sync)\r\n            throw err;\r\n        cb(err, root);\r\n    }\r\n\r\n    // Processes a single file\r\n    function process(filename, source) {\r\n        try {\r\n            if (util.isString(source) && source.charAt(0) === \"{\")\r\n                source = JSON.parse(source);\r\n            if (!util.isString(source))\r\n                self.setOptions(source.options).addJSON(source.nested);\r\n            else {\r\n                parse.filename = filename;\r\n                var parsed = parse(source, self, options),\r\n                    resolved,\r\n                    i = 0;\r\n                if (parsed.imports)\r\n                    for (; i < parsed.imports.length; ++i)\r\n                        if (resolved = self.resolvePath(filename, parsed.imports[i]))\r\n                            fetch(resolved);\r\n                if (parsed.weakImports)\r\n                    for (i = 0; i < parsed.weakImports.length; ++i)\r\n                        if (resolved = self.resolvePath(filename, parsed.weakImports[i]))\r\n                            fetch(resolved, true);\r\n            }\r\n        } catch (err) {\r\n            finish(err);\r\n        }\r\n        if (!sync && !queued)\r\n            finish(null, self); // only once anyway\r\n    }\r\n\r\n    // Fetches a single file\r\n    function fetch(filename, weak) {\r\n\r\n        // Strip path if this file references a bundled definition\r\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\r\n        if (idx > -1) {\r\n            var altname = filename.substring(idx);\r\n            if (altname in common)\r\n                filename = altname;\r\n        }\r\n\r\n        // Skip if already loaded / attempted\r\n        if (self.files.indexOf(filename) > -1)\r\n            return;\r\n        self.files.push(filename);\r\n\r\n        // Shortcut bundled definitions\r\n        if (filename in common) {\r\n            if (sync)\r\n                process(filename, common[filename]);\r\n            else {\r\n                ++queued;\r\n                setTimeout(function() {\r\n                    --queued;\r\n                    process(filename, common[filename]);\r\n                });\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Otherwise fetch from disk or network\r\n        if (sync) {\r\n            var source;\r\n            try {\r\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\r\n            } catch (err) {\r\n                if (!weak)\r\n                    finish(err);\r\n                return;\r\n            }\r\n            process(filename, source);\r\n        } else {\r\n            ++queued;\r\n            util.fetch(filename, function(err, source) {\r\n                --queued;\r\n                /* istanbul ignore if */\r\n                if (!callback)\r\n                    return; // terminated meanwhile\r\n                if (err) {\r\n                    /* istanbul ignore else */\r\n                    if (!weak)\r\n                        finish(err);\r\n                    else if (!queued) // can't be covered reliably\r\n                        finish(null, self);\r\n                    return;\r\n                }\r\n                process(filename, source);\r\n            });\r\n        }\r\n    }\r\n    var queued = 0;\r\n\r\n    // Assembling the root namespace doesn't require working type\r\n    // references anymore, so we can load everything in parallel\r\n    if (util.isString(filename))\r\n        filename = [ filename ];\r\n    for (var i = 0, resolved; i < filename.length; ++i)\r\n        if (resolved = self.resolvePath(\"\", filename[i]))\r\n            fetch(resolved);\r\n\r\n    if (sync)\r\n        return self;\r\n    if (!queued)\r\n        finish(null, self);\r\n    return undefined;\r\n};\r\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\r\n * @function Root#load\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {LoadCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n// function load(filename:string, callback:LoadCallback):undefined\r\n\r\n/**\r\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\r\n * @function Root#load\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {Promise<Root>} Promise\r\n * @variation 3\r\n */\r\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\r\n\r\n/**\r\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\r\n * @function Root#loadSync\r\n * @param {string|string[]} filename Names of one or multiple files to load\r\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\r\n * @returns {Root} Root namespace\r\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\r\n */\r\nRoot.prototype.loadSync = function loadSync(filename, options) {\r\n    if (!util.isNode)\r\n        throw Error(\"not supported\");\r\n    return this.load(filename, options, SYNC);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nRoot.prototype.resolveAll = function resolveAll() {\r\n    if (this.deferred.length)\r\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\r\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\r\n        }).join(\", \"));\r\n    return Namespace.prototype.resolveAll.call(this);\r\n};\r\n\r\n// only uppercased (and thus conflict-free) children are exposed, see below\r\nvar exposeRe = /^[A-Z]/;\r\n\r\n/**\r\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\r\n * @param {Root} root Root instance\r\n * @param {Field} field Declaring extension field witin the declaring type\r\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\r\n * @inner\r\n * @ignore\r\n */\r\nfunction tryHandleExtension(root, field) {\r\n    var extendedType = field.parent.lookup(field.extend);\r\n    if (extendedType) {\r\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\r\n        sisterField.declaringField = field;\r\n        field.extensionField = sisterField;\r\n        extendedType.add(sisterField);\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\n\r\n/**\r\n * Called when any object is added to this root or its sub-namespaces.\r\n * @param {ReflectionObject} object Object added\r\n * @returns {undefined}\r\n * @private\r\n */\r\nRoot.prototype._handleAdd = function _handleAdd(object) {\r\n    if (object instanceof Field) {\r\n\r\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\r\n            if (!tryHandleExtension(this, object))\r\n                this.deferred.push(object);\r\n\r\n    } else if (object instanceof Enum) {\r\n\r\n        if (exposeRe.test(object.name))\r\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\r\n\r\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\r\n\r\n        if (object instanceof Type) // Try to handle any deferred extensions\r\n            for (var i = 0; i < this.deferred.length;)\r\n                if (tryHandleExtension(this, this.deferred[i]))\r\n                    this.deferred.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\r\n            this._handleAdd(object._nestedArray[j]);\r\n        if (exposeRe.test(object.name))\r\n            object.parent[object.name] = object; // expose namespace as property of its parent\r\n    }\r\n\r\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\r\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\r\n    // a static module with reflection-based solutions where the condition is met.\r\n};\r\n\r\n/**\r\n * Called when any object is removed from this root or its sub-namespaces.\r\n * @param {ReflectionObject} object Object removed\r\n * @returns {undefined}\r\n * @private\r\n */\r\nRoot.prototype._handleRemove = function _handleRemove(object) {\r\n    if (object instanceof Field) {\r\n\r\n        if (/* an extension field */ object.extend !== undefined) {\r\n            if (/* already handled */ object.extensionField) { // remove its sister field\r\n                object.extensionField.parent.remove(object.extensionField);\r\n                object.extensionField = null;\r\n            } else { // cancel the extension\r\n                var index = this.deferred.indexOf(object);\r\n                /* istanbul ignore else */\r\n                if (index > -1)\r\n                    this.deferred.splice(index, 1);\r\n            }\r\n        }\r\n\r\n    } else if (object instanceof Enum) {\r\n\r\n        if (exposeRe.test(object.name))\r\n            delete object.parent[object.name]; // unexpose enum values\r\n\r\n    } else if (object instanceof Namespace) {\r\n\r\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\r\n            this._handleRemove(object._nestedArray[i]);\r\n\r\n        if (exposeRe.test(object.name))\r\n            delete object.parent[object.name]; // unexpose namespaces\r\n\r\n    }\r\n};\r\n\r\n// Sets up cyclic dependencies (called in index-light)\r\nRoot._configure = function(Type_, parse_, common_) {\r\n    Type   = Type_;\r\n    parse  = parse_;\r\n    common = common_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = {};\r\n\r\n/**\r\n * Named roots.\r\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\r\n * Can also be used manually to make roots available accross modules.\r\n * @name roots\r\n * @type {Object.<string,Root>}\r\n * @example\r\n * // pbjs -r myroot -o compiled.js ...\r\n *\r\n * // in another module:\r\n * require(\"./compiled.js\");\r\n *\r\n * // in any subsequent module:\r\n * var root = protobuf.roots[\"myroot\"];\r\n */\r\n", "\"use strict\";\r\n\r\n/**\r\n * Streaming RPC helpers.\r\n * @namespace\r\n */\r\nvar rpc = exports;\r\n\r\n/**\r\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\r\n * @typedef RPCImpl\r\n * @type {function}\r\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\r\n * @param {Uint8Array} requestData Request data\r\n * @param {RPCImplCallback} callback Callback function\r\n * @returns {undefined}\r\n * @example\r\n * function rpcImpl(method, requestData, callback) {\r\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\r\n *         throw Error(\"no such method\");\r\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\r\n *         callback(err, responseData);\r\n *     });\r\n * }\r\n */\r\n\r\n/**\r\n * Node-style callback as used by {@link RPCImpl}.\r\n * @typedef RPCImplCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any, otherwise `null`\r\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\nrpc.Service = require(29);\r\n", "\"use strict\";\r\nmodule.exports = Service;\r\n\r\nvar util = require(35);\r\n\r\n// Extends EventEmitter\r\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\r\n\r\n/**\r\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\r\n *\r\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\r\n * @typedef rpc.ServiceMethodCallback\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {TRes} [response] Response message\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\r\n * @typedef rpc.ServiceMethod\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n * @type {function}\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\r\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\r\n */\r\n\r\n/**\r\n * Constructs a new RPC service instance.\r\n * @classdesc An RPC service as returned by {@link Service#create}.\r\n * @exports rpc.Service\r\n * @extends util.EventEmitter\r\n * @constructor\r\n * @param {RPCImpl} rpcImpl RPC implementation\r\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\r\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\r\n */\r\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\r\n\r\n    if (typeof rpcImpl !== \"function\")\r\n        throw TypeError(\"rpcImpl must be a function\");\r\n\r\n    util.EventEmitter.call(this);\r\n\r\n    /**\r\n     * RPC implementation. Becomes `null` once the service is ended.\r\n     * @type {RPCImpl|null}\r\n     */\r\n    this.rpcImpl = rpcImpl;\r\n\r\n    /**\r\n     * Whether requests are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.requestDelimited = Boolean(requestDelimited);\r\n\r\n    /**\r\n     * Whether responses are length-delimited.\r\n     * @type {boolean}\r\n     */\r\n    this.responseDelimited = Boolean(responseDelimited);\r\n}\r\n\r\n/**\r\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\r\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\r\n * @param {Constructor<TReq>} requestCtor Request constructor\r\n * @param {Constructor<TRes>} responseCtor Response constructor\r\n * @param {TReq|Properties<TReq>} request Request message or plain object\r\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\r\n * @returns {undefined}\r\n * @template TReq extends Message<TReq>\r\n * @template TRes extends Message<TRes>\r\n */\r\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\r\n\r\n    if (!request)\r\n        throw TypeError(\"request must be specified\");\r\n\r\n    var self = this;\r\n    if (!callback)\r\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\r\n\r\n    if (!self.rpcImpl) {\r\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\r\n        return undefined;\r\n    }\r\n\r\n    try {\r\n        return self.rpcImpl(\r\n            method,\r\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\r\n            function rpcCallback(err, response) {\r\n\r\n                if (err) {\r\n                    self.emit(\"error\", err, method);\r\n                    return callback(err);\r\n                }\r\n\r\n                if (response === null) {\r\n                    self.end(/* endedByRPC */ true);\r\n                    return undefined;\r\n                }\r\n\r\n                if (!(response instanceof responseCtor)) {\r\n                    try {\r\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\r\n                    } catch (err) {\r\n                        self.emit(\"error\", err, method);\r\n                        return callback(err);\r\n                    }\r\n                }\r\n\r\n                self.emit(\"data\", response, method);\r\n                return callback(null, response);\r\n            }\r\n        );\r\n    } catch (err) {\r\n        self.emit(\"error\", err, method);\r\n        setTimeout(function() { callback(err); }, 0);\r\n        return undefined;\r\n    }\r\n};\r\n\r\n/**\r\n * Ends this service and emits the `end` event.\r\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\r\n * @returns {rpc.Service} `this`\r\n */\r\nService.prototype.end = function end(endedByRPC) {\r\n    if (this.rpcImpl) {\r\n        if (!endedByRPC) // signal end to rpcImpl\r\n            this.rpcImpl(null, null, null);\r\n        this.rpcImpl = null;\r\n        this.emit(\"end\").off();\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Service;\r\n\r\n// extends Namespace\r\nvar Namespace = require(21);\r\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\r\n\r\nvar Method = require(20),\r\n    util   = require(33),\r\n    rpc    = require(28);\r\n\r\n/**\r\n * Constructs a new service instance.\r\n * @classdesc Reflected service.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Service name\r\n * @param {Object.<string,*>} [options] Service options\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nfunction Service(name, options) {\r\n    Namespace.call(this, name, options);\r\n\r\n    /**\r\n     * Service methods.\r\n     * @type {Object.<string,Method>}\r\n     */\r\n    this.methods = {}; // toJSON, marker\r\n\r\n    /**\r\n     * Cached methods as an array.\r\n     * @type {Method[]|null}\r\n     * @private\r\n     */\r\n    this._methodsArray = null;\r\n}\r\n\r\n/**\r\n * Service descriptor.\r\n * @interface IService\r\n * @extends INamespace\r\n * @property {Object.<string,IMethod>} methods Method descriptors\r\n */\r\n\r\n/**\r\n * Constructs a service from a service descriptor.\r\n * @param {string} name Service name\r\n * @param {IService} json Service descriptor\r\n * @returns {Service} Created service\r\n * @throws {TypeError} If arguments are invalid\r\n */\r\nService.fromJSON = function fromJSON(name, json) {\r\n    var service = new Service(name, json.options);\r\n    /* istanbul ignore else */\r\n    if (json.methods)\r\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\r\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\r\n    if (json.nested)\r\n        service.addJSON(json.nested);\r\n    service.comment = json.comment;\r\n    return service;\r\n};\r\n\r\n/**\r\n * Converts this service to a service descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IService} Service descriptor\r\n */\r\nService.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\" , inherited && inherited.options || undefined,\r\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\r\n        \"nested\"  , inherited && inherited.nested || undefined,\r\n        \"comment\" , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * Methods of this service as an array for iteration.\r\n * @name Service#methodsArray\r\n * @type {Method[]}\r\n * @readonly\r\n */\r\nObject.defineProperty(Service.prototype, \"methodsArray\", {\r\n    get: function() {\r\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\r\n    }\r\n});\r\n\r\nfunction clearCache(service) {\r\n    service._methodsArray = null;\r\n    return service;\r\n}\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.get = function get(name) {\r\n    return this.methods[name]\r\n        || Namespace.prototype.get.call(this, name);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.resolveAll = function resolveAll() {\r\n    var methods = this.methodsArray;\r\n    for (var i = 0; i < methods.length; ++i)\r\n        methods[i].resolve();\r\n    return Namespace.prototype.resolve.call(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.add = function add(object) {\r\n\r\n    /* istanbul ignore if */\r\n    if (this.get(object.name))\r\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n\r\n    if (object instanceof Method) {\r\n        this.methods[object.name] = object;\r\n        object.parent = this;\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.add.call(this, object);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nService.prototype.remove = function remove(object) {\r\n    if (object instanceof Method) {\r\n\r\n        /* istanbul ignore if */\r\n        if (this.methods[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.methods[object.name];\r\n        object.parent = null;\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.remove.call(this, object);\r\n};\r\n\r\n/**\r\n * Creates a runtime service using the specified rpc implementation.\r\n * @param {RPCImpl} rpcImpl RPC implementation\r\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\r\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\r\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\r\n */\r\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\r\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\r\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\r\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\r\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\r\n            m: method,\r\n            q: method.resolvedRequestType.ctor,\r\n            s: method.resolvedResponseType.ctor\r\n        });\r\n    }\r\n    return rpcService;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Type;\r\n\r\n// extends Namespace\r\nvar Namespace = require(21);\r\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\r\n\r\nvar Enum      = require(14),\r\n    OneOf     = require(23),\r\n    Field     = require(15),\r\n    MapField  = require(18),\r\n    Service   = require(30),\r\n    Message   = require(19),\r\n    Reader    = require(24),\r\n    Writer    = require(38),\r\n    util      = require(33),\r\n    encoder   = require(13),\r\n    decoder   = require(12),\r\n    verifier  = require(36),\r\n    converter = require(11),\r\n    wrappers  = require(37);\r\n\r\n/**\r\n * Constructs a new reflected message type instance.\r\n * @classdesc Reflected message type.\r\n * @extends NamespaceBase\r\n * @constructor\r\n * @param {string} name Message name\r\n * @param {Object.<string,*>} [options] Declared options\r\n */\r\nfunction Type(name, options) {\r\n    Namespace.call(this, name, options);\r\n\r\n    /**\r\n     * Message fields.\r\n     * @type {Object.<string,Field>}\r\n     */\r\n    this.fields = {};  // toJSON, marker\r\n\r\n    /**\r\n     * Oneofs declared within this namespace, if any.\r\n     * @type {Object.<string,OneOf>}\r\n     */\r\n    this.oneofs = undefined; // toJSON\r\n\r\n    /**\r\n     * Extension ranges, if any.\r\n     * @type {number[][]}\r\n     */\r\n    this.extensions = undefined; // toJSON\r\n\r\n    /**\r\n     * Reserved ranges, if any.\r\n     * @type {Array.<number[]|string>}\r\n     */\r\n    this.reserved = undefined; // toJSON\r\n\r\n    /*?\r\n     * Whether this type is a legacy group.\r\n     * @type {boolean|undefined}\r\n     */\r\n    this.group = undefined; // toJSON\r\n\r\n    /**\r\n     * Cached fields by id.\r\n     * @type {Object.<number,Field>|null}\r\n     * @private\r\n     */\r\n    this._fieldsById = null;\r\n\r\n    /**\r\n     * Cached fields as an array.\r\n     * @type {Field[]|null}\r\n     * @private\r\n     */\r\n    this._fieldsArray = null;\r\n\r\n    /**\r\n     * Cached oneofs as an array.\r\n     * @type {OneOf[]|null}\r\n     * @private\r\n     */\r\n    this._oneofsArray = null;\r\n\r\n    /**\r\n     * Cached constructor.\r\n     * @type {Constructor<{}>}\r\n     * @private\r\n     */\r\n    this._ctor = null;\r\n}\r\n\r\nObject.defineProperties(Type.prototype, {\r\n\r\n    /**\r\n     * Message fields by id.\r\n     * @name Type#fieldsById\r\n     * @type {Object.<number,Field>}\r\n     * @readonly\r\n     */\r\n    fieldsById: {\r\n        get: function() {\r\n\r\n            /* istanbul ignore if */\r\n            if (this._fieldsById)\r\n                return this._fieldsById;\r\n\r\n            this._fieldsById = {};\r\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\r\n                var field = this.fields[names[i]],\r\n                    id = field.id;\r\n\r\n                /* istanbul ignore if */\r\n                if (this._fieldsById[id])\r\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\r\n\r\n                this._fieldsById[id] = field;\r\n            }\r\n            return this._fieldsById;\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Fields of this message as an array for iteration.\r\n     * @name Type#fieldsArray\r\n     * @type {Field[]}\r\n     * @readonly\r\n     */\r\n    fieldsArray: {\r\n        get: function() {\r\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\r\n        }\r\n    },\r\n\r\n    /**\r\n     * Oneofs of this message as an array for iteration.\r\n     * @name Type#oneofsArray\r\n     * @type {OneOf[]}\r\n     * @readonly\r\n     */\r\n    oneofsArray: {\r\n        get: function() {\r\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\r\n        }\r\n    },\r\n\r\n    /**\r\n     * The registered constructor, if any registered, otherwise a generic constructor.\r\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\r\n     * @name Type#ctor\r\n     * @type {Constructor<{}>}\r\n     */\r\n    ctor: {\r\n        get: function() {\r\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\r\n        },\r\n        set: function(ctor) {\r\n\r\n            // Ensure proper prototype\r\n            var prototype = ctor.prototype;\r\n            if (!(prototype instanceof Message)) {\r\n                (ctor.prototype = new Message()).constructor = ctor;\r\n                util.merge(ctor.prototype, prototype);\r\n            }\r\n\r\n            // Classes and messages reference their reflected type\r\n            ctor.$type = ctor.prototype.$type = this;\r\n\r\n            // Mix in static methods\r\n            util.merge(ctor, Message, true);\r\n\r\n            this._ctor = ctor;\r\n\r\n            // Messages have non-enumerable default values on their prototype\r\n            var i = 0;\r\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\r\n                this._fieldsArray[i].resolve(); // ensures a proper value\r\n\r\n            // Messages have non-enumerable getters and setters for each virtual oneof field\r\n            var ctorProperties = {};\r\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\r\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\r\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\r\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\r\n                };\r\n            if (i)\r\n                Object.defineProperties(ctor.prototype, ctorProperties);\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Generates a constructor function for the specified type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nType.generateConstructor = function generateConstructor(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    var gen = util.codegen([\"p\"], mtype.name);\r\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\r\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\r\n        if ((field = mtype._fieldsArray[i]).map) gen\r\n            (\"this%s={}\", util.safeProp(field.name));\r\n        else if (field.repeated) gen\r\n            (\"this%s=[]\", util.safeProp(field.name));\r\n    return gen\r\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\r\n        (\"this[ks[i]]=p[ks[i]]\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n};\r\n\r\nfunction clearCache(type) {\r\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\r\n    delete type.encode;\r\n    delete type.decode;\r\n    delete type.verify;\r\n    return type;\r\n}\r\n\r\n/**\r\n * Message type descriptor.\r\n * @interface IType\r\n * @extends INamespace\r\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\r\n * @property {Object.<string,IField>} fields Field descriptors\r\n * @property {number[][]} [extensions] Extension ranges\r\n * @property {number[][]} [reserved] Reserved ranges\r\n * @property {boolean} [group=false] Whether a legacy group or not\r\n */\r\n\r\n/**\r\n * Creates a message type from a message type descriptor.\r\n * @param {string} name Message name\r\n * @param {IType} json Message type descriptor\r\n * @returns {Type} Created message type\r\n */\r\nType.fromJSON = function fromJSON(name, json) {\r\n    var type = new Type(name, json.options);\r\n    type.extensions = json.extensions;\r\n    type.reserved = json.reserved;\r\n    var names = Object.keys(json.fields),\r\n        i = 0;\r\n    for (; i < names.length; ++i)\r\n        type.add(\r\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\r\n            ? MapField.fromJSON\r\n            : Field.fromJSON )(names[i], json.fields[names[i]])\r\n        );\r\n    if (json.oneofs)\r\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\r\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\r\n    if (json.nested)\r\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\r\n            var nested = json.nested[names[i]];\r\n            type.add( // most to least likely\r\n                ( nested.id !== undefined\r\n                ? Field.fromJSON\r\n                : nested.fields !== undefined\r\n                ? Type.fromJSON\r\n                : nested.values !== undefined\r\n                ? Enum.fromJSON\r\n                : nested.methods !== undefined\r\n                ? Service.fromJSON\r\n                : Namespace.fromJSON )(names[i], nested)\r\n            );\r\n        }\r\n    if (json.extensions && json.extensions.length)\r\n        type.extensions = json.extensions;\r\n    if (json.reserved && json.reserved.length)\r\n        type.reserved = json.reserved;\r\n    if (json.group)\r\n        type.group = true;\r\n    if (json.comment)\r\n        type.comment = json.comment;\r\n    return type;\r\n};\r\n\r\n/**\r\n * Converts this message type to a message type descriptor.\r\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\r\n * @returns {IType} Message type descriptor\r\n */\r\nType.prototype.toJSON = function toJSON(toJSONOptions) {\r\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\r\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\r\n    return util.toObject([\r\n        \"options\"    , inherited && inherited.options || undefined,\r\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\r\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\r\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\r\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\r\n        \"group\"      , this.group || undefined,\r\n        \"nested\"     , inherited && inherited.nested || undefined,\r\n        \"comment\"    , keepComments ? this.comment : undefined\r\n    ]);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nType.prototype.resolveAll = function resolveAll() {\r\n    var fields = this.fieldsArray, i = 0;\r\n    while (i < fields.length)\r\n        fields[i++].resolve();\r\n    var oneofs = this.oneofsArray; i = 0;\r\n    while (i < oneofs.length)\r\n        oneofs[i++].resolve();\r\n    return Namespace.prototype.resolveAll.call(this);\r\n};\r\n\r\n/**\r\n * @override\r\n */\r\nType.prototype.get = function get(name) {\r\n    return this.fields[name]\r\n        || this.oneofs && this.oneofs[name]\r\n        || this.nested && this.nested[name]\r\n        || null;\r\n};\r\n\r\n/**\r\n * Adds a nested object to this type.\r\n * @param {ReflectionObject} object Nested object to add\r\n * @returns {Type} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\r\n */\r\nType.prototype.add = function add(object) {\r\n\r\n    if (this.get(object.name))\r\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\r\n\r\n    if (object instanceof Field && object.extend === undefined) {\r\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\r\n        // The root object takes care of adding distinct sister-fields to the respective extended\r\n        // type instead.\r\n\r\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\r\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\r\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\r\n        if (this.isReservedId(object.id))\r\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\r\n        if (this.isReservedName(object.name))\r\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\r\n\r\n        if (object.parent)\r\n            object.parent.remove(object);\r\n        this.fields[object.name] = object;\r\n        object.message = this;\r\n        object.onAdd(this);\r\n        return clearCache(this);\r\n    }\r\n    if (object instanceof OneOf) {\r\n        if (!this.oneofs)\r\n            this.oneofs = {};\r\n        this.oneofs[object.name] = object;\r\n        object.onAdd(this);\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.add.call(this, object);\r\n};\r\n\r\n/**\r\n * Removes a nested object from this type.\r\n * @param {ReflectionObject} object Nested object to remove\r\n * @returns {Type} `this`\r\n * @throws {TypeError} If arguments are invalid\r\n * @throws {Error} If `object` is not a member of this type\r\n */\r\nType.prototype.remove = function remove(object) {\r\n    if (object instanceof Field && object.extend === undefined) {\r\n        // See Type#add for the reason why extension fields are excluded here.\r\n\r\n        /* istanbul ignore if */\r\n        if (!this.fields || this.fields[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.fields[object.name];\r\n        object.parent = null;\r\n        object.onRemove(this);\r\n        return clearCache(this);\r\n    }\r\n    if (object instanceof OneOf) {\r\n\r\n        /* istanbul ignore if */\r\n        if (!this.oneofs || this.oneofs[object.name] !== object)\r\n            throw Error(object + \" is not a member of \" + this);\r\n\r\n        delete this.oneofs[object.name];\r\n        object.parent = null;\r\n        object.onRemove(this);\r\n        return clearCache(this);\r\n    }\r\n    return Namespace.prototype.remove.call(this, object);\r\n};\r\n\r\n/**\r\n * Tests if the specified id is reserved.\r\n * @param {number} id Id to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nType.prototype.isReservedId = function isReservedId(id) {\r\n    return Namespace.isReservedId(this.reserved, id);\r\n};\r\n\r\n/**\r\n * Tests if the specified name is reserved.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nType.prototype.isReservedName = function isReservedName(name) {\r\n    return Namespace.isReservedName(this.reserved, name);\r\n};\r\n\r\n/**\r\n * Creates a new message of this type using the specified properties.\r\n * @param {Object.<string,*>} [properties] Properties to set\r\n * @returns {Message<{}>} Message instance\r\n */\r\nType.prototype.create = function create(properties) {\r\n    return new this.ctor(properties);\r\n};\r\n\r\n/**\r\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\r\n * @returns {Type} `this`\r\n */\r\nType.prototype.setup = function setup() {\r\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\r\n    // multiple times (V8, soft-deopt prototype-check).\r\n\r\n    var fullName = this.fullName,\r\n        types    = [];\r\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\r\n        types.push(this._fieldsArray[i].resolve().resolvedType);\r\n\r\n    // Replace setup methods with type-specific generated functions\r\n    this.encode = encoder(this)({\r\n        Writer : Writer,\r\n        types  : types,\r\n        util   : util\r\n    });\r\n    this.decode = decoder(this)({\r\n        Reader : Reader,\r\n        types  : types,\r\n        util   : util\r\n    });\r\n    this.verify = verifier(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n    this.fromObject = converter.fromObject(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n    this.toObject = converter.toObject(this)({\r\n        types : types,\r\n        util  : util\r\n    });\r\n\r\n    // Inject custom wrappers for common types\r\n    var wrapper = wrappers[fullName];\r\n    if (wrapper) {\r\n        var originalThis = Object.create(this);\r\n        // if (wrapper.fromObject) {\r\n            originalThis.fromObject = this.fromObject;\r\n            this.fromObject = wrapper.fromObject.bind(originalThis);\r\n        // }\r\n        // if (wrapper.toObject) {\r\n            originalThis.toObject = this.toObject;\r\n            this.toObject = wrapper.toObject.bind(originalThis);\r\n        // }\r\n    }\r\n\r\n    return this;\r\n};\r\n\r\n/**\r\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\r\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\r\n * @param {Writer} [writer] Writer to encode to\r\n * @returns {Writer} writer\r\n */\r\nType.prototype.encode = function encode_setup(message, writer) {\r\n    return this.setup().encode(message, writer); // overrides this method\r\n};\r\n\r\n/**\r\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\r\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\r\n * @param {Writer} [writer] Writer to encode to\r\n * @returns {Writer} writer\r\n */\r\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\r\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\r\n};\r\n\r\n/**\r\n * Decodes a message of this type.\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\r\n * @param {number} [length] Length of the message, if known beforehand\r\n * @returns {Message<{}>} Decoded message\r\n * @throws {Error} If the payload is not a reader or valid buffer\r\n * @throws {util.ProtocolError<{}>} If required fields are missing\r\n */\r\nType.prototype.decode = function decode_setup(reader, length) {\r\n    return this.setup().decode(reader, length); // overrides this method\r\n};\r\n\r\n/**\r\n * Decodes a message of this type preceeded by its byte length as a varint.\r\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\r\n * @returns {Message<{}>} Decoded message\r\n * @throws {Error} If the payload is not a reader or valid buffer\r\n * @throws {util.ProtocolError} If required fields are missing\r\n */\r\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\r\n    if (!(reader instanceof Reader))\r\n        reader = Reader.create(reader);\r\n    return this.decode(reader, reader.uint32());\r\n};\r\n\r\n/**\r\n * Verifies that field values are valid and that required fields are present.\r\n * @param {Object.<string,*>} message Plain object to verify\r\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\r\n */\r\nType.prototype.verify = function verify_setup(message) {\r\n    return this.setup().verify(message); // overrides this method\r\n};\r\n\r\n/**\r\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\r\n * @param {Object.<string,*>} object Plain object to convert\r\n * @returns {Message<{}>} Message instance\r\n */\r\nType.prototype.fromObject = function fromObject(object) {\r\n    return this.setup().fromObject(object);\r\n};\r\n\r\n/**\r\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\r\n * @interface IConversionOptions\r\n * @property {Function} [longs] Long conversion type.\r\n * Valid values are `String` and `Number` (the global types).\r\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\r\n * @property {Function} [enums] Enum value conversion type.\r\n * Only valid value is `String` (the global type).\r\n * Defaults to copy the present value, which is the numeric id.\r\n * @property {Function} [bytes] Bytes value conversion type.\r\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\r\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\r\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\r\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\r\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\r\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\r\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\r\n */\r\n\r\n/**\r\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\r\n * @param {Message<{}>} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n */\r\nType.prototype.toObject = function toObject(message, options) {\r\n    return this.setup().toObject(message, options);\r\n};\r\n\r\n/**\r\n * Decorator function as returned by {@link Type.d} (TypeScript).\r\n * @typedef TypeDecorator\r\n * @type {function}\r\n * @param {Constructor<T>} target Target constructor\r\n * @returns {undefined}\r\n * @template T extends Message<T>\r\n */\r\n\r\n/**\r\n * Type decorator (TypeScript).\r\n * @param {string} [typeName] Type name, defaults to the constructor's name\r\n * @returns {TypeDecorator<T>} Decorator function\r\n * @template T extends Message<T>\r\n */\r\nType.d = function decorateType(typeName) {\r\n    return function typeDecorator(target) {\r\n        util.decorateType(target, typeName);\r\n    };\r\n};\r\n", "\"use strict\";\r\n\r\n/**\r\n * Common type constants.\r\n * @namespace\r\n */\r\nvar types = exports;\r\n\r\nvar util = require(33);\r\n\r\nvar s = [\r\n    \"double\",   // 0\r\n    \"float\",    // 1\r\n    \"int32\",    // 2\r\n    \"uint32\",   // 3\r\n    \"sint32\",   // 4\r\n    \"fixed32\",  // 5\r\n    \"sfixed32\", // 6\r\n    \"int64\",    // 7\r\n    \"uint64\",   // 8\r\n    \"sint64\",   // 9\r\n    \"fixed64\",  // 10\r\n    \"sfixed64\", // 11\r\n    \"bool\",     // 12\r\n    \"string\",   // 13\r\n    \"bytes\"     // 14\r\n];\r\n\r\nfunction bake(values, offset) {\r\n    var i = 0, o = {};\r\n    offset |= 0;\r\n    while (i < values.length) o[s[i + offset]] = values[i++];\r\n    return o;\r\n}\r\n\r\n/**\r\n * Basic type wire types.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} double=1 Fixed64 wire type\r\n * @property {number} float=5 Fixed32 wire type\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n * @property {number} string=2 Ldelim wire type\r\n * @property {number} bytes=2 Ldelim wire type\r\n */\r\ntypes.basic = bake([\r\n    /* double   */ 1,\r\n    /* float    */ 5,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0,\r\n    /* string   */ 2,\r\n    /* bytes    */ 2\r\n]);\r\n\r\n/**\r\n * Basic type defaults.\r\n * @type {Object.<string,*>}\r\n * @const\r\n * @property {number} double=0 Double default\r\n * @property {number} float=0 Float default\r\n * @property {number} int32=0 Int32 default\r\n * @property {number} uint32=0 Uint32 default\r\n * @property {number} sint32=0 Sint32 default\r\n * @property {number} fixed32=0 Fixed32 default\r\n * @property {number} sfixed32=0 Sfixed32 default\r\n * @property {number} int64=0 Int64 default\r\n * @property {number} uint64=0 Uint64 default\r\n * @property {number} sint64=0 Sint32 default\r\n * @property {number} fixed64=0 Fixed64 default\r\n * @property {number} sfixed64=0 Sfixed64 default\r\n * @property {boolean} bool=false Bool default\r\n * @property {string} string=\"\" String default\r\n * @property {Array.<number>} bytes=Array(0) Bytes default\r\n * @property {null} message=null Message default\r\n */\r\ntypes.defaults = bake([\r\n    /* double   */ 0,\r\n    /* float    */ 0,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 0,\r\n    /* sfixed32 */ 0,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 0,\r\n    /* sfixed64 */ 0,\r\n    /* bool     */ false,\r\n    /* string   */ \"\",\r\n    /* bytes    */ util.emptyArray,\r\n    /* message  */ null\r\n]);\r\n\r\n/**\r\n * Basic long type wire types.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n */\r\ntypes.long = bake([\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1\r\n], 7);\r\n\r\n/**\r\n * Allowed types for map keys with their associated wire type.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n * @property {number} string=2 Ldelim wire type\r\n */\r\ntypes.mapKey = bake([\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0,\r\n    /* string   */ 2\r\n], 2);\r\n\r\n/**\r\n * Allowed types for packed repeated fields with their associated wire type.\r\n * @type {Object.<string,number>}\r\n * @const\r\n * @property {number} double=1 Fixed64 wire type\r\n * @property {number} float=5 Fixed32 wire type\r\n * @property {number} int32=0 Varint wire type\r\n * @property {number} uint32=0 Varint wire type\r\n * @property {number} sint32=0 Varint wire type\r\n * @property {number} fixed32=5 Fixed32 wire type\r\n * @property {number} sfixed32=5 Fixed32 wire type\r\n * @property {number} int64=0 Varint wire type\r\n * @property {number} uint64=0 Varint wire type\r\n * @property {number} sint64=0 Varint wire type\r\n * @property {number} fixed64=1 Fixed64 wire type\r\n * @property {number} sfixed64=1 Fixed64 wire type\r\n * @property {number} bool=0 Varint wire type\r\n */\r\ntypes.packed = bake([\r\n    /* double   */ 1,\r\n    /* float    */ 5,\r\n    /* int32    */ 0,\r\n    /* uint32   */ 0,\r\n    /* sint32   */ 0,\r\n    /* fixed32  */ 5,\r\n    /* sfixed32 */ 5,\r\n    /* int64    */ 0,\r\n    /* uint64   */ 0,\r\n    /* sint64   */ 0,\r\n    /* fixed64  */ 1,\r\n    /* sfixed64 */ 1,\r\n    /* bool     */ 0\r\n]);\r\n", "\"use strict\";\r\n\r\n/**\r\n * Various utility functions.\r\n * @namespace\r\n */\r\nvar util = module.exports = require(35);\r\n\r\nvar roots = require(27);\r\n\r\nvar Type, // cyclic\r\n    Enum;\r\n\r\nutil.codegen = require(3);\r\nutil.fetch   = require(5);\r\nutil.path    = require(8);\r\n\r\n/**\r\n * Node's fs module if available.\r\n * @type {Object.<string,*>}\r\n */\r\nutil.fs = util.inquire(\"fs\");\r\n\r\n/**\r\n * Converts an object's values to an array.\r\n * @param {Object.<string,*>} object Object to convert\r\n * @returns {Array.<*>} Converted array\r\n */\r\nutil.toArray = function toArray(object) {\r\n    if (object) {\r\n        var keys  = Object.keys(object),\r\n            array = new Array(keys.length),\r\n            index = 0;\r\n        while (index < keys.length)\r\n            array[index] = object[keys[index++]];\r\n        return array;\r\n    }\r\n    return [];\r\n};\r\n\r\n/**\r\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\r\n * @param {Array.<*>} array Array to convert\r\n * @returns {Object.<string,*>} Converted object\r\n */\r\nutil.toObject = function toObject(array) {\r\n    var object = {},\r\n        index  = 0;\r\n    while (index < array.length) {\r\n        var key = array[index++],\r\n            val = array[index++];\r\n        if (val !== undefined)\r\n            object[key] = val;\r\n    }\r\n    return object;\r\n};\r\n\r\nvar safePropBackslashRe = /\\\\/g,\r\n    safePropQuoteRe     = /\"/g;\r\n\r\n/**\r\n * Tests whether the specified name is a reserved word in JS.\r\n * @param {string} name Name to test\r\n * @returns {boolean} `true` if reserved, otherwise `false`\r\n */\r\nutil.isReserved = function isReserved(name) {\r\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\r\n};\r\n\r\n/**\r\n * Returns a safe property accessor for the specified property name.\r\n * @param {string} prop Property name\r\n * @returns {string} Safe accessor\r\n */\r\nutil.safeProp = function safeProp(prop) {\r\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\r\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\r\n    return \".\" + prop;\r\n};\r\n\r\n/**\r\n * Converts the first character of a string to upper case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.ucFirst = function ucFirst(str) {\r\n    return str.charAt(0).toUpperCase() + str.substring(1);\r\n};\r\n\r\nvar camelCaseRe = /_([a-z])/g;\r\n\r\n/**\r\n * Converts a string to camel case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.camelCase = function camelCase(str) {\r\n    return str.substring(0, 1)\r\n         + str.substring(1)\r\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\r\n};\r\n\r\n/**\r\n * Compares reflected fields by id.\r\n * @param {Field} a First field\r\n * @param {Field} b Second field\r\n * @returns {number} Comparison value\r\n */\r\nutil.compareFieldsById = function compareFieldsById(a, b) {\r\n    return a.id - b.id;\r\n};\r\n\r\n/**\r\n * Decorator helper for types (TypeScript).\r\n * @param {Constructor<T>} ctor Constructor function\r\n * @param {string} [typeName] Type name, defaults to the constructor's name\r\n * @returns {Type} Reflected type\r\n * @template T extends Message<T>\r\n * @property {Root} root Decorators root\r\n */\r\nutil.decorateType = function decorateType(ctor, typeName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (ctor.$type) {\r\n        if (typeName && ctor.$type.name !== typeName) {\r\n            util.decorateRoot.remove(ctor.$type);\r\n            ctor.$type.name = typeName;\r\n            util.decorateRoot.add(ctor.$type);\r\n        }\r\n        return ctor.$type;\r\n    }\r\n\r\n    /* istanbul ignore next */\r\n    if (!Type)\r\n        Type = require(31);\r\n\r\n    var type = new Type(typeName || ctor.name);\r\n    util.decorateRoot.add(type);\r\n    type.ctor = ctor; // sets up .encode, .decode etc.\r\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\r\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\r\n    return type;\r\n};\r\n\r\nvar decorateEnumIndex = 0;\r\n\r\n/**\r\n * Decorator helper for enums (TypeScript).\r\n * @param {Object} object Enum object\r\n * @returns {Enum} Reflected enum\r\n */\r\nutil.decorateEnum = function decorateEnum(object) {\r\n\r\n    /* istanbul ignore if */\r\n    if (object.$type)\r\n        return object.$type;\r\n\r\n    /* istanbul ignore next */\r\n    if (!Enum)\r\n        Enum = require(14);\r\n\r\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\r\n    util.decorateRoot.add(enm);\r\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\r\n    return enm;\r\n};\r\n\r\n/**\r\n * Decorator root (TypeScript).\r\n * @name util.decorateRoot\r\n * @type {Root}\r\n * @readonly\r\n */\r\nObject.defineProperty(util, \"decorateRoot\", {\r\n    get: function() {\r\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(26))());\r\n    }\r\n});\r\n", "\"use strict\";\r\nmodule.exports = LongBits;\r\n\r\nvar util = require(35);\r\n\r\n/**\r\n * Constructs new long bits.\r\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\r\n * @memberof util\r\n * @constructor\r\n * @param {number} lo Low 32 bits, unsigned\r\n * @param {number} hi High 32 bits, unsigned\r\n */\r\nfunction LongBits(lo, hi) {\r\n\r\n    // note that the casts below are theoretically unnecessary as of today, but older statically\r\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\r\n\r\n    /**\r\n     * Low bits.\r\n     * @type {number}\r\n     */\r\n    this.lo = lo >>> 0;\r\n\r\n    /**\r\n     * High bits.\r\n     * @type {number}\r\n     */\r\n    this.hi = hi >>> 0;\r\n}\r\n\r\n/**\r\n * Zero bits.\r\n * @memberof util.LongBits\r\n * @type {util.LongBits}\r\n */\r\nvar zero = LongBits.zero = new LongBits(0, 0);\r\n\r\nzero.toNumber = function() { return 0; };\r\nzero.zzEncode = zero.zzDecode = function() { return this; };\r\nzero.length = function() { return 1; };\r\n\r\n/**\r\n * Zero hash.\r\n * @memberof util.LongBits\r\n * @type {string}\r\n */\r\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\r\n\r\n/**\r\n * Constructs new long bits from the specified number.\r\n * @param {number} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.fromNumber = function fromNumber(value) {\r\n    if (value === 0)\r\n        return zero;\r\n    var sign = value < 0;\r\n    if (sign)\r\n        value = -value;\r\n    var lo = value >>> 0,\r\n        hi = (value - lo) / 4294967296 >>> 0;\r\n    if (sign) {\r\n        hi = ~hi >>> 0;\r\n        lo = ~lo >>> 0;\r\n        if (++lo > 4294967295) {\r\n            lo = 0;\r\n            if (++hi > 4294967295)\r\n                hi = 0;\r\n        }\r\n    }\r\n    return new LongBits(lo, hi);\r\n};\r\n\r\n/**\r\n * Constructs new long bits from a number, long or string.\r\n * @param {Long|number|string} value Value\r\n * @returns {util.LongBits} Instance\r\n */\r\nLongBits.from = function from(value) {\r\n    if (typeof value === \"number\")\r\n        return LongBits.fromNumber(value);\r\n    if (util.isString(value)) {\r\n        /* istanbul ignore else */\r\n        if (util.Long)\r\n            value = util.Long.fromString(value);\r\n        else\r\n            return LongBits.fromNumber(parseInt(value, 10));\r\n    }\r\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a possibly unsafe JavaScript number.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {number} Possibly unsafe number\r\n */\r\nLongBits.prototype.toNumber = function toNumber(unsigned) {\r\n    if (!unsigned && this.hi >>> 31) {\r\n        var lo = ~this.lo + 1 >>> 0,\r\n            hi = ~this.hi     >>> 0;\r\n        if (!lo)\r\n            hi = hi + 1 >>> 0;\r\n        return -(lo + hi * 4294967296);\r\n    }\r\n    return this.lo + this.hi * 4294967296;\r\n};\r\n\r\n/**\r\n * Converts this long bits to a long.\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long} Long\r\n */\r\nLongBits.prototype.toLong = function toLong(unsigned) {\r\n    return util.Long\r\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\r\n        /* istanbul ignore next */\r\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\r\n};\r\n\r\nvar charCodeAt = String.prototype.charCodeAt;\r\n\r\n/**\r\n * Constructs new long bits from the specified 8 characters long hash.\r\n * @param {string} hash Hash\r\n * @returns {util.LongBits} Bits\r\n */\r\nLongBits.fromHash = function fromHash(hash) {\r\n    if (hash === zeroHash)\r\n        return zero;\r\n    return new LongBits(\r\n        ( charCodeAt.call(hash, 0)\r\n        | charCodeAt.call(hash, 1) << 8\r\n        | charCodeAt.call(hash, 2) << 16\r\n        | charCodeAt.call(hash, 3) << 24) >>> 0\r\n    ,\r\n        ( charCodeAt.call(hash, 4)\r\n        | charCodeAt.call(hash, 5) << 8\r\n        | charCodeAt.call(hash, 6) << 16\r\n        | charCodeAt.call(hash, 7) << 24) >>> 0\r\n    );\r\n};\r\n\r\n/**\r\n * Converts this long bits to a 8 characters long hash.\r\n * @returns {string} Hash\r\n */\r\nLongBits.prototype.toHash = function toHash() {\r\n    return String.fromCharCode(\r\n        this.lo        & 255,\r\n        this.lo >>> 8  & 255,\r\n        this.lo >>> 16 & 255,\r\n        this.lo >>> 24      ,\r\n        this.hi        & 255,\r\n        this.hi >>> 8  & 255,\r\n        this.hi >>> 16 & 255,\r\n        this.hi >>> 24\r\n    );\r\n};\r\n\r\n/**\r\n * Zig-zag encodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzEncode = function zzEncode() {\r\n    var mask =   this.hi >> 31;\r\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\r\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Zig-zag decodes this long bits.\r\n * @returns {util.LongBits} `this`\r\n */\r\nLongBits.prototype.zzDecode = function zzDecode() {\r\n    var mask = -(this.lo & 1);\r\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\r\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Calculates the length of this longbits when encoded as a varint.\r\n * @returns {number} Length\r\n */\r\nLongBits.prototype.length = function length() {\r\n    var part0 =  this.lo,\r\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\r\n        part2 =  this.hi >>> 24;\r\n    return part2 === 0\r\n         ? part1 === 0\r\n           ? part0 < 16384\r\n             ? part0 < 128 ? 1 : 2\r\n             : part0 < 2097152 ? 3 : 4\r\n           : part1 < 16384\r\n             ? part1 < 128 ? 5 : 6\r\n             : part1 < 2097152 ? 7 : 8\r\n         : part2 < 128 ? 9 : 10;\r\n};\r\n", "\"use strict\";\r\nvar util = exports;\r\n\r\n// used to return a Promise where callback is omitted\r\nutil.asPromise = require(1);\r\n\r\n// converts to / from base64 encoded strings\r\nutil.base64 = require(2);\r\n\r\n// base class of rpc.Service\r\nutil.EventEmitter = require(4);\r\n\r\n// float handling accross browsers\r\nutil.float = require(6);\r\n\r\n// requires modules optionally and hides the call from bundlers\r\nutil.inquire = require(7);\r\n\r\n// converts to / from utf8 encoded strings\r\nutil.utf8 = require(10);\r\n\r\n// provides a node-like buffer pool in the browser\r\nutil.pool = require(9);\r\n\r\n// utility to work with the low and high bits of a 64 bit value\r\nutil.LongBits = require(34);\r\n\r\n// global object reference\r\nutil.global = typeof window !== \"undefined\" && window\r\n           || typeof global !== \"undefined\" && global\r\n           || typeof self   !== \"undefined\" && self\r\n           || this; // eslint-disable-line no-invalid-this\r\n\r\n/**\r\n * An immuable empty array.\r\n * @memberof util\r\n * @type {Array.<*>}\r\n * @const\r\n */\r\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\r\n\r\n/**\r\n * An immutable empty object.\r\n * @type {Object}\r\n * @const\r\n */\r\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\r\n\r\n/**\r\n * Whether running within node or not.\r\n * @memberof util\r\n * @type {boolean}\r\n * @const\r\n */\r\nutil.isNode = Boolean(util.global.process && util.global.process.versions && util.global.process.versions.node);\r\n\r\n/**\r\n * Tests if the specified value is an integer.\r\n * @function\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is an integer\r\n */\r\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\r\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a string.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a string\r\n */\r\nutil.isString = function isString(value) {\r\n    return typeof value === \"string\" || value instanceof String;\r\n};\r\n\r\n/**\r\n * Tests if the specified value is a non-null object.\r\n * @param {*} value Value to test\r\n * @returns {boolean} `true` if the value is a non-null object\r\n */\r\nutil.isObject = function isObject(value) {\r\n    return value && typeof value === \"object\";\r\n};\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * This is an alias of {@link util.isSet}.\r\n * @function\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isset =\r\n\r\n/**\r\n * Checks if a property on a message is considered to be present.\r\n * @param {Object} obj Plain object or message instance\r\n * @param {string} prop Property name\r\n * @returns {boolean} `true` if considered to be present, otherwise `false`\r\n */\r\nutil.isSet = function isSet(obj, prop) {\r\n    var value = obj[prop];\r\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\r\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\r\n    return false;\r\n};\r\n\r\n/**\r\n * Any compatible Buffer instance.\r\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\r\n * @interface Buffer\r\n * @extends Uint8Array\r\n */\r\n\r\n/**\r\n * Node's Buffer class if available.\r\n * @type {Constructor<Buffer>}\r\n */\r\nutil.Buffer = (function() {\r\n    try {\r\n        var Buffer = util.inquire(\"buffer\").Buffer;\r\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\r\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\r\n    } catch (e) {\r\n        /* istanbul ignore next */\r\n        return null;\r\n    }\r\n})();\r\n\r\n// Internal alias of or polyfull for Buffer.from.\r\nutil._Buffer_from = null;\r\n\r\n// Internal alias of or polyfill for Buffer.allocUnsafe.\r\nutil._Buffer_allocUnsafe = null;\r\n\r\n/**\r\n * Creates a new buffer of whatever type supported by the environment.\r\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\r\n * @returns {Uint8Array|Buffer} Buffer\r\n */\r\nutil.newBuffer = function newBuffer(sizeOrArray) {\r\n    /* istanbul ignore next */\r\n    return typeof sizeOrArray === \"number\"\r\n        ? util.Buffer\r\n            ? util._Buffer_allocUnsafe(sizeOrArray)\r\n            : new util.Array(sizeOrArray)\r\n        : util.Buffer\r\n            ? util._Buffer_from(sizeOrArray)\r\n            : typeof Uint8Array === \"undefined\"\r\n                ? sizeOrArray\r\n                : new Uint8Array(sizeOrArray);\r\n};\r\n\r\n/**\r\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\r\n * @type {Constructor<Uint8Array>}\r\n */\r\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\r\n\r\n/**\r\n * Any compatible Long instance.\r\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\r\n * @interface Long\r\n * @property {number} low Low bits\r\n * @property {number} high High bits\r\n * @property {boolean} unsigned Whether unsigned or not\r\n */\r\n\r\n/**\r\n * Long.js's Long class if available.\r\n * @type {Constructor<Long>}\r\n */\r\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\r\n         || /* istanbul ignore next */ util.global.Long\r\n         || util.inquire(\"long\");\r\n\r\n/**\r\n * Regular expression used to verify 2 bit (`bool`) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key2Re = /^true|false|0|1$/;\r\n\r\n/**\r\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\r\n\r\n/**\r\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\r\n * @type {RegExp}\r\n * @const\r\n */\r\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\r\n\r\n/**\r\n * Converts a number or long to an 8 characters long hash string.\r\n * @param {Long|number} value Value to convert\r\n * @returns {string} Hash\r\n */\r\nutil.longToHash = function longToHash(value) {\r\n    return value\r\n        ? util.LongBits.from(value).toHash()\r\n        : util.LongBits.zeroHash;\r\n};\r\n\r\n/**\r\n * Converts an 8 characters long hash string to a long or number.\r\n * @param {string} hash Hash\r\n * @param {boolean} [unsigned=false] Whether unsigned or not\r\n * @returns {Long|number} Original value\r\n */\r\nutil.longFromHash = function longFromHash(hash, unsigned) {\r\n    var bits = util.LongBits.fromHash(hash);\r\n    if (util.Long)\r\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\r\n    return bits.toNumber(Boolean(unsigned));\r\n};\r\n\r\n/**\r\n * Merges the properties of the source object into the destination object.\r\n * @memberof util\r\n * @param {Object.<string,*>} dst Destination object\r\n * @param {Object.<string,*>} src Source object\r\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\r\n * @returns {Object.<string,*>} Destination object\r\n */\r\nfunction merge(dst, src, ifNotSet) { // used by converters\r\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\r\n        if (dst[keys[i]] === undefined || !ifNotSet)\r\n            dst[keys[i]] = src[keys[i]];\r\n    return dst;\r\n}\r\n\r\nutil.merge = merge;\r\n\r\n/**\r\n * Converts the first character of a string to lower case.\r\n * @param {string} str String to convert\r\n * @returns {string} Converted string\r\n */\r\nutil.lcFirst = function lcFirst(str) {\r\n    return str.charAt(0).toLowerCase() + str.substring(1);\r\n};\r\n\r\n/**\r\n * Creates a custom error constructor.\r\n * @memberof util\r\n * @param {string} name Error name\r\n * @returns {Constructor<Error>} Custom error constructor\r\n */\r\nfunction newError(name) {\r\n\r\n    function CustomError(message, properties) {\r\n\r\n        if (!(this instanceof CustomError))\r\n            return new CustomError(message, properties);\r\n\r\n        // Error.call(this, message);\r\n        // ^ just returns a new error instance because the ctor can be called as a function\r\n\r\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\r\n\r\n        /* istanbul ignore next */\r\n        if (Error.captureStackTrace) // node\r\n            Error.captureStackTrace(this, CustomError);\r\n        else\r\n            Object.defineProperty(this, \"stack\", { value: (new Error()).stack || \"\" });\r\n\r\n        if (properties)\r\n            merge(this, properties);\r\n    }\r\n\r\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\r\n\r\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\r\n\r\n    CustomError.prototype.toString = function toString() {\r\n        return this.name + \": \" + this.message;\r\n    };\r\n\r\n    return CustomError;\r\n}\r\n\r\nutil.newError = newError;\r\n\r\n/**\r\n * Constructs a new protocol error.\r\n * @classdesc Error subclass indicating a protocol specifc error.\r\n * @memberof util\r\n * @extends Error\r\n * @template T extends Message<T>\r\n * @constructor\r\n * @param {string} message Error message\r\n * @param {Object.<string,*>} [properties] Additional properties\r\n * @example\r\n * try {\r\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\r\n * } catch (e) {\r\n *     if (e instanceof ProtocolError && e.instance)\r\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\r\n * }\r\n */\r\nutil.ProtocolError = newError(\"ProtocolError\");\r\n\r\n/**\r\n * So far decoded message instance.\r\n * @name util.ProtocolError#instance\r\n * @type {Message<T>}\r\n */\r\n\r\n/**\r\n * A OneOf getter as returned by {@link util.oneOfGetter}.\r\n * @typedef OneOfGetter\r\n * @type {function}\r\n * @returns {string|undefined} Set field name, if any\r\n */\r\n\r\n/**\r\n * Builds a getter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfGetter} Unbound getter\r\n */\r\nutil.oneOfGetter = function getOneOf(fieldNames) {\r\n    var fieldMap = {};\r\n    for (var i = 0; i < fieldNames.length; ++i)\r\n        fieldMap[fieldNames[i]] = 1;\r\n\r\n    /**\r\n     * @returns {string|undefined} Set field name, if any\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function() { // eslint-disable-line consistent-return\r\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\r\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\r\n                return keys[i];\r\n    };\r\n};\r\n\r\n/**\r\n * A OneOf setter as returned by {@link util.oneOfSetter}.\r\n * @typedef OneOfSetter\r\n * @type {function}\r\n * @param {string|undefined} value Field name\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Builds a setter for a oneof's present field name.\r\n * @param {string[]} fieldNames Field names\r\n * @returns {OneOfSetter} Unbound setter\r\n */\r\nutil.oneOfSetter = function setOneOf(fieldNames) {\r\n\r\n    /**\r\n     * @param {string} name Field name\r\n     * @returns {undefined}\r\n     * @this Object\r\n     * @ignore\r\n     */\r\n    return function(name) {\r\n        for (var i = 0; i < fieldNames.length; ++i)\r\n            if (fieldNames[i] !== name)\r\n                delete this[fieldNames[i]];\r\n    };\r\n};\r\n\r\n/**\r\n * Default conversion options used for {@link Message#toJSON} implementations.\r\n *\r\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\r\n *\r\n * - Longs become strings\r\n * - Enums become string keys\r\n * - Bytes become base64 encoded strings\r\n * - (Sub-)Messages become plain objects\r\n * - Maps become plain objects with all string keys\r\n * - Repeated fields become arrays\r\n * - NaN and Infinity for float and double fields become strings\r\n *\r\n * @type {IConversionOptions}\r\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\r\n */\r\nutil.toJSONOptions = {\r\n    longs: String,\r\n    enums: String,\r\n    bytes: String,\r\n    json: true\r\n};\r\n\r\n// Sets up buffer utility according to the environment (called in index-minimal)\r\nutil._configure = function() {\r\n    var Buffer = util.Buffer;\r\n    /* istanbul ignore if */\r\n    if (!Buffer) {\r\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\r\n        return;\r\n    }\r\n    // because node 4.x buffers are incompatible & immutable\r\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\r\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\r\n        /* istanbul ignore next */\r\n        function Buffer_from(value, encoding) {\r\n            return new Buffer(value, encoding);\r\n        };\r\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\r\n        /* istanbul ignore next */\r\n        function Buffer_allocUnsafe(size) {\r\n            return new Buffer(size);\r\n        };\r\n};\r\n", "\"use strict\";\r\nmodule.exports = verifier;\r\n\r\nvar Enum      = require(14),\r\n    util      = require(33);\r\n\r\nfunction invalid(field, expected) {\r\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\r\n}\r\n\r\n/**\r\n * Generates a partial value verifier.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {number} fieldIndex Field index\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    if (field.resolvedType) {\r\n        if (field.resolvedType instanceof Enum) { gen\r\n            (\"switch(%s){\", ref)\r\n                (\"default:\")\r\n                    (\"return%j\", invalid(field, \"enum value\"));\r\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\r\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\r\n            gen\r\n                    (\"break\")\r\n            (\"}\");\r\n        } else {\r\n            gen\r\n            (\"{\")\r\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\r\n                (\"if(e)\")\r\n                    (\"return%j+e\", field.name + \".\")\r\n            (\"}\");\r\n        }\r\n    } else {\r\n        switch (field.type) {\r\n            case \"int32\":\r\n            case \"uint32\":\r\n            case \"sint32\":\r\n            case \"fixed32\":\r\n            case \"sfixed32\": gen\r\n                (\"if(!util.isInteger(%s))\", ref)\r\n                    (\"return%j\", invalid(field, \"integer\"));\r\n                break;\r\n            case \"int64\":\r\n            case \"uint64\":\r\n            case \"sint64\":\r\n            case \"fixed64\":\r\n            case \"sfixed64\": gen\r\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\r\n                    (\"return%j\", invalid(field, \"integer|Long\"));\r\n                break;\r\n            case \"float\":\r\n            case \"double\": gen\r\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\r\n                    (\"return%j\", invalid(field, \"number\"));\r\n                break;\r\n            case \"bool\": gen\r\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\r\n                    (\"return%j\", invalid(field, \"boolean\"));\r\n                break;\r\n            case \"string\": gen\r\n                (\"if(!util.isString(%s))\", ref)\r\n                    (\"return%j\", invalid(field, \"string\"));\r\n                break;\r\n            case \"bytes\": gen\r\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\r\n                    (\"return%j\", invalid(field, \"buffer\"));\r\n                break;\r\n        }\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n\r\n/**\r\n * Generates a partial key verifier.\r\n * @param {Codegen} gen Codegen instance\r\n * @param {Field} field Reflected field\r\n * @param {string} ref Variable reference\r\n * @returns {Codegen} Codegen instance\r\n * @ignore\r\n */\r\nfunction genVerifyKey(gen, field, ref) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n    switch (field.keyType) {\r\n        case \"int32\":\r\n        case \"uint32\":\r\n        case \"sint32\":\r\n        case \"fixed32\":\r\n        case \"sfixed32\": gen\r\n            (\"if(!util.key32Re.test(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"integer key\"));\r\n            break;\r\n        case \"int64\":\r\n        case \"uint64\":\r\n        case \"sint64\":\r\n        case \"fixed64\":\r\n        case \"sfixed64\": gen\r\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\r\n                (\"return%j\", invalid(field, \"integer|Long key\"));\r\n            break;\r\n        case \"bool\": gen\r\n            (\"if(!util.key2Re.test(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"boolean key\"));\r\n            break;\r\n    }\r\n    return gen;\r\n    /* eslint-enable no-unexpected-multiline */\r\n}\r\n\r\n/**\r\n * Generates a verifier specific to the specified message type.\r\n * @param {Type} mtype Message type\r\n * @returns {Codegen} Codegen instance\r\n */\r\nfunction verifier(mtype) {\r\n    /* eslint-disable no-unexpected-multiline */\r\n\r\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\r\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\r\n        (\"return%j\", \"object expected\");\r\n    var oneofs = mtype.oneofsArray,\r\n        seenFirstField = {};\r\n    if (oneofs.length) gen\r\n    (\"var p={}\");\r\n\r\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\r\n        var field = mtype._fieldsArray[i].resolve(),\r\n            ref   = \"m\" + util.safeProp(field.name);\r\n\r\n        if (field.optional) gen\r\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\r\n\r\n        // map fields\r\n        if (field.map) { gen\r\n            (\"if(!util.isObject(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"object\"))\r\n            (\"var k=Object.keys(%s)\", ref)\r\n            (\"for(var i=0;i<k.length;++i){\");\r\n                genVerifyKey(gen, field, \"k[i]\");\r\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\r\n            (\"}\");\r\n\r\n        // repeated fields\r\n        } else if (field.repeated) { gen\r\n            (\"if(!Array.isArray(%s))\", ref)\r\n                (\"return%j\", invalid(field, \"array\"))\r\n            (\"for(var i=0;i<%s.length;++i){\", ref);\r\n                genVerifyValue(gen, field, i, ref + \"[i]\")\r\n            (\"}\");\r\n\r\n        // required or present fields\r\n        } else {\r\n            if (field.partOf) {\r\n                var oneofProp = util.safeProp(field.partOf.name);\r\n                if (seenFirstField[field.partOf.name] === 1) gen\r\n            (\"if(p%s===1)\", oneofProp)\r\n                (\"return%j\", field.partOf.name + \": multiple values\");\r\n                seenFirstField[field.partOf.name] = 1;\r\n                gen\r\n            (\"p%s=1\", oneofProp);\r\n            }\r\n            genVerifyValue(gen, field, i, ref);\r\n        }\r\n        if (field.optional) gen\r\n        (\"}\");\r\n    }\r\n    return gen\r\n    (\"return null\");\r\n    /* eslint-enable no-unexpected-multiline */\r\n}", "\"use strict\";\r\n\r\n/**\r\n * Wrappers for common types.\r\n * @type {Object.<string,IWrapper>}\r\n * @const\r\n */\r\nvar wrappers = exports;\r\n\r\nvar Message = require(19);\r\n\r\n/**\r\n * From object converter part of an {@link IWrapper}.\r\n * @typedef WrapperFromObjectConverter\r\n * @type {function}\r\n * @param {Object.<string,*>} object Plain object\r\n * @returns {Message<{}>} Message instance\r\n * @this Type\r\n */\r\n\r\n/**\r\n * To object converter part of an {@link IWrapper}.\r\n * @typedef WrapperToObjectConverter\r\n * @type {function}\r\n * @param {Message<{}>} message Message instance\r\n * @param {IConversionOptions} [options] Conversion options\r\n * @returns {Object.<string,*>} Plain object\r\n * @this Type\r\n */\r\n\r\n/**\r\n * Common type wrapper part of {@link wrappers}.\r\n * @interface IWrapper\r\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\r\n * @property {WrapperToObjectConverter} [toObject] To object converter\r\n */\r\n\r\n// Custom wrapper for Any\r\nwrappers[\".google.protobuf.Any\"] = {\r\n\r\n    fromObject: function(object) {\r\n\r\n        // unwrap value type if mapped\r\n        if (object && object[\"@type\"]) {\r\n            var type = this.lookup(object[\"@type\"]);\r\n            /* istanbul ignore else */\r\n            if (type) {\r\n                // type_url does not accept leading \".\"\r\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\r\n                    object[\"@type\"].substr(1) : object[\"@type\"];\r\n                // type_url prefix is optional, but path seperator is required\r\n                return this.create({\r\n                    type_url: \"/\" + type_url,\r\n                    value: type.encode(type.fromObject(object)).finish()\r\n                });\r\n            }\r\n        }\r\n\r\n        return this.fromObject(object);\r\n    },\r\n\r\n    toObject: function(message, options) {\r\n\r\n        // decode value if requested and unmapped\r\n        if (options && options.json && message.type_url && message.value) {\r\n            // Only use fully qualified type name after the last '/'\r\n            var name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\r\n            var type = this.lookup(name);\r\n            /* istanbul ignore else */\r\n            if (type)\r\n                message = type.decode(message.value);\r\n        }\r\n\r\n        // wrap value if unmapped\r\n        if (!(message instanceof this.ctor) && message instanceof Message) {\r\n            var object = message.$type.toObject(message, options);\r\n            object[\"@type\"] = message.$type.fullName;\r\n            return object;\r\n        }\r\n\r\n        return this.toObject(message, options);\r\n    }\r\n};\r\n", "\"use strict\";\r\nmodule.exports = Writer;\r\n\r\nvar util      = require(35);\r\n\r\nvar BufferWriter; // cyclic\r\n\r\nvar LongBits  = util.LongBits,\r\n    base64    = util.base64,\r\n    utf8      = util.utf8;\r\n\r\n/**\r\n * Constructs a new writer operation instance.\r\n * @classdesc Scheduled writer operation.\r\n * @constructor\r\n * @param {function(*, Uint8Array, number)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {*} val Value to write\r\n * @ignore\r\n */\r\nfunction Op(fn, len, val) {\r\n\r\n    /**\r\n     * Function to call.\r\n     * @type {function(Uint8Array, number, *)}\r\n     */\r\n    this.fn = fn;\r\n\r\n    /**\r\n     * Value byte length.\r\n     * @type {number}\r\n     */\r\n    this.len = len;\r\n\r\n    /**\r\n     * Next operation.\r\n     * @type {Writer.Op|undefined}\r\n     */\r\n    this.next = undefined;\r\n\r\n    /**\r\n     * Value to write.\r\n     * @type {*}\r\n     */\r\n    this.val = val; // type varies\r\n}\r\n\r\n/* istanbul ignore next */\r\nfunction noop() {} // eslint-disable-line no-empty-function\r\n\r\n/**\r\n * Constructs a new writer state instance.\r\n * @classdesc Copied writer state.\r\n * @memberof Writer\r\n * @constructor\r\n * @param {Writer} writer Writer to copy state from\r\n * @ignore\r\n */\r\nfunction State(writer) {\r\n\r\n    /**\r\n     * Current head.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.head = writer.head;\r\n\r\n    /**\r\n     * Current tail.\r\n     * @type {Writer.Op}\r\n     */\r\n    this.tail = writer.tail;\r\n\r\n    /**\r\n     * Current buffer length.\r\n     * @type {number}\r\n     */\r\n    this.len = writer.len;\r\n\r\n    /**\r\n     * Next state.\r\n     * @type {State|null}\r\n     */\r\n    this.next = writer.states;\r\n}\r\n\r\n/**\r\n * Constructs a new writer instance.\r\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\r\n * @constructor\r\n */\r\nfunction Writer() {\r\n\r\n    /**\r\n     * Current length.\r\n     * @type {number}\r\n     */\r\n    this.len = 0;\r\n\r\n    /**\r\n     * Operations head.\r\n     * @type {Object}\r\n     */\r\n    this.head = new Op(noop, 0, 0);\r\n\r\n    /**\r\n     * Operations tail\r\n     * @type {Object}\r\n     */\r\n    this.tail = this.head;\r\n\r\n    /**\r\n     * Linked forked states.\r\n     * @type {Object|null}\r\n     */\r\n    this.states = null;\r\n\r\n    // When a value is written, the writer calculates its byte length and puts it into a linked\r\n    // list of operations to perform when finish() is called. This both allows us to allocate\r\n    // buffers of the exact required size and reduces the amount of work we have to do compared\r\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\r\n    // part is just a linked list walk calling operations with already prepared values.\r\n}\r\n\r\n/**\r\n * Creates a new writer.\r\n * @function\r\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\r\n */\r\nWriter.create = util.Buffer\r\n    ? function create_buffer_setup() {\r\n        return (Writer.create = function create_buffer() {\r\n            return new BufferWriter();\r\n        })();\r\n    }\r\n    /* istanbul ignore next */\r\n    : function create_array() {\r\n        return new Writer();\r\n    };\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\nWriter.alloc = function alloc(size) {\r\n    return new util.Array(size);\r\n};\r\n\r\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\r\n/* istanbul ignore else */\r\nif (util.Array !== Array)\r\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\r\n\r\n/**\r\n * Pushes a new operation to the queue.\r\n * @param {function(Uint8Array, number, *)} fn Function to call\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @returns {Writer} `this`\r\n * @private\r\n */\r\nWriter.prototype._push = function push(fn, len, val) {\r\n    this.tail = this.tail.next = new Op(fn, len, val);\r\n    this.len += len;\r\n    return this;\r\n};\r\n\r\nfunction writeByte(val, buf, pos) {\r\n    buf[pos] = val & 255;\r\n}\r\n\r\nfunction writeVarint32(val, buf, pos) {\r\n    while (val > 127) {\r\n        buf[pos++] = val & 127 | 128;\r\n        val >>>= 7;\r\n    }\r\n    buf[pos] = val;\r\n}\r\n\r\n/**\r\n * Constructs a new varint writer operation instance.\r\n * @classdesc Scheduled varint writer operation.\r\n * @extends Op\r\n * @constructor\r\n * @param {number} len Value byte length\r\n * @param {number} val Value to write\r\n * @ignore\r\n */\r\nfunction VarintOp(len, val) {\r\n    this.len = len;\r\n    this.next = undefined;\r\n    this.val = val;\r\n}\r\n\r\nVarintOp.prototype = Object.create(Op.prototype);\r\nVarintOp.prototype.fn = writeVarint32;\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as a varint.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.uint32 = function write_uint32(value) {\r\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\r\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\r\n    this.len += (this.tail = this.tail.next = new VarintOp(\r\n        (value = value >>> 0)\r\n                < 128       ? 1\r\n        : value < 16384     ? 2\r\n        : value < 2097152   ? 3\r\n        : value < 268435456 ? 4\r\n        :                     5,\r\n    value)).len;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as a varint.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.int32 = function write_int32(value) {\r\n    return value < 0\r\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\r\n        : this.uint32(value);\r\n};\r\n\r\n/**\r\n * Writes a 32 bit value as a varint, zig-zag encoded.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sint32 = function write_sint32(value) {\r\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\r\n};\r\n\r\nfunction writeVarint64(val, buf, pos) {\r\n    while (val.hi) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\r\n        val.hi >>>= 7;\r\n    }\r\n    while (val.lo > 127) {\r\n        buf[pos++] = val.lo & 127 | 128;\r\n        val.lo = val.lo >>> 7;\r\n    }\r\n    buf[pos++] = val.lo;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as a varint.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.uint64 = function write_uint64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.int64 = Writer.prototype.uint64;\r\n\r\n/**\r\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sint64 = function write_sint64(value) {\r\n    var bits = LongBits.from(value).zzEncode();\r\n    return this._push(writeVarint64, bits.length(), bits);\r\n};\r\n\r\n/**\r\n * Writes a boolish value as a varint.\r\n * @param {boolean} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bool = function write_bool(value) {\r\n    return this._push(writeByte, 1, value ? 1 : 0);\r\n};\r\n\r\nfunction writeFixed32(val, buf, pos) {\r\n    buf[pos    ] =  val         & 255;\r\n    buf[pos + 1] =  val >>> 8   & 255;\r\n    buf[pos + 2] =  val >>> 16  & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\n/**\r\n * Writes an unsigned 32 bit value as fixed 32 bits.\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fixed32 = function write_fixed32(value) {\r\n    return this._push(writeFixed32, 4, value >>> 0);\r\n};\r\n\r\n/**\r\n * Writes a signed 32 bit value as fixed 32 bits.\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\r\n\r\n/**\r\n * Writes an unsigned 64 bit value as fixed 64 bits.\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.fixed64 = function write_fixed64(value) {\r\n    var bits = LongBits.from(value);\r\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\r\n};\r\n\r\n/**\r\n * Writes a signed 64 bit value as fixed 64 bits.\r\n * @function\r\n * @param {Long|number|string} value Value to write\r\n * @returns {Writer} `this`\r\n * @throws {TypeError} If `value` is a string and no long library is present.\r\n */\r\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\r\n\r\n/**\r\n * Writes a float (32 bit).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.float = function write_float(value) {\r\n    return this._push(util.float.writeFloatLE, 4, value);\r\n};\r\n\r\n/**\r\n * Writes a double (64 bit float).\r\n * @function\r\n * @param {number} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.double = function write_double(value) {\r\n    return this._push(util.float.writeDoubleLE, 8, value);\r\n};\r\n\r\nvar writeBytes = util.Array.prototype.set\r\n    ? function writeBytes_set(val, buf, pos) {\r\n        buf.set(val, pos); // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytes_for(val, buf, pos) {\r\n        for (var i = 0; i < val.length; ++i)\r\n            buf[pos + i] = val[i];\r\n    };\r\n\r\n/**\r\n * Writes a sequence of bytes.\r\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.bytes = function write_bytes(value) {\r\n    var len = value.length >>> 0;\r\n    if (!len)\r\n        return this._push(writeByte, 1, 0);\r\n    if (util.isString(value)) {\r\n        var buf = Writer.alloc(len = base64.length(value));\r\n        base64.decode(value, buf, 0);\r\n        value = buf;\r\n    }\r\n    return this.uint32(len)._push(writeBytes, len, value);\r\n};\r\n\r\n/**\r\n * Writes a string.\r\n * @param {string} value Value to write\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.string = function write_string(value) {\r\n    var len = utf8.length(value);\r\n    return len\r\n        ? this.uint32(len)._push(utf8.write, len, value)\r\n        : this._push(writeByte, 1, 0);\r\n};\r\n\r\n/**\r\n * Forks this writer's state by pushing it to a stack.\r\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.fork = function fork() {\r\n    this.states = new State(this);\r\n    this.head = this.tail = new Op(noop, 0, 0);\r\n    this.len = 0;\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets this instance to the last state.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.reset = function reset() {\r\n    if (this.states) {\r\n        this.head   = this.states.head;\r\n        this.tail   = this.states.tail;\r\n        this.len    = this.states.len;\r\n        this.states = this.states.next;\r\n    } else {\r\n        this.head = this.tail = new Op(noop, 0, 0);\r\n        this.len  = 0;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\r\n * @returns {Writer} `this`\r\n */\r\nWriter.prototype.ldelim = function ldelim() {\r\n    var head = this.head,\r\n        tail = this.tail,\r\n        len  = this.len;\r\n    this.reset().uint32(len);\r\n    if (len) {\r\n        this.tail.next = head.next; // skip noop\r\n        this.tail = tail;\r\n        this.len += len;\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @returns {Uint8Array} Finished buffer\r\n */\r\nWriter.prototype.finish = function finish() {\r\n    var head = this.head.next, // skip noop\r\n        buf  = this.constructor.alloc(this.len),\r\n        pos  = 0;\r\n    while (head) {\r\n        head.fn(head.val, buf, pos);\r\n        pos += head.len;\r\n        head = head.next;\r\n    }\r\n    // this.head = this.tail = null;\r\n    return buf;\r\n};\r\n\r\nWriter._configure = function(BufferWriter_) {\r\n    BufferWriter = BufferWriter_;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = <PERSON><PERSON>erWriter;\r\n\r\n// extends Writer\r\nvar Writer = require(38);\r\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\r\n\r\nvar util = require(35);\r\n\r\nvar Buffer = util.Buffer;\r\n\r\n/**\r\n * Constructs a new buffer writer instance.\r\n * @classdesc Wire format writer using node buffers.\r\n * @extends Writer\r\n * @constructor\r\n */\r\nfunction BufferWriter() {\r\n    Writer.call(this);\r\n}\r\n\r\n/**\r\n * Allocates a buffer of the specified size.\r\n * @param {number} size Buffer size\r\n * @returns {Buffer} Buffer\r\n */\r\nBufferWriter.alloc = function alloc_buffer(size) {\r\n    return (BufferWriter.alloc = util._Buffer_allocUnsafe)(size);\r\n};\r\n\r\nvar writeBytesBuffer = Buffer && Buffer.prototype instanceof Uint8Array && Buffer.prototype.set.name === \"set\"\r\n    ? function writeBytesBuffer_set(val, buf, pos) {\r\n        buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\r\n                           // also works for plain array values\r\n    }\r\n    /* istanbul ignore next */\r\n    : function writeBytesBuffer_copy(val, buf, pos) {\r\n        if (val.copy) // Buffer values\r\n            val.copy(buf, pos, 0, val.length);\r\n        else for (var i = 0; i < val.length;) // plain array values\r\n            buf[pos++] = val[i++];\r\n    };\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\r\n    if (util.isString(value))\r\n        value = util._Buffer_from(value, \"base64\");\r\n    var len = value.length >>> 0;\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeBytesBuffer, len, value);\r\n    return this;\r\n};\r\n\r\nfunction writeStringBuffer(val, buf, pos) {\r\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\r\n        util.utf8.write(val, buf, pos);\r\n    else\r\n        buf.utf8Write(val, pos);\r\n}\r\n\r\n/**\r\n * @override\r\n */\r\nBufferWriter.prototype.string = function write_string_buffer(value) {\r\n    var len = Buffer.byteLength(value);\r\n    this.uint32(len);\r\n    if (len)\r\n        this._push(writeStringBuffer, len, value);\r\n    return this;\r\n};\r\n\r\n\r\n/**\r\n * Finishes the write operation.\r\n * @name BufferWriter#finish\r\n * @function\r\n * @returns {Buffer} Finished buffer\r\n */\r\n"], "sourceRoot": "."}