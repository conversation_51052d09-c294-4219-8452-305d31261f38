print("[MainCoreUI_FactionSelect_CL] Client script loaded.")

RegisterNetEvent('MainCore:client:showFactionMenu', function()
    print("[MainCoreUI_FactionSelect_CL] Event 'MainCore:client:showFactionMenu' received.")
    print("[MainCoreUI_FactionSelect_CL] Sending NUI message 'showFactionMenu'.")
    SendNUIMessage({
        action = 'showFactionMenu'
    })
    SetNuiFocus(true, true)
    print("[MainCoreUI_FactionSelect_CL] NUI focus set to true.")
end)

-- Callback NUI pour la sélection de faction
-- Ce callback est logiquement placé ici car MainCoreUI_FactionSelect possède l'interface.
RegisterNUICallback('selectFaction', function(data, cb)
    -- Utiliser une fonction de sérialisation simple si json.encode n'est pas disponible par défaut partout.
    local function basicSerialize(val)
        if type(val) == "table" then
            local parts = {}
            for k, v in pairs(val) do
                table.insert(parts, string.format("[%q]=%q", tostring(k), tostring(v)))
            end
            return "{ " .. table.concat(parts, ", ") .. " }"
        elseif type(val) == "string" then
            return string.format("%q", val)
        else
            return tostring(val)
        end
    end
    print("[MainCoreUI_FactionSelect_CL] NUI Callback: 'selectFaction' received. Data: " .. basicSerialize(data))

    local factionChosen = data.faction
    if factionChosen == 'sheriffs' or factionChosen == 'outlaws' then
        print("[MainCoreUI_FactionSelect_CL] Faction chosen: '" .. factionChosen .. "'. Triggering 'MainCore:server:joinFaction'.")
        TriggerServerEvent('MainCore:server:joinFaction', factionChosen)
    else
        print('[MainCoreUI_FactionSelect_CL] Invalid faction received from NUI: ' .. tostring(factionChosen))
    end

    print("[MainCoreUI_FactionSelect_CL] Setting NUI focus to false.")
    SetNuiFocus(false, false)
    cb({ status = 'ok' })
end)