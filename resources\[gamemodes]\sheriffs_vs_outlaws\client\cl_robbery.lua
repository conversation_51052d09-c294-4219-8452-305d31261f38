-- Variables locales pour la gestion des braquages
local CurrentRobbery = {
    isRobbing = false,
    locationId = nil,
    startTime = 0,
    timeRemaining = 0
}

-- Afficher des blips pour les lieux braquables sur la carte
Citizen.CreateThread(function()
    for i, location in ipairs(Config.RobbableLocations) do
        local blip = Citizen.InvokeNative(0x554D9D53F696D002, 1664425300, location.coords.x, location.coords.y, location.coords.z) -- BLIP_ADD_FOR_COORD
        Citizen.InvokeNative(0x74F74D3207ED525C, blip, location.blipSprite, 1) -- SET_BLIP_SPRITE
        Citizen.InvokeNative(0xD38744167B2FA257, blip, 0.8) -- SET_BLIP_SCALE
        
        -- Couleur rouge pour les lieux braquables (peut varier selon les préférences)
        Citizen.InvokeNative(0x662D364ABF16DE2F, blip, 0x801A7D6A) -- _SET_BLIP_FLASH_STYLE (rouge)
        
        -- Créer une entrée dans le radar
        Citizen.InvokeNative(0x9CB1A1623062F402, blip, location.name) -- SET_BLIP_NAME_FROM_PLAYER_STRING
    end
end)

-- Vérifier la proximité des lieux braquables et afficher un marqueur/prompt
Citizen.CreateThread(function()
    local promptGroup = GetRandomIntInRange(0, 0xffffff)
    local robberyPrompt = nil
    
    -- Créer le prompt (touche d'interaction)
    robberyPrompt = Citizen.InvokeNative(0x04F97DE45A519419) -- CREATE_PROMPT
    Citizen.InvokeNative(0xB5352B7494A08258, robberyPrompt, 0xC7B5340A) -- PROMPT_SET_CONTROL_ACTION (INPUT_DYNAMIC_SCENARIO)
    Citizen.InvokeNative(0x5DD02A8318420DD7, robberyPrompt, "Braquer") -- PROMPT_SET_TEXT
    Citizen.InvokeNative(0xF7AA2696A22AD8B9, robberyPrompt) -- PROMPT_REGISTER_END
    
    Citizen.InvokeNative(0x8A0FB4D03A630D21, promptGroup, true) -- PROMPT_SET_GROUP_ACTIVE
    Citizen.InvokeNative(0x2F11D3A254169EA4, promptGroup, robberyPrompt) -- PROMPT_SET_GROUP_ID
    
    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local isNearRobbable = false
        local closestDistance = 1000.0
        local closestLocation = nil
        local closestIndex = 0
        
        -- Vérifier la distance avec chaque lieu braquable
        for i, location in ipairs(Config.RobbableLocations) do
            local distance = #(coords - location.coords)
            if distance < closestDistance then
                closestDistance = distance
                closestLocation = location
                closestIndex = i
            end
        end
        
        -- Si le joueur est assez proche
        if closestDistance < 2.0 then
            isNearRobbable = true
            
            -- Afficher marqueur ou texte 3D
            DrawTxt(closestLocation.name, 0.5, 0.85, 0.6, 4, 255, 255, 255, 255, 1, true, true)
            
            if LocalPlayer.faction == "outlaws" then
                DrawTxt("Appuyez sur [E] pour braquer", 0.5, 0.88, 0.5, 4, 255, 255, 0, 255, 1, true, true)
                
                -- Activer le prompt
                Citizen.InvokeNative(0x8A0FB4D03A630D21, promptGroup, true) -- PROMPT_SET_GROUP_ACTIVE
                Citizen.InvokeNative(0x71215ACCFDE075EE, promptGroup, true) -- PROMPT_SET_VISIBLE

                -- Vérifier si le prompt est activé
                if Citizen.InvokeNative(0xE0F65F0640EF0617, robberyPrompt) then -- PROMPT_HAS_HOLD_MODE_COMPLETED
                    -- Vérifier si le lieu peut être braqué (cooldown, etc.)
                    TriggerServerEvent('sheriffs_vs_outlaws:checkRobbery', closestIndex)
                end
            else
                if LocalPlayer.faction == "sheriffs" then
                    DrawTxt("Vous êtes un représentant de la loi", 0.5, 0.88, 0.5, 4, 255, 0, 0, 255, 1, true, true)
                else
                    DrawTxt("Vous devez être un hors-la-loi pour braquer", 0.5, 0.88, 0.5, 4, 255, 0, 0, 255, 1, true, true)
                end
                
                -- Désactiver le prompt pour les non-outlaws
                Citizen.InvokeNative(0x71215ACCFDE075EE, promptGroup, false) -- PROMPT_SET_VISIBLE
            end
        else
            -- Cacher le prompt si trop loin
            Citizen.InvokeNative(0x71215ACCFDE075EE, promptGroup, false) -- PROMPT_SET_VISIBLE
        end
        
        -- Si un braquage est en cours, afficher la barre de progression
        if CurrentRobbery.isRobbing then
            DrawTxt("Braquage en cours: " .. CurrentRobbery.timeRemaining .. "s", 0.5, 0.92, 0.7, 4, 255, 0, 0, 255, 1, true, true)
            
            -- Dessin de la barre de progression
            local maxWidth = 0.2
            local progress = CurrentRobbery.timeRemaining / Config.RobbableLocations[CurrentRobbery.locationId].robberyDuration
            local width = maxWidth * progress
            
            -- Fond de la barre
            DrawRect(0.5, 0.94, maxWidth, 0.02, 100, 100, 100, 150)
            -- Progression
            DrawRect(0.5 - ((maxWidth - width) / 2), 0.94, width, 0.02, 200, 0, 0, 150)
        end
    end
end)

-- Fonction pour dessiner un rectangle à l'écran
function DrawRect(x, y, width, height, r, g, b, a)
    Citizen.InvokeNative(0x3A618A217E5154F0, x, y, width, height, r, g, b, a) -- DRAW_RECT
end

-- Gérer la progression du braquage
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        
        if CurrentRobbery.isRobbing then
            CurrentRobbery.timeRemaining = CurrentRobbery.timeRemaining - 1
            
            if CurrentRobbery.timeRemaining <= 0 then
                -- Braquage terminé
                TriggerServerEvent('sheriffs_vs_outlaws:completeRobbery', CurrentRobbery.locationId)
                CurrentRobbery.isRobbing = false
            end
        end
    end
end)

-- Événement pour démarrer un braquage
RegisterNetEvent('sheriffs_vs_outlaws:startRobbery')
AddEventHandler('sheriffs_vs_outlaws:startRobbery', function(locationId, duration)
    CurrentRobbery.isRobbing = true
    CurrentRobbery.locationId = locationId
    CurrentRobbery.startTime = GetGameTimer()
    CurrentRobbery.timeRemaining = duration
    
    -- Commenté temporairement pour tester
    -- -- Animation de braquage (option)
    -- TaskStartScenarioInPlace(PlayerPedId(), GetHashKey("WORLD_HUMAN_INSPECT"), -1, true, false, false, false)
end)

-- Événement pour être notifié d'un braquage en cours (pour les shérifs)
RegisterNetEvent('sheriffs_vs_outlaws:robberyAlert')
AddEventHandler('sheriffs_vs_outlaws:robberyAlert', function(locationName, coords)
    if LocalPlayer.faction == "sheriffs" then
        -- Notification pour les forces de l'ordre
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"ALERTE", "Un braquage est en cours à " .. locationName .. "!"}
        })
        
        -- Placer un blip temporaire sur la carte à l'emplacement du braquage
        local blip = Citizen.InvokeNative(0x554D9D53F696D002, 1664425300, coords.x, coords.y, coords.z) -- BLIP_ADD_FOR_COORD
        Citizen.InvokeNative(0x74F74D3207ED525C, blip, -1103135225, 1) -- SET_BLIP_SPRITE
        Citizen.InvokeNative(0xDF735600A4696DAF, blip, -184692826) -- _SET_BLIP_FLASH_STYLE (rouge clignotant)
        Citizen.InvokeNative(0x9CB1A1623062F402, blip, "Braquage en cours") -- SET_BLIP_NAME_FROM_PLAYER_STRING
        
        -- Supprimer le blip après 60 secondes
        Citizen.SetTimeout(60000, function()
            RemoveBlip(blip)
        end)
    end
end)

-- Événement quand un braquage est annulé
RegisterNetEvent('sheriffs_vs_outlaws:cancelRobbery')
AddEventHandler('sheriffs_vs_outlaws:cancelRobbery', function(reason)
    if CurrentRobbery.isRobbing then
        CurrentRobbery.isRobbing = false
        ClearPedTasks(PlayerPedId()) -- Arrêter l'animation
        
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"BRAQUAGE", "Braquage échoué: " .. reason}
        })
    end
end)

-- Événement quand un braquage est réussi
RegisterNetEvent('sheriffs_vs_outlaws:robberySuccess')
AddEventHandler('sheriffs_vs_outlaws:robberySuccess', function(reward, bountyIncrease)
    ClearPedTasks(PlayerPedId()) -- Arrêter l'animation
    
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = true,
        args = {"BRAQUAGE", "Braquage réussi! Vous avez gagné $" .. reward .. " et votre prime a augmenté de $" .. bountyIncrease}
    })
end) 